package database

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"langchain.com/smith/config"
	"langchain.com/smith/testutil/leak"
)

func TestAuditLogs(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	t.Run("SanitizedContextVars", func(t *testing.T) {
		config.Env.AuditLogsEnabled = true
		ctx := context.Background()
		ctx = context.WithValue(ctx, config.ApiKeyCtxKey, "Abc_... \n123\ndef")
		ctx = context.WithValue(ctx, config.LbTraceCtxKey, "Traceid \nwith \nwhitespace//")
		ctx = context.WithValue(ctx, config.OperationCtxKey, "test_operation")
		prefixedQuery := auditPrefixedQuery(ctx, "SELECT 1")
		assert.Contains(t, prefixedQuery, "-- api_key_short:Abc_...123def")
		assert.Contains(t, prefixedQuery, "-- lb_trace_context:Traceidwithwhitespace//")
		assert.Contains(t, prefixedQuery, "-- audit_operation_name:test_operation")
	})
}
