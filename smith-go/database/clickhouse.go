package database

import (
	"crypto/tls"
	"fmt"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"langchain.com/smith/config"
)

func ChConnect(keepalive bool) (driver.Conn, error) {
	var tlsConfig *tls.Config
	if config.Env.ClickHouseTLS {
		tlsConfig = &tls.Config{}
	}

	conn, err := clickhouse.Open(&clickhouse.Options{
		Addr: []string{fmt.Sprintf("%s:%d", config.Env.ClickHouseHost, config.Env.ClickHousePort)},
		Auth: clickhouse.Auth{
			Database: config.Env.ClickHouseDb,
			Username: config.Env.ClickHouseUser,
			Password: config.Env.ClickHousePassword,
		},
		TLS: tlsConfig,
		ClientInfo: clickhouse.ClientInfo{
			Products: []struct {
				Name    string
				Version string
			}{
				{Name: config.Env.ServiceName, Version: "0.1"},
			},
		},
		// Env timeouts are in seconds, time.Duration is in ns
		DialTimeout:          time.Duration(config.Env.ClickHouseConnectTimeout) * time.Second,
		FreeBufOnConnRelease: !keepalive,
		MaxOpenConns:         config.Env.ClickHouseMaxConnections,
		MaxIdleConns:         config.Env.ClickHouseMaxKeepAliveConnections,
		// Env timeouts are in seconds, time.Duration is in ns
		ReadTimeout: time.Duration(config.Env.ClickHouseReadTimeout) * time.Second,
	})

	if err != nil {
		return nil, err
	}

	return conn, nil
}
