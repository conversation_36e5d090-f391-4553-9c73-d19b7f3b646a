package database

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"langchain.com/smith/config"
)

// Regex for sanitizing context variable values provided by the user in headers
var sanitizedContextVarRegex = regexp.MustCompile(`[^a-zA-Z0-9/._]`)

// auditPrefixedQuery formats a query with metadata retrieved from context.
func auditPrefixedQuery(ctx context.Context, sql string) string {
	if !config.Env.AuditLogsEnabled {
		return sql
	}

	contextInfo := []string{"-- engine:pgx"}

	// Sanitize context variable values provided by the user
	for _, key := range config.Env.SanitizedAuditLogContextVars.SplitList {
		if value, ok := ctx.Value(key).(string); ok && value != "" {
			sanitized := sanitizedContextVarRegex.ReplaceAllString(value, "")
			contextInfo = append(contextInfo, fmt.Sprintf("-- %s:%s", key, sanitized))
		}
	}

	// Fetch additional audit log context variables
	for _, key := range config.Env.AuditLogContextVars.SplitList {
		if value, ok := ctx.Value(key).(string); ok && value != "" {
			contextInfo = append(contextInfo, fmt.Sprintf("-- %s:%s", key, value))
		}
	}

	// Format and return the prefixed query
	return fmt.Sprintf("%s\n%s", strings.Join(contextInfo, "\n"), sql)
}
