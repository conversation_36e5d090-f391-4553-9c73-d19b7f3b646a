package database

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"strings"
	"sync"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	pgxtrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/jackc/pgx.v5"

	"langchain.com/smith/config"
)

type sqlQueryTracer struct {
	log *log.Logger
}

type TestsCaptureTracer struct {
	mu      sync.Mutex
	queries []pgx.TraceQueryStartData
}

func (tracer *TestsCaptureTracer) TraceQueryStart(ctx context.Context, _ *pgx.Conn, data pgx.TraceQueryStartData) context.Context {
	tracer.mu.Lock()
	defer tracer.mu.Unlock()
	tracer.queries = append(tracer.queries, data)
	return ctx
}

func (tracer *TestsCaptureTracer) TraceQueryEnd(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryEndData) {
}

func (tracer *TestsCaptureTracer) Queries() []pgx.TraceQueryStartData {
	return tracer.queries
}

func (tracer *TestsCaptureTracer) Reset() {
	tracer.queries = []pgx.TraceQueryStartData{}
}

var userDefinedDataTypes = []string{
	"citext",
	"_citext",
	"access_scope",
	"_access_scope",
	"auth_provider",
	"_auth_provider",
}

var (
	dataTypeCache sync.Map
)

func (tracer *sqlQueryTracer) TraceQueryStart(ctx context.Context, _ *pgx.Conn, data pgx.TraceQueryStartData) context.Context {
	argsFormatted := ""
	for i, arg := range data.Args {
		argsFormatted += fmt.Sprintf("Arg $%d: %v, ", i+1, arg)
	}
	tracer.log.Printf("args: %s\n sql: %s", argsFormatted, data.SQL)
	return ctx
}

func (tracer *sqlQueryTracer) TraceQueryEnd(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryEndData) {
}

// TODO: use this or delete: https://linear.app/langchain/issue/INF-275/register-dynamic-types-correctly-in-go
func afterConnect(ctx context.Context, conn *pgx.Conn) error {
	// Register our user-defined types.
	// https://pkg.go.dev/github.com/thoohv5/pgx/pgtype#hdr-New_PostgreSQL_Type_Support
	typeMap := conn.TypeMap()

	for _, typeName := range userDefinedDataTypes {
		if _, ok := typeMap.TypeForName(typeName); ok {
			continue
		}

		if cachedType, ok := dataTypeCache.Load(typeName); ok {
			conn.TypeMap().RegisterDefaultPgType(cachedType.(*pgtype.Type), typeName)
		} else {
			dataType, err := conn.LoadType(ctx, typeName)
			if err != nil {
				log.Printf("Error loading user-defined type %s. %v", typeName, err)
				return nil
			}
			conn.TypeMap().RegisterDefaultPgType(dataType, typeName)

			dataTypeCache.Store(typeName, dataType)
		}
	}

	return nil
}

// AuditLoggedPool wraps pgxpool.Pool to prefix queries with additional context
// as SQL comments if audit logging is enabled.
type AuditLoggedPool struct {
	*pgxpool.Pool
	Tracer pgx.QueryTracer
}

func (p *AuditLoggedPool) Acquire(ctx context.Context) (*AuditLoggedConnection, error) {
	conn, err := p.Pool.Acquire(ctx)
	if err != nil {
		return nil, err
	}
	return &AuditLoggedConnection{Conn: conn}, nil
}

func (p *AuditLoggedPool) BeginTx(ctx context.Context, txOptions pgx.TxOptions) (pgx.Tx, error) {
	tx, err := p.Pool.BeginTx(ctx, txOptions)
	if err != nil {
		return nil, err
	}
	return AuditLoggedTx{Tx: tx}, nil
}

func (p *AuditLoggedPool) Begin(ctx context.Context) (pgx.Tx, error) {
	return p.BeginTx(ctx, pgx.TxOptions{})
}

// AuditLoggedTx similarly wraps and implements pgx.Tx
type AuditLoggedTx struct {
	Tx pgx.Tx
}

func (t AuditLoggedTx) Begin(ctx context.Context) (pgx.Tx, error) {
	tx, err := t.Tx.Begin(ctx)
	if err != nil {
		return tx, err
	}
	return AuditLoggedTx{Tx: tx}, nil
}

func (t AuditLoggedTx) Commit(ctx context.Context) error {
	return t.Tx.Commit(ctx)
}

func (t AuditLoggedTx) Rollback(ctx context.Context) error {
	return t.Tx.Rollback(ctx)
}

func (t AuditLoggedTx) CopyFrom(ctx context.Context, tableName pgx.Identifier, columnNames []string, rowSrc pgx.CopyFromSource) (int64, error) {
	// CopyFrom doesn't use inputted sql, so we need to audit the query manually
	res, err := t.Tx.CopyFrom(ctx, tableName, columnNames, rowSrc)
	if err != nil {
		return res, err
	}
	_, err = t.Exec(ctx, "SELECT 1;")
	if err != nil {
		return 0, err
	}
	return res, nil
}

func (t AuditLoggedTx) SendBatch(ctx context.Context, b *pgx.Batch) pgx.BatchResults {
	return t.Tx.SendBatch(ctx, b)
}

func (t AuditLoggedTx) LargeObjects() pgx.LargeObjects {
	return t.Tx.LargeObjects()
}

func (t AuditLoggedTx) Prepare(ctx context.Context, name, sql string) (*pgconn.StatementDescription, error) {
	return t.Tx.Prepare(ctx, name, sql)
}

func (t AuditLoggedTx) Exec(ctx context.Context, sql string, arguments ...any) (pgconn.CommandTag, error) {
	sql = auditPrefixedQuery(ctx, sql)
	return t.Tx.Exec(ctx, sql, arguments...)
}

func (t AuditLoggedTx) Query(ctx context.Context, sql string, args ...any) (pgx.Rows, error) {
	sql = auditPrefixedQuery(ctx, sql)
	return t.Tx.Query(ctx, sql, args...)
}

func (t AuditLoggedTx) QueryRow(ctx context.Context, sql string, args ...any) pgx.Row {
	sql = auditPrefixedQuery(ctx, sql)
	return t.Tx.QueryRow(ctx, sql, args...)
}

func (t AuditLoggedTx) Conn() *pgx.Conn {
	return t.Tx.Conn()
}

type AuditLoggedConnection struct {
	Conn *pgxpool.Conn
}

func (c *AuditLoggedConnection) Query(ctx context.Context, sql string, args ...any) (pgx.Rows, error) {
	sql = auditPrefixedQuery(ctx, sql)
	return c.Conn.Query(ctx, sql, args...)
}

func (c *AuditLoggedConnection) QueryRow(ctx context.Context, sql string, args ...any) pgx.Row {
	sql = auditPrefixedQuery(ctx, sql)
	return c.Conn.QueryRow(ctx, sql, args...)
}

func (c *AuditLoggedConnection) Exec(ctx context.Context, sql string, args ...any) (pgconn.CommandTag, error) {
	sql = auditPrefixedQuery(ctx, sql)
	return c.Conn.Exec(ctx, sql, args...)
}

// PgConnect initializes a connection pool and wraps it
func PgConnect() *AuditLoggedPool {
	var (
		dbpool *pgxpool.Pool
		err    error
	)
	connString := fmt.Sprintf("postgresql://%s", config.Env.PgDatabaseURI)
	pgBouncerEnabled := config.Env.PgbouncerDatabaseURI != ""

	if pgBouncerEnabled {
		connString = fmt.Sprintf("postgresql://%s", config.Env.PgbouncerDatabaseURI)
	}

	poolConfig, err := pgxpool.ParseConfig(connString)
	if err != nil {
		slog.Error("Error parsing connection string", "err", err)
		panic(err)
	}

	if pgBouncerEnabled {
		// This is the recommended mode for PGBouncer
		// https://github.com/jackc/pgx/blob/master/pgbouncer_test.go
		// https://github.com/jackc/pgx/discussions/1784
		poolConfig.ConnConfig.DefaultQueryExecMode = pgx.QueryExecModeCacheDescribe
	} else {
		poolConfig.ConnConfig.RuntimeParams["search_path"] = config.Env.PgSchema
	}
	poolConfig.MaxConns = int32(config.Env.AsyncpgPoolMaxSize)
	poolConfig.ConnConfig.ConnectTimeout = time.Duration(config.Env.AsyncPgPoolTimeoutSec) * time.Second
	if strings.ToLower(config.Env.DbLogLevel) == "debug" {
		poolConfig.ConnConfig.Tracer = &sqlQueryTracer{log: log.New(log.Writer(), "pgx: ", log.LstdFlags|log.LUTC|log.Lmsgprefix|log.Lmicroseconds)}
	}
	if strings.ToLower(config.Env.DbLogLevel) == "tests-capture" {
		poolConfig.ConnConfig.Tracer = &TestsCaptureTracer{}
	}
	poolConfig.MaxConns = int32(config.Env.AsyncpgPoolMaxSize)
	poolConfig.ConnConfig.ConnectTimeout = time.Duration(config.Env.AsyncPgPoolTimeoutSec) * time.Second

	if config.Env.DatadogEnabled {
		dbpool, err = pgxtrace.NewPoolWithConfig(context.Background(), poolConfig, pgxtrace.WithServiceName("postgres"))
	} else {
		dbpool, err = pgxpool.NewWithConfig(context.Background(), poolConfig)
	}

	if err != nil {
		slog.Error("Error connecting to database", "err", err)
		panic(err)
	}

	return &AuditLoggedPool{Pool: dbpool, Tracer: poolConfig.ConnConfig.Tracer}
}
