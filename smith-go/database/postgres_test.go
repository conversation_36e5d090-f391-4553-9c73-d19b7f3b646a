package database

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"langchain.com/smith/testutil/leak"
)

func TestConnect(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	db := PgConnect()
	defer db.Close()

	t.Run("Query", func(t *testing.T) {
		rows, err := db.Query(context.Background(), "SELECT 1")
		assert.NoError(t, err)
		defer rows.Close()
		assert.NotNil(t, rows)
	})

	t.Run("QueryRow", func(t *testing.T) {
		row := db.QueryRow(context.Background(), "SELECT 1")
		assert.NotNil(t, row)
		var val int
		err := row.<PERSON>an(&val)
		assert.NoError(t, err)
	})

	t.Run("Exec", func(t *testing.T) {
		_, err := db.Exec(context.Background(), "SELECT 1")
		assert.NoError(t, err)
	})

}
