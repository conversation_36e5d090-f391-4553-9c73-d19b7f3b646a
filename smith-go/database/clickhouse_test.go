package database_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/database"
	"langchain.com/smith/testutil/leak"
)

func TestClickHouseConnect(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	conn, err := database.ChConnect(false)
	require.NoError(t, err)

	ctx := context.Background()
	if err := conn.Ping(ctx); err != nil {
		if exception, ok := err.(*clickhouse.Exception); ok {
			fmt.Printf("Exception [%d] %s \n%s\n", exception.Code, exception.Message, exception.StackTrace)
		}
		require.NoError(t, err)
	}

	defer func() {
		err = conn.Close()
		require.NoError(t, err)
	}()
}
