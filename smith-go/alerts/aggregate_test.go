package alerts_test

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"github.com/neilotoole/slogt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/goleak"
	"langchain.com/smith/alerts"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
)

func testLogger(t *testing.T) func(next http.Handler) http.Handler {
	opt := httplog.Options{LogLevel: slog.Level(config.Env.SlogLevel)}
	log := &httplog.Logger{Logger: slogt.New(t), Options: opt}
	return httplog.RequestLogger(log)
}

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM tracer_session; DELETE FROM organizations; DELETE FROM users; DELETE FROM alert_rules; DELETE FROM alert_actions;")
	assert.NoError(t, err)
}

func TestAlertsHandlerErrorCount(t *testing.T) {
	defer goleak.VerifyNone(t, goleak.IgnoreAnyFunction("github.com/cihub/seelog.(*asyncLoopLogger).processItem"))
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	alertAggregationHandler := alerts.NewAlertsAggregationHandler(dbpool, *routedRedisPools, cachingRedisPool)
	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	now := time.Now().UTC()
	alertRuleId := uuid.New().String()
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionID := testutil.TracerSessionSetup(t, dbpool, tenantID)

	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

	alertRule := alerts.AlertRule{
		AlertRuleBase: alerts.AlertRuleBase{
			ID:            alertRuleId,
			Name:          "Alert Rule 1",
			Description:   "Error count threshold alert",
			Type:          "threshold",
			Attribute:     "error_count",
			Aggregation:   "sum",
			Threshold:     &[]float64{10.0}[0],
			Operator:      "gte",
			WindowMinutes: 15,
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	alertRuleActions := []alerts.AlertAction{
		{
			AlertActionBase: alerts.AlertActionBase{
				ID:          uuid.New().String(),
				AlertRuleID: alertRule.ID,
				Target:      "webhook",
				Config:      json.RawMessage(`{"url": "https://example.com/webhook"}`),
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
	}
	if err := alertRulesCrudHandler.CreateAlertRule(ctx, tenantID, sessionID, &alertRule, &alertRuleActions); err != nil {
		t.Fatalf("failed to create alert rule: %v", err)
	}

	// set up vars to check if alert was triggered
	triggeredWebhookAlert := false
	triggeredAlertRuleId := ""
	triggeredMinuteKey := ""
	triggeredValue := 0.0
	triggeredThresh := 0.0
	alertAggregationHandler.FireWebhookActionFn = func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule alerts.AlertRule,
		action alerts.AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error {
		triggeredWebhookAlert = true
		triggeredAlertRuleId = alertRule.ID
		triggeredMinuteKey = triggeredTime.Format("2006-01-02-15-04")
		triggeredValue = triggeredMetricValue
		triggeredThresh = triggeredThreshold
		return nil
	}

	t.Run("test alert does not fire when not enough metrics", func(t *testing.T) {
		minuteKey := "2025-01-01-00-00"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{1, 1, 1, 1, 1, 1}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}
		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 6.0, minuteAggregationMap["sum"])
		require.Equal(t, 6.0, minuteAggregationMap["count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was not triggered (since threshold is 10 and we only have 6)
		assert.False(t, triggeredWebhookAlert)
		assert.Equal(t, "", triggeredAlertRuleId)
		assert.Equal(t, "", triggeredMinuteKey)
		assert.Equal(t, 0.0, triggeredValue)
		assert.Equal(t, 0.0, triggeredThresh)
	})

	t.Run("test alert fires when enough metrics", func(t *testing.T) {
		minuteKey := "2025-01-01-00-01"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

		// add metrics to alert key
		values := []float64{1, 1, 1, 1}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 4.0, minuteAggregationMap["sum"])
		require.Equal(t, 4.0, minuteAggregationMap["count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was triggered (since threshold is 10 and we have 10)
		assert.True(t, triggeredWebhookAlert)
		assert.Equal(t, alertRuleId, triggeredAlertRuleId)
		assert.Equal(t, minuteKey, triggeredMinuteKey)
		assert.Equal(t, 10.0, triggeredValue)
		assert.Equal(t, 10.0, triggeredThresh)

		// unset the triggered flag
		triggeredWebhookAlert = false
		triggeredAlertRuleId = ""
		triggeredMinuteKey = ""
		triggeredValue = 0.0
		triggeredThresh = 0.0
	})

	// fast forward 15 minutes and errors are outside window, check if alert is triggered
	t.Run("test alert does not fire when errors are outside window", func(t *testing.T) {
		minuteKey := "2025-01-01-00-16"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

		// add metrics to alert key
		values := []float64{1, 1}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 2.0, minuteAggregationMap["sum"])
		require.Equal(t, 2.0, minuteAggregationMap["count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was not triggered (since threshold is 10 and we only have 1)
		assert.False(t, triggeredWebhookAlert)
		assert.Equal(t, "", triggeredAlertRuleId)
		assert.Equal(t, "", triggeredMinuteKey)
		assert.Equal(t, 0.0, triggeredValue)
		assert.Equal(t, 0.0, triggeredThresh)
	})
}

func TestAlertsHandlerErrorPercentage(t *testing.T) {
	defer goleak.VerifyNone(t, goleak.IgnoreAnyFunction("github.com/cihub/seelog.(*asyncLoopLogger).processItem"))
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()
	alertAggregationHandler := alerts.NewAlertsAggregationHandler(dbpool, *routedRedisPools, cachingRedisPool)
	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	now := time.Now().UTC()
	alertRuleId := uuid.New().String()
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionID := testutil.TracerSessionSetup(t, dbpool, tenantID)

	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

	alertRule := alerts.AlertRule{
		AlertRuleBase: alerts.AlertRuleBase{
			ID:            alertRuleId,
			Name:          "Alert Rule 1",
			Description:   "Error percentage threshold alert",
			Type:          "threshold",
			Attribute:     "error_percentage",
			Aggregation:   "pct",
			Threshold:     &[]float64{30}[0],
			Operator:      "gte",
			WindowMinutes: 5,
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	alertRuleActions := []alerts.AlertAction{
		{
			AlertActionBase: alerts.AlertActionBase{
				ID:          uuid.New().String(),
				AlertRuleID: alertRule.ID,
				Target:      "pagerduty",
				Config:      json.RawMessage(`{"integration_key": "test-integration-key", "severity": "error"}`),
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
	}

	if err := alertRulesCrudHandler.CreateAlertRule(ctx, tenantID, sessionID, &alertRule, &alertRuleActions); err != nil {
		t.Fatalf("failed to create alert rule: %v", err)
	}

	// set up vars to check if alert was triggered
	triggeredPagerdutyAlert := false
	triggeredAlertRuleId := ""
	triggeredMinuteKey := ""
	triggeredValue := 0.0
	triggeredThresh := 0.0
	alertAggregationHandler.FirePagerDutyActionFn = func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule alerts.AlertRule,
		action alerts.AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error {
		triggeredPagerdutyAlert = true
		triggeredAlertRuleId = alertRule.ID
		triggeredMinuteKey = triggeredTime.Format("2006-01-02-15-04")
		triggeredValue = triggeredMetricValue
		triggeredThresh = triggeredThreshold
		return nil
	}

	t.Run("test alert does not fire when not enough metrics", func(t *testing.T) {
		minuteKey := "2025-01-01-00-00"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		denominatorKey := fmt.Sprintf("smith:alerts:metrics:%s:%s:denominator", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{1, 0, 0, 0, 0, 0, 0, 0, 0, 1}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		denominatorValues := []float64{1, 1, 1, 1, 1, 1, 1, 1, 1, 1}
		for _, value := range denominatorValues {
			routedRedisClient.RPush(context.Background(), denominatorKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 2.0, minuteAggregationMap["sum"])
		require.Equal(t, 10.0, minuteAggregationMap["count"])
		require.Equal(t, 10.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 10.0, minuteAggregationMap["denominator_sum"])

		// check if alert was not triggered (since threshold is 0.3 and we only have 0.2)
		assert.False(t, triggeredPagerdutyAlert)
		assert.Equal(t, "", triggeredAlertRuleId)
		assert.Equal(t, "", triggeredMinuteKey)
		assert.Equal(t, 0.0, triggeredValue)
		assert.Equal(t, 0.0, triggeredThresh)
	})

	t.Run("test alert fires when enough metrics", func(t *testing.T) {
		minuteKey := "2025-01-01-00-04"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		denominatorKey := fmt.Sprintf("smith:alerts:metrics:%s:%s:denominator", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{1, 1}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		denominatorValues := []float64{1, 1}
		for _, value := range denominatorValues {
			routedRedisClient.RPush(context.Background(), denominatorKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 2.0, minuteAggregationMap["sum"])
		require.Equal(t, 2.0, minuteAggregationMap["count"])
		require.Equal(t, 2.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 2.0, minuteAggregationMap["denominator_sum"])

		// check if alert was triggered (since threshold is 0.3 and we have 4.0 / 12.0 = 0.3333)
		assert.True(t, triggeredPagerdutyAlert)
		assert.Equal(t, alertRuleId, triggeredAlertRuleId)
		assert.Equal(t, minuteKey, triggeredMinuteKey)
		require.InDelta(t, (4.0/12.0)*100, triggeredValue, 0.000001)
		assert.Equal(t, 30.0, triggeredThresh)

		// unset the triggered flag
		triggeredPagerdutyAlert = false
		triggeredAlertRuleId = ""
		triggeredMinuteKey = ""
		triggeredValue = 0.0
		triggeredThresh = 0.0
	})

	t.Run("test alert does not fire when errors are outside window", func(t *testing.T) {
		minuteKey := "2025-01-01-00-08"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		denominatorKey := fmt.Sprintf("smith:alerts:metrics:%s:%s:denominator", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		denominatorValues := []float64{1, 1, 1, 1, 1, 1, 1, 1, 1, 1}
		for _, value := range denominatorValues {
			routedRedisClient.RPush(context.Background(), denominatorKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 0.0, minuteAggregationMap["sum"])
		require.Equal(t, 10.0, minuteAggregationMap["count"])
		require.Equal(t, 10.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 10.0, minuteAggregationMap["denominator_sum"])

		// check if alert was not triggered (since threshold is 0.3 and we only have 2/12 = 0.1666)
		assert.False(t, triggeredPagerdutyAlert)
		assert.Equal(t, "", triggeredAlertRuleId)
		assert.Equal(t, "", triggeredMinuteKey)
		assert.Equal(t, 0.0, triggeredValue)
		assert.Equal(t, 0.0, triggeredThresh)
	})
}

func TestAlertsHandlerLatency(t *testing.T) {
	defer goleak.VerifyNone(t, goleak.IgnoreAnyFunction("github.com/cihub/seelog.(*asyncLoopLogger).processItem"))
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)
	ctx := context.Background()

	alertAggregationHandler := alerts.NewAlertsAggregationHandler(dbpool, *routedRedisPools, cachingRedisPool)
	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	now := time.Now().UTC()
	alertRuleId := uuid.New().String()
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionID := testutil.TracerSessionSetup(t, dbpool, tenantID)

	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

	alertRule := alerts.AlertRule{
		AlertRuleBase: alerts.AlertRuleBase{
			ID:            alertRuleId,
			Name:          "Alert Rule 1",
			Description:   "Latency threshold alert",
			Type:          "threshold",
			Attribute:     "latency",
			Aggregation:   "avg",
			Threshold:     &[]float64{3}[0],
			Operator:      "gte",
			WindowMinutes: 5,
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	alertRuleActions := []alerts.AlertAction{
		{
			AlertActionBase: alerts.AlertActionBase{
				ID:          uuid.New().String(),
				AlertRuleID: alertRule.ID,
				Target:      "pagerduty",
				Config:      json.RawMessage(`{"integration_key": "test-integration-key", "severity": "error"}`),
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
	}

	if err := alertRulesCrudHandler.CreateAlertRule(ctx, tenantID, sessionID, &alertRule, &alertRuleActions); err != nil {
		t.Fatalf("failed to create alert rule: %v", err)
	}

	// set up vars to check if alert was triggered
	triggeredPagerdutyAlert := false
	triggeredAlertRuleId := ""
	triggeredMinuteKey := ""
	triggeredValue := 0.0
	triggeredThresh := 0.0
	alertAggregationHandler.FirePagerDutyActionFn = func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule alerts.AlertRule,
		action alerts.AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error {
		triggeredPagerdutyAlert = true
		triggeredAlertRuleId = alertRule.ID
		triggeredMinuteKey = triggeredTime.Format("2006-01-02-15-04")
		triggeredValue = triggeredMetricValue
		triggeredThresh = triggeredThreshold
		return nil
	}

	t.Run("test alert does not fire when not enough metrics", func(t *testing.T) {
		minuteKey := "2025-01-01-00-00"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{1.2, 0.5, 1.3, 1.15, 0.9}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 5.0, minuteAggregationMap["count"])
		require.InDelta(t, 5.05, minuteAggregationMap["sum"], 0.00001)
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was not triggered (since threshold is 3 and we only have 1.01)
		assert.False(t, triggeredPagerdutyAlert)
		assert.Equal(t, "", triggeredAlertRuleId)
		assert.Equal(t, "", triggeredMinuteKey)
		assert.Equal(t, 0.0, triggeredValue)
		assert.Equal(t, 0.0, triggeredThresh)
	})

	t.Run("test alert fires when average latency is greater than threshold", func(t *testing.T) {
		minuteKey := "2025-01-01-00-04"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{5.1, 3.5, 7.21, 6.25}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		// assert all the metrics were accounted for
		require.Equal(t, 4.0, minuteAggregationMap["count"])
		require.InDelta(t, 22.06, minuteAggregationMap["sum"], 0.00001)
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was triggered (since threshold is 3 and we have 3.012)
		averageValue := ((22.06 + 5.05) / (4 + 5))
		assert.True(t, triggeredPagerdutyAlert)
		assert.Equal(t, alertRuleId, triggeredAlertRuleId)
		assert.Equal(t, minuteKey, triggeredMinuteKey)
		assert.InDelta(t, averageValue, triggeredValue, 0.00001)
		assert.Equal(t, 3.0, triggeredThresh)
	})
}

func TestAlertsFeedbackScore(t *testing.T) {
	defer goleak.VerifyNone(t, goleak.IgnoreAnyFunction("github.com/cihub/seelog.(*asyncLoopLogger).processItem"))
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	alertAggregationHandler := alerts.NewAlertsAggregationHandler(dbpool, *routedRedisPools, cachingRedisPool)
	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	now := time.Now().UTC()
	alertRuleId := uuid.New().String()
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionID := testutil.TracerSessionSetup(t, dbpool, tenantID)

	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

	alertRule := alerts.AlertRule{
		AlertRuleBase: alerts.AlertRuleBase{
			ID:            alertRuleId,
			Name:          "Alert Rule 1",
			Description:   "Feedback score threshold alert",
			Type:          "threshold",
			Attribute:     "feedback_score",
			Aggregation:   "avg",
			Threshold:     &[]float64{6}[0],
			Operator:      "lte",
			WindowMinutes: 5,
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	alertRuleActions := []alerts.AlertAction{
		{
			AlertActionBase: alerts.AlertActionBase{
				ID:          uuid.New().String(),
				AlertRuleID: alertRule.ID,
				Target:      "webhook",
				Config:      json.RawMessage(`{"url": "https://example.com/webhook"}`),
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
	}

	if err := alertRulesCrudHandler.CreateAlertRule(ctx, tenantID, sessionID, &alertRule, &alertRuleActions); err != nil {
		t.Fatalf("failed to create alert rule: %v", err)
	}

	// set up vars to check if alert was triggered
	triggeredWebhookAlert := false
	triggeredAlertRuleId := ""
	triggeredMinuteKey := ""
	triggeredValue := 0.0
	triggeredThresh := 0.0
	alertAggregationHandler.FireWebhookActionFn = func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule alerts.AlertRule,
		action alerts.AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error {
		triggeredWebhookAlert = true
		triggeredAlertRuleId = alertRule.ID
		triggeredMinuteKey = triggeredTime.Format("2006-01-02-15-04")
		triggeredValue = triggeredMetricValue
		triggeredThresh = triggeredThreshold
		return nil
	}

	t.Run("test alert does not fire when not enough metrics", func(t *testing.T) {
		minuteKey := "2025-01-01-00-00"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{8.5, 7.5, 9.6, 8.2, 7.9}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		sum := 8.5 + 7.5 + 9.6 + 8.2 + 7.9
		// assert all the metrics were accounted for
		require.Equal(t, 5.0, minuteAggregationMap["count"])
		require.InDelta(t, sum, minuteAggregationMap["sum"], 0.00001)
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was not triggered (since threshold is 6 and we only have 8.34)
		assert.False(t, triggeredWebhookAlert)
		assert.Equal(t, "", triggeredAlertRuleId)
		assert.Equal(t, "", triggeredMinuteKey)
		assert.Equal(t, 0.0, triggeredValue)
		assert.Equal(t, 0.0, triggeredThresh)
	})

	t.Run("test alert fires when average feedback score is less than threshold", func(t *testing.T) {
		minuteKey := "2025-01-01-00-03"
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)

		// add metrics to alert key
		values := []float64{3.0, 3.0, 1.2, 2.0, 1.5}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		// send aggregation request
		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		sum := 3.0 + 3.0 + 1.2 + 2.0 + 1.5
		// assert all the metrics were accounted for
		require.Equal(t, 5.0, minuteAggregationMap["count"])
		require.InDelta(t, sum, minuteAggregationMap["sum"], 0.000001)
		require.Equal(t, 0.0, minuteAggregationMap["denominator_count"])
		require.Equal(t, 0.0, minuteAggregationMap["denominator_sum"])

		// check if alert was triggered (since threshold is 6 and we have 5.24)
		assert.True(t, triggeredWebhookAlert)
		assert.Equal(t, alertRuleId, triggeredAlertRuleId)
		assert.Equal(t, minuteKey, triggeredMinuteKey)
		assert.InDelta(t, 5.24, triggeredValue, 0.000001)
		assert.Equal(t, 6.0, triggeredThresh)
	})
}

func TestAlertsErrorPercentChange(t *testing.T) {
	defer goleak.VerifyNone(t, goleak.IgnoreAnyFunction("github.com/cihub/seelog.(*asyncLoopLogger).processItem"))
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()
	alertAggregationHandler := alerts.NewAlertsAggregationHandler(dbpool, *routedRedisPools, cachingRedisPool)
	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	now := time.Now().UTC()
	alertRuleId := uuid.New().String()
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionID := testutil.TracerSessionSetup(t, dbpool, tenantID)

	routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationAlerts)

	alertRule := alerts.AlertRule{
		AlertRuleBase: alerts.AlertRuleBase{
			ID:                     alertRuleId,
			Name:                   "Alert Rule 1",
			Description:            "Error percent change threshold alert",
			Type:                   "change",
			Attribute:              "error_count",
			Aggregation:            "pct",
			ThresholdWindowMinutes: &[]int{30}[0],
			ThresholdMultiplier:    &[]float64{1.3}[0],
			Operator:               "gte",
			WindowMinutes:          15,
		},
		CreatedAt: now,
		UpdatedAt: now,
	}

	alertRuleActions := []alerts.AlertAction{
		{
			AlertActionBase: alerts.AlertActionBase{
				ID:          uuid.New().String(),
				AlertRuleID: alertRule.ID,
				Target:      "webhook",
				Config:      json.RawMessage(`{"url": "https://example.com/webhook"}`),
			},
			CreatedAt: now,
			UpdatedAt: now,
		},
	}

	if err := alertRulesCrudHandler.CreateAlertRule(ctx, tenantID, sessionID, &alertRule, &alertRuleActions); err != nil {
		t.Fatalf("failed to create alert rule: %v", err)
	}

	// set up vars to check if alert was triggered
	triggeredWebhookAlert := false
	triggeredAlertRuleId := ""
	triggeredMinuteKey := ""
	triggeredValue := 0.0
	triggeredThresh := 0.0
	alertAggregationHandler.FireWebhookActionFn = func(
		ctx context.Context,
		tenantID string,
		sessionID string,
		alertRule alerts.AlertRule,
		action alerts.AlertAction,
		triggeredMetricValue float64,
		triggeredThreshold float64,
		triggeredTime time.Time,
	) error {
		triggeredWebhookAlert = true
		triggeredAlertRuleId = alertRule.ID
		triggeredMinuteKey = triggeredTime.Format("2006-01-02-15-04")
		triggeredValue = triggeredMetricValue
		triggeredThresh = triggeredThreshold
		return nil
	}

	t.Run("test alert does not fire when not enough metrics", func(t *testing.T) {
		for i := 0; i <= 58; i++ {
			minuteKey := fmt.Sprintf("2025-01-01-00-%02d", i)
			metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
			denominatorKey := fmt.Sprintf("smith:alerts:metrics:%s:%s:denominator", alertRuleId, minuteKey)

			// add metrics to alert key
			values := []float64{1.0, 0.0, 0.0, 0.0, 0.0, 0.0}
			for _, value := range values {
				routedRedisClient.RPush(context.Background(), metricsKey, value)
			}

			denominatorValues := []float64{1.0, 1.0, 1.0, 1.0, 1.0, 1.0}
			for _, denominatorValue := range denominatorValues {
				routedRedisClient.RPush(context.Background(), denominatorKey, denominatorValue)
			}

			b := strings.NewReader(fmt.Sprintf(`{ "session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
			w := httptest.NewRecorder()
			r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
			defer r.Body.Close()
			r.Header.Set("Content-Type", "application/json")
			r.Header.Set("X-Tenant-Id", tenantID)
			testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

			// check the state keys
			stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)
			result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
			if err != nil {
				t.Fatalf("failed to get state key: %v", err)
			}

			minuteAggregationMap := map[string]float64{}
			for k, v := range result {
				floatVal, _ := strconv.ParseFloat(v, 64)
				minuteAggregationMap[k] = floatVal
			}

			// assert all the metrics were accounted for
			require.Equal(t, 6.0, minuteAggregationMap["count"])
			require.InDelta(t, 1.0, minuteAggregationMap["sum"], 0.000001)
			require.Equal(t, 6.0, minuteAggregationMap["denominator_count"])
			require.InDelta(t, 6.0, minuteAggregationMap["denominator_sum"], 0.000001)

		}

		minuteKey := fmt.Sprintf("2025-01-01-00-%02d", 59)
		metricsKey := fmt.Sprintf("smith:alerts:metrics:%s:%s", alertRuleId, minuteKey)
		denominatorKey := fmt.Sprintf("smith:alerts:metrics:%s:%s:denominator", alertRuleId, minuteKey)

		// add metrics to alert key make (14+6)/90 > 1.3 x average 15/90
		values := []float64{1.0, 1.0, 1.0, 1.0, 1.0, 1.0}
		for _, value := range values {
			routedRedisClient.RPush(context.Background(), metricsKey, value)
		}

		denominatorValues := []float64{1.0, 1.0, 1.0, 1.0, 1.0, 1.0}
		for _, denominatorValue := range denominatorValues {
			routedRedisClient.RPush(context.Background(), denominatorKey, denominatorValue)
		}

		b := strings.NewReader(fmt.Sprintf(`{"session_id": "%s", "alert_rule_id": "%s", "minute_key": "%s"}`, sessionID, alertRuleId, minuteKey))
		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/internal/aggregate-alerts", b)
		defer r.Body.Close()
		r.Header.Set("Content-Type", "application/json")
		r.Header.Set("X-Tenant-Id", tenantID)
		testLogger(t)(http.HandlerFunc(alertAggregationHandler.AggregatedAlerts)).ServeHTTP(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())
		require.Contains(t, w.Body.String(), "Alerts aggregated successfully")

		// check the state keys
		stateKey := fmt.Sprintf("smith:alerts:state:%s:%s", alertRuleId, minuteKey)
		result, err := routedRedisClient.HGetAll(context.Background(), stateKey).Result()
		if err != nil {
			t.Fatalf("failed to get state key: %v", err)
		}

		minuteAggregationMap := map[string]float64{}
		for k, v := range result {
			floatVal, _ := strconv.ParseFloat(v, 64)
			minuteAggregationMap[k] = floatVal
		}

		sum := 1.0 + 1.0 + 1.0 + 1.0 + 1.0 + 1.0
		// assert all the metrics were accounted for
		require.Equal(t, 6.0, minuteAggregationMap["count"])
		require.InDelta(t, sum, minuteAggregationMap["sum"], 0.000001)
		require.Equal(t, 6.0, minuteAggregationMap["denominator_count"])
		require.InDelta(t, 6.0, minuteAggregationMap["denominator_sum"], 0.000001)

		assert.True(t, triggeredWebhookAlert)
		assert.Equal(t, alertRuleId, triggeredAlertRuleId)
		assert.Equal(t, minuteKey, triggeredMinuteKey)
		assert.Equal(t, (20.0/90.0)*100, triggeredValue)
		require.InDelta(t, 1.3*(15.0/90.0)*100, triggeredThresh, 0.000001)

	})
}
