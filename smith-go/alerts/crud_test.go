package alerts_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/alerts"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
)

func SetupRequestWithParams(t *testing.T, authInfo *auth.AuthInfo, method string, endpoint string, body *strings.Reader, sessionID *string, alertRuleID *string) *http.Request {
	rctx := chi.NewRouteContext()
	if sessionID != nil {
		rctx.URLParams.Add("session_id", *sessionID)
	}
	if alertRuleID != nil {
		rctx.URLParams.Add("alert_rule_id", *alertRuleID)
	}

	r := httptest.NewRequest(method, endpoint, body)

	ctx := r.Context()
	ctx = context.WithValue(ctx, chi.RouteCtxKey, rctx)
	ctx = context.WithValue(ctx, auth.AuthCtxKey, authInfo)
	r = r.WithContext(ctx)

	return r
}

func TestCreateAlertRule(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	t.Run("create alert rule success and verify", func(t *testing.T) {

		orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
		tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
		sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\":\"{\\\"Content-Type\\\": \\\"application/json\\\"}\", \"body\": \"{}\"}"
				}
			]
		}`)

		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		createdAlertRule := alerts.AlertRuleResponse{}
		err := json.Unmarshal(w.Body.Bytes(), &createdAlertRule)
		require.NoError(t, err)

		// check that the alert rule was created in the database
		var tenantIDDB string
		var sessionIDDB string
		var alertRule alerts.AlertRule
		err = dbpool.QueryRow(
			context.Background(),
			"SELECT * FROM alert_rules WHERE id = $1",
			createdAlertRule.Rule.ID,
		).Scan(
			&alertRule.ID,
			&alertRule.Name,
			&alertRule.Description,
			&sessionIDDB,
			&tenantIDDB,
			&alertRule.Type,
			&alertRule.Attribute,
			&alertRule.Aggregation,
			&alertRule.WindowMinutes,
			&alertRule.Threshold,
			&alertRule.ThresholdWindowMinutes,
			&alertRule.ThresholdMultiplier,
			&alertRule.Filter,
			&alertRule.DenominatorFilter,
			&alertRule.CreatedAt,
			&alertRule.UpdatedAt,
			&alertRule.Operator,
		)
		require.NoError(t, err)
		require.Equal(t, tenantIDDB, tenantId)
		require.Equal(t, sessionIDDB, sessionId)
		require.Equal(t, alertRule.ID, createdAlertRule.Rule.ID)
		require.Equal(t, alertRule.Name, createdAlertRule.Rule.Name)
		require.Equal(t, alertRule.Description, createdAlertRule.Rule.Description)
		require.Equal(t, alertRule.Type, createdAlertRule.Rule.Type)
		require.Equal(t, alertRule.Attribute, createdAlertRule.Rule.Attribute)
		require.Equal(t, alertRule.Aggregation, createdAlertRule.Rule.Aggregation)
		require.Equal(t, alertRule.WindowMinutes, createdAlertRule.Rule.WindowMinutes)
		require.Equal(t, *alertRule.Threshold, *createdAlertRule.Rule.Threshold)
		require.Equal(t, *alertRule.ThresholdWindowMinutes, *createdAlertRule.Rule.ThresholdWindowMinutes)
		require.Equal(t, *alertRule.ThresholdMultiplier, *createdAlertRule.Rule.ThresholdMultiplier)
		require.Equal(t, alertRule.Filter, createdAlertRule.Rule.Filter)
		require.Equal(t, alertRule.DenominatorFilter, createdAlertRule.Rule.DenominatorFilter)
		require.NotNil(t, alertRule.CreatedAt)
		require.NotNil(t, alertRule.UpdatedAt)
		require.Equal(t, alertRule.Operator, createdAlertRule.Rule.Operator)
		// check that the alert action was created in the database
		var alertAction alerts.AlertAction
		rows, err := dbpool.Query(
			context.Background(),
			"SELECT id, alert_rule_id, target, config, created_at, updated_at FROM alert_actions WHERE alert_rule_id = $1",
			createdAlertRule.Rule.ID,
		)
		require.NoError(t, err)
		defer rows.Close()
		alertActions := []alerts.AlertAction{}
		for rows.Next() {
			err = rows.Scan(
				&alertAction.ID,
				&alertAction.AlertRuleID,
				&alertAction.Target,
				&alertAction.Config,
				&alertAction.CreatedAt,
				&alertAction.UpdatedAt,
			)
			require.NoError(t, err)
			alertActions = append(alertActions, alertAction)
		}
		require.Equal(t, len(alertActions), 1)
		require.Equal(t, alertActions[0].ID, createdAlertRule.Actions[0].ID)
		require.Equal(t, alertActions[0].Target, createdAlertRule.Actions[0].Target)
		require.Equal(t, string(alertActions[0].Config), string(createdAlertRule.Actions[0].Config))
	})

}

func TestGetAlertRule(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	t.Run("get alert rule success", func(t *testing.T) {

		orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
		tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
		sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

		// create alert rule
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{}\"}"
				}
			]
		}`)
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)

		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		createdAlertRule := alerts.AlertRuleResponse{}
		err := json.Unmarshal(w.Body.Bytes(), &createdAlertRule)
		require.NoError(t, err)

		// get alert rule
		w = httptest.NewRecorder()
		r = SetupRequestWithParams(t, authInfo, "GET", fmt.Sprintf("/alerts/%s/%s", sessionId, createdAlertRule.Rule.ID), strings.NewReader(""), &sessionId, &createdAlertRule.Rule.ID)
		alertRulesCrudHandler.GetAlertRuleHandler(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// check that the alert rule was returned
		var alertRule alerts.AlertRuleResponse
		err = json.Unmarshal(w.Body.Bytes(), &alertRule)
		require.NoError(t, err)
		require.Equal(t, alertRule.Rule.ID, createdAlertRule.Rule.ID)
		require.Equal(t, alertRule.Rule.Name, createdAlertRule.Rule.Name)
		require.Equal(t, alertRule.Rule.Description, createdAlertRule.Rule.Description)
		require.Equal(t, alertRule.Rule.Type, createdAlertRule.Rule.Type)
		require.Equal(t, alertRule.Rule.Attribute, createdAlertRule.Rule.Attribute)
		require.Equal(t, alertRule.Rule.Aggregation, createdAlertRule.Rule.Aggregation)
		require.Equal(t, alertRule.Rule.WindowMinutes, createdAlertRule.Rule.WindowMinutes)
		require.Equal(t, *alertRule.Rule.Threshold, *createdAlertRule.Rule.Threshold)
		require.Equal(t, *alertRule.Rule.ThresholdWindowMinutes, *createdAlertRule.Rule.ThresholdWindowMinutes)
		require.Equal(t, *alertRule.Rule.ThresholdMultiplier, *createdAlertRule.Rule.ThresholdMultiplier)
		require.Equal(t, alertRule.Rule.Filter, createdAlertRule.Rule.Filter)
		require.Equal(t, alertRule.Rule.DenominatorFilter, createdAlertRule.Rule.DenominatorFilter)
		require.NotNil(t, alertRule.Rule.CreatedAt)
		require.NotNil(t, alertRule.Rule.UpdatedAt)
		require.Equal(t, alertRule.Rule.Operator, createdAlertRule.Rule.Operator)
		// check that the alert action was returned
		require.Equal(t, alertRule.Actions[0].ID, createdAlertRule.Actions[0].ID)
		require.Equal(t, alertRule.Actions[0].AlertRuleID, createdAlertRule.Actions[0].AlertRuleID)
		require.Equal(t, alertRule.Actions[0].Target, createdAlertRule.Actions[0].Target)
		require.Equal(t, string(alertRule.Actions[0].Config), string(createdAlertRule.Actions[0].Config))
		require.NotNil(t, alertRule.Actions[0].CreatedAt)
		require.NotNil(t, alertRule.Actions[0].UpdatedAt)
	})
}

func TestListAlertRulesBySessionID(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	t.Run("list alert rules by session id success", func(t *testing.T) {

		orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
		tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
		sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

		// create alert rule
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{}\"}"
				}
			]
		}`)
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		createdAlertRule1 := alerts.AlertRuleResponse{}
		err := json.Unmarshal(w.Body.Bytes(), &createdAlertRule1)
		require.NoError(t, err)

		// Create a second alert rule with the same session ID
		w = httptest.NewRecorder()
		b = strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule-2",
				"description": "test-alert-rule-description-2",
				"type": "threshold",
				"attribute": "latency",
				"aggregation": "avg",
				"window_minutes": 5,
				"operator": "lte",
				"threshold": 100
			},
			"actions": [
				{
					"target": "pagerduty",
					"config": "{\"integration_key\": \"abc123\", \"severity\": \"error\", \"project_name\": \"test-project\"}"
				}
			]
		}`)
		r = SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		createdAlertRule2 := alerts.AlertRuleResponse{}
		err = json.Unmarshal(w.Body.Bytes(), &createdAlertRule2)
		require.NoError(t, err)

		// List alert rules by session ID
		w = httptest.NewRecorder()
		r = SetupRequestWithParams(t, authInfo, "GET", fmt.Sprintf("/alerts/session/%s", sessionId), strings.NewReader(""), &sessionId, nil)
		alertRulesCrudHandler.ListAlertRulesBySessionIDHandler(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// Parse response
		var alertRules alerts.ListAlertRulesResponse
		err = json.Unmarshal(w.Body.Bytes(), &alertRules)
		require.NoError(t, err)

		// Verify response
		require.Equal(t, 2, len(alertRules.SessionAlertRules), "Should return 2 alert rules")

		// Check first alert rule
		foundRule1 := false
		foundRule2 := false

		for _, rule := range alertRules.SessionAlertRules {
			if rule.Rule.ID == createdAlertRule1.Rule.ID {
				foundRule1 = true
				require.Equal(t, createdAlertRule1.Rule.Name, rule.Rule.Name)
				require.Equal(t, createdAlertRule1.Rule.Description, rule.Rule.Description)
				require.Equal(t, createdAlertRule1.Rule.Type, rule.Rule.Type)
				require.Equal(t, createdAlertRule1.Rule.Attribute, rule.Rule.Attribute)
				require.Equal(t, createdAlertRule1.Rule.Aggregation, rule.Rule.Aggregation)
				require.Equal(t, createdAlertRule1.Rule.WindowMinutes, rule.Rule.WindowMinutes)
				require.Equal(t, *createdAlertRule1.Rule.ThresholdWindowMinutes, *rule.Rule.ThresholdWindowMinutes)
				require.Equal(t, *createdAlertRule1.Rule.ThresholdMultiplier, *rule.Rule.ThresholdMultiplier)
				require.Equal(t, createdAlertRule1.Rule.Operator, rule.Rule.Operator)
				// Check actions
				require.Equal(t, 1, len(rule.Actions))
				require.Equal(t, createdAlertRule1.Actions[0].ID, rule.Actions[0].ID)
				require.Equal(t, createdAlertRule1.Actions[0].Target, rule.Actions[0].Target)
				require.Equal(t, string(createdAlertRule1.Actions[0].Config), string(rule.Actions[0].Config))
			} else if rule.Rule.ID == createdAlertRule2.Rule.ID {
				foundRule2 = true
				require.Equal(t, createdAlertRule2.Rule.Name, rule.Rule.Name)
				require.Equal(t, createdAlertRule2.Rule.Description, rule.Rule.Description)
				require.Equal(t, createdAlertRule2.Rule.Type, rule.Rule.Type)
				require.Equal(t, createdAlertRule2.Rule.Attribute, rule.Rule.Attribute)
				require.Equal(t, createdAlertRule2.Rule.Aggregation, rule.Rule.Aggregation)
				require.Equal(t, createdAlertRule2.Rule.WindowMinutes, rule.Rule.WindowMinutes)
				require.Equal(t, *createdAlertRule2.Rule.Threshold, *rule.Rule.Threshold)
				require.Nil(t, rule.Rule.ThresholdWindowMinutes)
				require.Nil(t, rule.Rule.ThresholdMultiplier)
				require.Equal(t, createdAlertRule2.Rule.Operator, rule.Rule.Operator)
				// Check actions
				require.Equal(t, 1, len(rule.Actions))
				require.Equal(t, createdAlertRule2.Actions[0].ID, rule.Actions[0].ID)
				require.Equal(t, createdAlertRule2.Actions[0].Target, rule.Actions[0].Target)
				require.Equal(t, string(createdAlertRule2.Actions[0].Config), string(rule.Actions[0].Config))
			}
		}

		require.True(t, foundRule1, "First alert rule should be in the response")
		require.True(t, foundRule2, "Second alert rule should be in the response")
	})
}

func TestDeleteAlertRule(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	t.Run("delete alert rule success", func(t *testing.T) {

		orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
		tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
		sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

		// delete alert rule
		w := httptest.NewRecorder()
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		// first create the alert rule
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{}\"}"
				}
			]
		}`)
		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		createdAlertRule := alerts.AlertRuleResponse{}
		err := json.Unmarshal(w.Body.Bytes(), &createdAlertRule)
		require.NoError(t, err)

		// then delete the alert rule
		w = httptest.NewRecorder()
		r = SetupRequestWithParams(t, authInfo, "DELETE", fmt.Sprintf("/alerts/%s/%s", sessionId, createdAlertRule.Rule.ID), strings.NewReader(""), &sessionId, &createdAlertRule.Rule.ID)
		alertRulesCrudHandler.DeleteAlertRuleHandler(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// check that the alert rule was deleted from the database
		err = dbpool.QueryRow(
			context.Background(),
			"SELECT 1 FROM alert_rules WHERE id = $1",
			createdAlertRule.Rule.ID,
		).Scan()
		require.Error(t, err, "Should return an error because the alert rule was deleted")

		// check that the alert action was deleted from the database
		err = dbpool.QueryRow(
			context.Background(),
			"SELECT 1 FROM alert_actions WHERE alert_rule_id = $1",
			createdAlertRule.Rule.ID,
		).Scan()
		require.Error(t, err, "Should return an error because the alert action was cascade deleted")
	})
}

func TestUpdateAlertRule(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	t.Run("update alert rule success", func(t *testing.T) {

		orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
		tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
		sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

		// create alert rule
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{}\"}"
				}
			]
		}`)

		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		createdAlertRule := alerts.AlertRuleResponse{}
		err := json.Unmarshal(w.Body.Bytes(), &createdAlertRule)
		require.NoError(t, err)

		// check that the alert rule was created in the database
		var threshold float64
		err = dbpool.QueryRow(
			context.Background(),
			"SELECT threshold FROM alert_rules WHERE id = $1",
			createdAlertRule.Rule.ID,
		).Scan(&threshold)
		require.NoError(t, err)
		require.Equal(t, 1.0, threshold)

		// update the alert rule threshold
		w = httptest.NewRecorder()
		b = strings.NewReader(fmt.Sprintf(`{
			"rule": {
				"id": "%s",
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "lte",
				"threshold": 2,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"id": "%s",
					"alert_rule_id": "%s",
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{}\"}"
				}
			]
		}`, createdAlertRule.Rule.ID, createdAlertRule.Actions[0].ID, createdAlertRule.Rule.ID))
		r = SetupRequestWithParams(t, authInfo, "PATCH", fmt.Sprintf("/alerts/%s/%s", sessionId, createdAlertRule.Rule.ID), b, &sessionId, &createdAlertRule.Rule.ID)
		alertRulesCrudHandler.UpdateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// check that the alert rule was updated in the database
		var operator string
		err = dbpool.QueryRow(
			context.Background(),
			"SELECT threshold, operator FROM alert_rules WHERE id = $1",
			createdAlertRule.Rule.ID,
		).Scan(&threshold, &operator)
		require.NoError(t, err)
		require.Equal(t, 2.0, threshold)
		require.Equal(t, "lte", operator)
	})
}

func TestValidateWebhookAlertActionConfig(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

	t.Run("validate alert action webhook config invalid url failure", func(t *testing.T) {
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"invalid-url\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{}\"}"
				}
			]
		}`)

		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})

	t.Run("validate alert action webhook config invalid headers failure", func(t *testing.T) {
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"invalid-headers\", \"body\": \"{}\"}"
				}
			]
		}`)

		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})

	t.Run("validate alert action webhook config invalid body failure", func(t *testing.T) {
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "webhook",
					"config": "{\"url\": \"https://example.com/webhook\", \"project_name\": \"test-project\", \"headers\": \"{}\", \"body\": \"{\\\"key\\\": \\\"value\\\", \\\"key2\\\"}\"}"
				}
			]
		}`)
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})
}

func TestValidatePagerDutyAlertActionConfig(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)

	alertRulesCrudHandler := alerts.NewAlertRulesCrudHandler(dbpool)

	orgId := testutil.OrgSetup(t, dbpool, "test org 1", false, "00000000-0000-4000-8000-000000000005")
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", "test-tenant", &auth.TenantConfig{}, true)
	sessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)

	t.Run("validate alert action pagerduty config invalid service key failure", func(t *testing.T) {
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "pagerduty",
					"config": "{\"random_key\": \"invalid-service-key\", \"severity\": \"info\"}"
				}
			]
		}`)

		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})

	t.Run("validate alert action pagerduty config invalid severity failure", func(t *testing.T) {
		w := httptest.NewRecorder()
		b := strings.NewReader(`{
			"rule": {
				"name": "test-alert-rule",
				"description": "test-alert-rule-description",
				"type": "change",
				"attribute": "error_count",
				"aggregation": "sum",
				"window_minutes": 10,
				"operator": "gte",
				"threshold": 1,
				"threshold_window_minutes": 60,
				"threshold_multiplier": 1.5
			},
			"actions": [
				{
					"target": "pagerduty",
					"config": "{\"service_key\": \"invalid-service-key\", \"sivirety\": \"critical\"}"
				}
			]
		}`)

		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      orgId,
			UserID:              "00000000-0000-4000-8000-000000000005",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}

		r := SetupRequestWithParams(t, authInfo, "POST", fmt.Sprintf("/alerts/%s", sessionId), b, &sessionId, nil)
		alertRulesCrudHandler.CreateAlertRuleHandler(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})
}
