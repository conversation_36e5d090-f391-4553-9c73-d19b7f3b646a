# Stage 1: Building the Go binary
FROM golang:1.23 AS builder
ARG ARCH="arm64"
ARG APP_VERSION="dev"

# Set the Current Working Directory inside the container
WORKDIR /app

# Copy go mod and sum files
COPY ./smith-go/go.mod ./smith-go/go.sum ./

# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download

# Copy the source from the current directory to the Working Directory inside the container
COPY ./smith-go .

# Build the Go app
RUN CGO_ENABLED=1 GOOS=linux GOARCH=${ARCH} go build -ldflags "-X langchain.com/smith/license.Version=$APP_VERSION -extldflags -static"  -a -installsuffix cgo -o smith-go .

# Stage 2: Setup the runtime container
FROM alpine:latest

RUN apk --no-cache add ca-certificates

COPY ./.env* ./
RUN rm ./.env.local_dev* ./.env.local_test

WORKDIR /code

# Copy the Pre-built binary file from the previous stage
COPY --from=builder /app/smith-go .

# Command to run the executable
CMD ["./smith-go"]
