package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/redis/go-redis/v9"
)

// Lua script for atomic enqueue operation
var enqueueScript = redis.NewScript(`
    if not redis.call('ZSCOR<PERSON>', KEYS[1], KEYS[2]) and redis.call('EXISTS', KEYS[4]) == 0 then
        redis.call('SET', KEYS[2], ARGV[1])
        redis.call('ZADD', KEYS[1], ARGV[2], KEYS[2])
        if ARGV[2] == '0' then redis.call('RPUSH', KEYS[3], KEYS[2]) end
        return 1
    else
        return nil
    end
`)

type Job struct {
	Function     string                 `json:"function"`
	Args         []interface{}          `json:"args,omitempty"`
	Kwargs       map[string]interface{} `json:"kwargs,omitempty"`
	Queue        string                 `json:"queue"`
	Key          string                 `json:"key"`
	Timeout      int                    `json:"timeout"`
	Retries      int                    `json:"retries"`
	RetryDelay   int                    `json:"retry_delay"`
	RetryBackoff bool                   `json:"retry_backoff"`
	Queued       int64                  `json:"queued"`
	Status       string                 `json:"status"`
}

type JobEnqueue struct {
	Func   string
	Kwargs map[string]interface{}
}

const (
	JobTimeout        = 300
	MaxAttempts       = 2
	MaxIngestAttempts = 12 // Last attempt delay of 5*2^10 seconds
	RetryDelay        = 5
)

func InitializeScripts(ctx context.Context, rdb redis.UniversalClient) (string, error) {
	stringCmd := enqueueScript.Load(ctx, rdb)
	return stringCmd.Result()
}

func prepareEnqueue(job *Job) ([]string, []interface{}, error) {
	// Serialize the job using JSON compatible with ORJSON
	jobJSON, err := json.Marshal(job)
	if err != nil {
		return nil, nil, fmt.Errorf("error marshaling job: %w", err)
	}

	incompleteKey := fmt.Sprintf("saq:%s:incomplete", job.Queue)
	queuedKey := fmt.Sprintf("saq:%s:queued", job.Queue)
	abortIDKey := fmt.Sprintf("saq:job:%s:abort", job.Key)
	// Job ID is fully qualified with queue name, but key is without queue name
	jobID := fmt.Sprintf("saq:job:%s:%s", job.Queue, job.Key)

	scheduled := "0" // For immediate execution

	// ARGV[1]: job JSON
	// ARGV[2]: scheduled time (as string)
	args := []interface{}{jobJSON, scheduled}
	keys := []string{incompleteKey, jobID, queuedKey, abortIDKey}

	return keys, args, nil
}

func EnqueueWithPipeline(ctx context.Context, job *Job, pipe redis.Pipeliner) error {
	oplog := httplog.LogEntry(ctx)

	keys, args, err := prepareEnqueue(job)
	if err != nil {
		return err
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	result := enqueueScript.Run(timeoutCtx, pipe, keys, args...)

	_, err = result.Result()
	if err != nil {
		if err == context.DeadlineExceeded {
			oplog.Error("Timeout enqueueing job to queue after 5s",
				"job", job.Key,
				"queue", job.Queue,
				"err", err)
			return err
		}
		if err != redis.Nil {
			oplog.Error("Failed to run enqueue script with pipeline",
				"job", job.Key,
				"queue", job.Queue,
				"err", err)
			return err
		}
	}

	return nil
}

func Enqueue(ctx context.Context, job *Job, rdb redis.UniversalClient) error {
	oplog := httplog.LogEntry(ctx)
	keys, args, err := prepareEnqueue(job)
	if err != nil {
		oplog.Error("Error preparing enqueue", "err", err)
		return err
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	result := enqueueScript.Run(timeoutCtx, rdb, keys, args...)

	_, err = result.Result()
	if err != nil {
		if err == context.DeadlineExceeded {
			oplog.Error("Timeout enqueueing job to queue after 5s",
				"job", job.Key,
				"queue", job.Queue,
				"err", err)
			return err
		}
		if err != redis.Nil {
			oplog.Error("Failed to run enqueue script",
				"job", job.Key,
				"queue", job.Queue,
				"err", err)
			return err
		}
	}

	return nil
}
