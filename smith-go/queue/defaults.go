package queue

import (
	"time"

	"github.com/google/uuid"
)

const (
	DefaultJobTimeout        = 300
	DefaultMaxAttempts       = 2
	DefaultMaxIngestAttempts = 12
	DefaultRetryDelay        = 5
	DefaultStatus            = "queued"
	JobKeyPrefix             = "saq:job:"
)

type JobOptions struct {
	Function     string
	Queue        string
	Kwargs       map[string]interface{}
	Timeout      int
	Retries      int
	RetryDelay   int
	RetryBackoff *bool
	KeyPrefix    string
	Status       string
}

func DefaultJobOptions() JobOptions {
	retryBackoff := true
	return JobOptions{
		Timeout:      DefaultJobTimeout,
		Retries:      DefaultMaxIngestAttempts,
		RetryDelay:   DefaultRetryDelay,
		RetryBackoff: &retryBackoff,
		KeyPrefix:    JobKeyPrefix,
		Status:       DefaultStatus,
	}
}

func NewJob(opts JobOptions) *Job {
	defaults := DefaultJobOptions()

	if opts.Timeout == 0 {
		opts.Timeout = defaults.Timeout
	}
	if opts.Retries == 0 {
		opts.Retries = defaults.Retries
	}
	if opts.RetryDelay == 0 {
		opts.RetryDelay = defaults.RetryDelay
	}
	if opts.KeyPrefix == "" {
		opts.KeyPrefix = defaults.KeyPrefix
	}
	if opts.Status == "" {
		opts.Status = defaults.Status
	}

	retryBackoff := true
	if opts.RetryBackoff != nil {
		retryBackoff = *opts.RetryBackoff
	}

	return &Job{
		Function:     opts.Function,
		Kwargs:       opts.Kwargs,
		Queue:        opts.Queue,
		Key:          uuid.New().String(),
		Timeout:      opts.Timeout,
		Retries:      opts.Retries,
		RetryDelay:   opts.RetryDelay,
		RetryBackoff: retryBackoff,
		Queued:       time.Now().UnixMilli(),
		Status:       opts.Status,
	}
}
