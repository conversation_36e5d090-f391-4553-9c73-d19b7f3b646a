package compression

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"errors"
	"io"
	"os"
	"sync"

	"github.com/andybalholm/brotli"
	"github.com/vmihailenco/msgpack/v5"

	"langchain.com/smith/config"
)

type CompressionMethod string

const (
	GZIP   CompressionMethod = "gzip" // lowercase to match content-encoding header
	BROTLI CompressionMethod = "BROTLI"
	NONE   CompressionMethod = "NONE"
)

func (c CompressionMethod) MarshalBinary() ([]byte, error) {
	return []byte(c), nil
}

func (c *CompressionMethod) UnmarshalBinary(data []byte) error {
	*c = CompressionMethod(data)
	return nil
}

type CompressedData struct {
	Data              []byte
	CompressionMethod CompressionMethod
	ContentType       string
}

type PayloadData struct {
	ContentEncoding string
	ContentType     string
	Payload         []byte
}

func CompressBasedOnSizeMany(payloads map[string]PayloadData) (map[string]CompressedData, error) {
	if len(payloads) == 0 {
		return map[string]CompressedData{}, nil
	}

	var wg sync.WaitGroup
	result := make(map[string]CompressedData)
	mu := &sync.Mutex{}
	errorsChan := make(chan error, len(payloads))

	for key, payload := range payloads {
		wg.Add(1)
		go func(k string, p PayloadData) {
			defer wg.Done()
			compressedPayload, compressionMethod, err := CompressBasedOnSize(p.ContentEncoding, p.Payload)
			if err != nil {
				errorsChan <- err
				return
			}
			mu.Lock()
			result[k] = CompressedData{
				Data:              compressedPayload,
				CompressionMethod: compressionMethod,
				ContentType:       p.ContentType,
			}
			mu.Unlock()
		}(key, payload)
	}

	wg.Wait()
	close(errorsChan)

	if len(errorsChan) > 0 {
		return nil, <-errorsChan
	}

	return result, nil
}

func CompressBasedOnSize(contentEncoding string, payload []byte) ([]byte, CompressionMethod, error) {
	size := len(payload)
	compressMinSizeKB := config.Env.CompressMinSizeKB * 1024

	if contentEncoding != "" {
		if contentEncoding == string(GZIP) {
			// Payload already compressed with GZIP
			return payload, GZIP, nil
		}
		return nil, "", errors.New("unsupported content encoding: " + contentEncoding)
	} else if size < int(compressMinSizeKB) {
		// Payload too small to compress
		return payload, NONE, nil
	} else {
		// Compress with Brotli in memory
		compressed, err := BrotliCompressPayload(payload)
		if err != nil {
			return nil, "", err
		}
		return compressed, BROTLI, nil
	}
}

func BrotliCompressPayload(payload []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer := brotli.NewWriterLevel(&buf, brotli.BestSpeed)
	_, err := writer.Write(payload)
	if err != nil {
		writer.Close()
		return nil, err
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

type tempFileReadCloser struct {
	*os.File
}

func (tf *tempFileReadCloser) Close() error {
	err := tf.File.Close()
	os.Remove(tf.File.Name())
	return err
}

func BrotliCompressPayloadIntoFile(payload []byte) (io.ReadCloser, error) {
	tempDir := os.TempDir()
	tempFile, err := os.CreateTemp(tempDir, "brotli_*")
	if err != nil {
		return nil, err
	}

	writer := brotli.NewWriterLevel(tempFile, brotli.BestSpeed)
	_, err = writer.Write(payload)
	if err != nil {
		writer.Close()
		tempFile.Close()
		os.Remove(tempFile.Name())
		return nil, err
	}
	err = writer.Close()
	if err != nil {
		tempFile.Close()
		os.Remove(tempFile.Name())
		return nil, err
	}

	// Reset file offset to the beginning
	_, err = tempFile.Seek(0, 0)
	if err != nil {
		tempFile.Close()
		os.Remove(tempFile.Name())
		return nil, err
	}

	// Wrap tempFile with a ReadCloser that removes the file when closed
	return &tempFileReadCloser{File: tempFile}, nil
}

func DecompressAndParseMany(payloads map[string]CompressedData) (map[string]interface{}, error) {
	var wg sync.WaitGroup
	result := make(map[string]interface{})
	mu := &sync.Mutex{}
	errorsChan := make(chan error, len(payloads))

	for key, payload := range payloads {
		wg.Add(1)
		go func(k string, p CompressedData) {
			defer wg.Done()

			decompressedBytes, err := DecompressBasedOnMethod(p.Data, p.CompressionMethod)
			if err != nil {
				errorsChan <- err
				return
			}

			parsed, err := ParsePayload(bytes.NewReader(decompressedBytes), p.ContentType)
			if err != nil {
				errorsChan <- err
				return
			}
			mu.Lock()
			result[k] = parsed
			mu.Unlock()
		}(key, payload)
	}

	wg.Wait()
	close(errorsChan)

	if len(errorsChan) > 0 {
		return nil, <-errorsChan
	}

	return result, nil
}

func DecompressBasedOnMethod(data []byte, compressionMethod CompressionMethod) ([]byte, error) {
	switch compressionMethod {
	case BROTLI, "":
		return BrotliDecompressPayload(data)
	case GZIP:
		return GzipDecompressPayload(data)
	case NONE:
		// No compression
		return data, nil
	default:
		return nil, errors.New("unsupported compression method: " + string(compressionMethod))
	}
}

func BrotliDecompressPayload(data []byte) ([]byte, error) {
	reader := brotli.NewReader(bytes.NewReader(data))
	return io.ReadAll(reader)
}

func GzipDecompressPayload(data []byte) ([]byte, error) {
	r, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer r.Close()
	return io.ReadAll(r)
}

func ParsePayload(payload io.Reader, contentType string) (interface{}, error) {
	if contentType == "" || contentType == "application/json" {
		var result interface{}
		decoder := json.NewDecoder(payload)
		err := decoder.Decode(&result)
		if err != nil {
			return nil, err
		}
		return result, nil
	} else if contentType == "application/msgpack" {
		var result interface{}
		decoder := msgpack.NewDecoder(payload)
		err := decoder.Decode(&result)
		if err != nil {
			return nil, err
		}
		return result, nil
	}
	return nil, errors.New("unsupported content type: " + contentType)
}
