package compression_test

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/assert"
	"langchain.com/smith/compression"

	"github.com/vmihailenco/msgpack/v5"
)

func TestCompressBasedOnSizeMany(t *testing.T) {
	smallData := []byte("small payload")
	largeData := bytes.Repeat([]byte("a"), 50*1024) // 50KB
	preCompressedData := []byte("pre-compressed payload")

	payloads := map[string]compression.PayloadData{
		"small": {
			ContentEncoding: "",
			ContentType:     "application/json",
			Payload:         smallData,
		},
		"large": {
			ContentEncoding: "",
			ContentType:     "application/json",
			Payload:         largeData,
		},
		"pre-compressed": {
			ContentEncoding: "gzip",
			ContentType:     "application/json",
			Payload:         preCompressedData,
		},
	}

	result, err := compression.CompressBasedOnSizeMany(payloads)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 3)

	// ------ SMALL PAYLOAD ------
	smallResult := result["small"]
	assert.Equal(t, compression.NONE, smallResult.CompressionMethod)

	smallCompressedPayload := smallResult.Data
	// Compare to original smallData bytes
	assert.Equal(t, smallData, smallCompressedPayload)

	assert.Equal(t, "application/json", smallResult.ContentType)

	// ------ LARGE PAYLOAD ------
	largeResult := result["large"]
	assert.Equal(t, compression.BROTLI, largeResult.CompressionMethod)

	largeCompressedPayload := largeResult.Data
	// Check that the compressed size < original
	assert.Less(t, len(largeCompressedPayload), len(largeData))

	assert.Equal(t, "application/json", largeResult.ContentType)

	// ------ PRE-COMPRESSED PAYLOAD (GZIP) ------
	preCompressedResult := result["pre-compressed"]
	assert.Equal(t, compression.GZIP, preCompressedResult.CompressionMethod)

	preCompressedPayloadBytes := preCompressedResult.Data
	// Compare to original preCompressedData bytes
	assert.Equal(t, preCompressedData, preCompressedPayloadBytes)

	assert.Equal(t, "application/json", preCompressedResult.ContentType)
}

func TestCompressBasedOnSizeMany_EmptyInput(t *testing.T) {
	result, err := compression.CompressBasedOnSizeMany(map[string]compression.PayloadData{})
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Len(t, result, 0)
}

func TestCompressBasedOnSizeMany_InvalidContentEncoding(t *testing.T) {
	payloads := map[string]compression.PayloadData{
		"invalid": {
			ContentEncoding: "invalid",
			ContentType:     "application/json",
			Payload:         []byte("test payload"),
		},
	}

	result, err := compression.CompressBasedOnSizeMany(payloads)
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "unsupported content encoding")
}

func TestCompressAndDecompress_MsgPack(t *testing.T) {
	// Prepare MsgPack payload
	originalData := map[string]interface{}{
		"key": "value",
		"num": 42,
	}

	var buf bytes.Buffer
	encoder := msgpack.NewEncoder(&buf)
	err := encoder.Encode(originalData)
	assert.NoError(t, err)

	payloads := map[string]compression.PayloadData{
		"msgpack": {
			ContentEncoding: "",
			ContentType:     "application/msgpack",
			Payload:         buf.Bytes(),
		},
	}

	// Compress
	compressedData, err := compression.CompressBasedOnSizeMany(payloads)
	assert.NoError(t, err)
	assert.NotNil(t, compressedData)

	// Decompress + parse
	decompressedData, err := compression.DecompressAndParseMany(compressedData)
	assert.NoError(t, err)
	assert.NotNil(t, decompressedData)
	assert.Len(t, decompressedData, 1)

	// Verify the data
	parsedData, ok := decompressedData["msgpack"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, originalData["key"], parsedData["key"])
	assert.EqualValues(t, originalData["num"], parsedData["num"])
}
