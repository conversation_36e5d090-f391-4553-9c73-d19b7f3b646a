package license

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"

	"github.com/golang-jwt/jwt/v4"
)

// This variable can be overridden at build time using -ldflags.
var Version = "dev" // default version if not overridden

var (
	v1LicensePublicKey = `-----BEGIN PUBLIC KEY-----
MIICITANBgkqhkiG9w0BAQEFAAOCAg4AMIICCQKCAgB4oUMm2810Ilp2YcWqlm2i
kENjdnhrfyaK7+c52N5d2TQAjoe6lbRf9bYJusDhO0bjkpErSc5Nd0dQqmA3f1RB
Wc9OLA0E5XHZMzVEid8WwNXkpWWpTlcNVDvhCAP2pr8g4+o6I6J9pKsHfRESCJ07
OI62GVJKoOjipoI0j4LgAVPLfDywwJmLr4gUkp228E55GJHHENqPSrnGV8ZqUd32
GfO76Owu68DHR19bzdpGCq0tVg2eU8t3G5l28MZpPL7QKL6RJgL1XzY8IDV2iLd3
I+MF9AQER8zTWGU27SV2vHiE8Tn/qufnZziB/282wgZPKYFCZb+2wJuL55vY7JEd
qaz5nztW+owdpMpttBCYQBPdvNPKvU4KTW+lmOrLrYQnOP3X2glndLSTrFr9wmMG
Swhnp0Kvj9bWnKv86hLaFvp0uW8rjffoyJ1bfKP1DneJjkkM2p5in2R8eYn3NBQ5
REU2bpRawNfXDwCOgLpCVMoIYQecOPnFgud7R0PZD1s6KeYqsT9NomAZ+EE/3F4l
dJxn1oHxzdNoYLiFZ9R4TZGAjkv8hHxYVZeeafOkJdYWvMqaGOpRyll3P4N7YQF/
Ae0Hchf66CLQV/mDU8LC6T0LqqLiW4UwIOITuK//AVEfwkh7jk35tEbXKS54eeF3
roZii5Ll5FQfc8RpfIiJWQIDAQAB
-----END PUBLIC KEY-----`
	// TODO: (langchain-infra) implement JWKS fetching. RN we just have KIDs for dev and prod
	publicLicenseJWKS = map[string]string{
		"local": `-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA0gyocJ2y0QNAUJh1BbZA
1PLvlKSL37kfaOmt2ZiPi5iqoGiTJXoPsdxHsGX5i4AUCsDQikYkhX2C/4a+cxWh
7HsRmxNWyfS4aS9BifVu6vfsJNRRnPuHj6qhKahu/C4G6nlL207Ty8W70UXFXO9S
8rsIDb0zFFyw18mYk7eCbaBFxlQSeLkh072YLhelRt4neUeL4nMHALNlwiuVGtwO
KFQTNAnJTxZO8cjrHQK10B09i69ycjInjvK5VZ7zLSGysUaHJWMysM0OVLMRdLDv
ll8l5QgO0LGdNAWFVjw87LL9XDRQikLd47PRgGXVO7O07BlLgZLJY+wafkukLH7Y
aTyXMce1/uTLxLnAFWEpI5Mbl5T6SoYkoI+KWzpk+T8FmX894b1d1HIRPixF0lUb
57GEtwkmvJyklyzZzjzSUYV1jopULh475oSC8AUXPnjvSsiLA43HI7xXRH4IskAD
qRiWklz+w3LcsC6Lk63s5q3elEsIMxYu8+NCVuT7LBCNQCUo9IOAqzAWZmOgvxRp
zECsOzZndTkRfleLLzvL/BNEVLFa5jSHivD6Xa/C8te/TJDgi5X/YmiEma9O/kYf
gIeiZEJCXGdXXU5A46mD14XRTKAmtVh2r+FIoXqYry/8uZSWBlUR/pIgFf0FTaYZ
fUia7+9TW15qGO1CTsCtnSkCAwEAAQ==
-----END PUBLIC KEY-----`,
		"dev": `-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAlU5BlmuKpYBGSo4ykeh7
DuhGXEn4zuJnCKqR9U1x3snf0UCC0575m/4wMdEwP2uFxKqxgqVpZMA0QaDsRDRW
udpnOKWzi28atLFrJZpxSLRxe/WJs/ZEDo+ee5H8Ob1hJqupUtTWNiDHBwYMbvBo
gBJO5bBU6t+xUUgRoS6M41EDLiXIJIfAs1NuaxmtCyZqe7sECh2lORa2HhwElCw3
LvBVz4nXkEgA1HMdHw5PB8AT5XEgax0XwuzavA8pNuVfceIWJipgqSWxlcfZ5KuF
H+6xvP6YCdUHB906ilpVRR3ja56tc2ahFGYur9G9XV+lrI2/EUnBJPjrzXyrCjee
yLJCiAZrF7lF9oa7NkyFnhKGPLF41uV87SExj1QHxo92MRwHsZ4nPgiuZE5r+l+S
TpnKjDeo4kRerJe+HxUaFmhr3vQBef3GDekVArdxwehF4IrVGlT7Nr7kDTEKZtJI
rVNYE2syolxU5HMFvLnPYtUI68hZtrOJ4LKUT8Yut/50I+p+0eLRs3T359i+3+Wi
6voYefSpfM6XCrtaFQ5YRAOYAlJmYy45ouPyBjMNtauanQHM5c9nZ7t7mlHWoSxC
wcKkudR+wtII6Hfnpdc9XmHY7xkgYgcYF3gCzthw8nFnQ4K+RGzvScbg6eWs4E3U
SaQ5asWf9uAMjd0ihy5GdbsCAwEAAQ==
-----END PUBLIC KEY-----`,
		"staging": `-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAxYj1BDGaez9GId4pjuZe
/IazS4tZwSu29J1i4+LnkzOckZH8yk9hKj7zB07EEkMiZ3tQ9+o5cDAqGl2Lh3tE
0DEa3qocbkWuoWTIezcuQBHLhMoZS9gv+c/CILCWSn2UWzF8vC8qwTJayGl26vb2
5l7CiPF4sjXI60PXVvOaqCkZdei9kJIGU9G+9vf5yf+BEGS4FBbqdqvoDFJuHBIt
7MYoud1moUBeG6RCoCxdm73/11+xI5GcHl9/mLic3Ih4jHfGAWD+KpcJ9hePuNTx
0ArkPnVtbiKtwOuhVEip2Jam2CxZbVFU6a8TVnbnjmXCGeWlUKRCq6k9nduJp/0g
DApi+ZvaOTrLcf3nwc0FWOp/cfKnkdyssWBsGvPhiiuX8gikQR5aK/DACcpQKzVB
fvgdleHPxE8XpT0v16jlU7ZCxZemm2Sc4WQR9LlOAV6gUYoHglyg6jr5isE9b11D
373ml1xoT8dlqdwG8+3f797xW96Y6OBtv/W3HWv84uZgYbsTdv3tTAv6waPVJm8o
cutX+mkcYot0sTFeXk0YnGXDVnHe0YEWuMYfukaXf1xPGTjwuojhc7vrdR/l9Lj8
g1U5Q0pE9A0xNxapz12EiRpsLfhpplzgWzoFD4ZpBDoathvS8o+OFNbUP//Fa2XL
5617JLBOpcIAgdB7gBvF/1kCAwEAAQ==
-----END PUBLIC KEY-----`,
		"prod": `-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAsLckaYnRceX1vfQD9W5i
bSo24J29ZhM7MHdZuO6PxBUD2EzZMzcxLfNla2a0YYPAqw6DDRSPxmok+n5Y1DDT
qTDUmn8rnFkgJ210t2jXYk5BVgcqNNbt92UogTri23GxVhYJqilWGAUilsyAwYaR
nytjOJodPPRb1gVt+PkMTP7b5rEE0+c/Gf1d1vFzP2WCgYD68FhV87m9UbZXsGKJ
ABOUclXtEhsN7zq8+K8GWlAumE4zZIt18scNr5OPlanwFcL6+Ha8p1NUoM5JMFgs
vmOL3q5sG2UoHHDatgcW8mrzSjVQ7PeIzRuLEVsy49Dp7XQm1qx/3Tb+UT+atUA+
CIy980xx8Na22AKosxjdrOPtkOakLQVQa9MggoQR1x6VcHheUVXHB/Dz1g5lgow3
7rire8u5HX8cXlSLwTAH30ka5Nrjp6LjEkydrlYhoZJCcRMYtcAAQIe4XYgdSpvW
UP4vGnWijOkbNC4vuLXYzzv9u/NurxfBYNI6sRdm7I3xUoIEZW5cs6p3QRb/E0DQ
wN3QaorP1bIAewrnRs9vvEr71KWVWPNYftXXbcW0BC6HYl2fNQGmFjVBnJcIlNax
McQKAWS9miE20Pzt0oJKLht897MaDOw45CDfXHbtRDaPLyiqeePEDu0zLe26htpL
3AsD90vkJdl6LWZvO5UDvg8CAwEAAQ==
-----END PUBLIC KEY-----`,
	}
)

func fetchLicenseKey(licenseKey string) (string, error) {
	url := fmt.Sprintf("%s/v1/beacon/verify", config.Env.BeaconEndpoint) // Replace with actual URL

	// Prepare the request body
	reqBody := map[string]interface{}{
		"license": licenseKey,
		"metadata": map[string]interface{}{
			"version": Version,
		},
	}
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", err
	}

	client := retryablehttp.NewClient()
	client.HTTPClient.Timeout = 10 * time.Second
	client.RetryMax = 3
	client.RetryWaitMin = 1 * time.Second
	client.RetryWaitMax = 4 * time.Second

	// Create a retryable HTTP request
	req, err := retryablehttp.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Check for successful response
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// Read and parse the response
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var respData map[string]string
	err = json.Unmarshal(bodyBytes, &respData)
	if err != nil {
		return "", err
	}

	newLicenseKey, ok := respData["token"]
	if !ok || newLicenseKey == "" {
		return "", fmt.Errorf("license key not found in response")
	}

	return newLicenseKey, nil
}

type LicenseData struct {
	LicenseKey    string `json:"license_key"`
	LastFetchedAt int64  `json:"last_fetched_at"`
}

func GetOrRefreshLicense(ctx context.Context, licenseKey string, redisPool redis.UniversalClient) (string, error) {
	if !strings.HasPrefix(licenseKey, "lcl_") {
		return licenseKey, nil
	}
	redisKey := fmt.Sprintf("license_key:%s", licenseKey)
	lockKey := fmt.Sprintf("license_key_lock:%s", licenseKey)
	lockTimeout := 10 * time.Second // Adjust as needed

	// Try to acquire the lock
	gotLock, err := acquireLock(ctx, redisPool, lockKey, lockTimeout)
	if err != nil {
		return "", err
	}

	if gotLock {
		defer func() {
			// Release the lock
			err := releaseLock(ctx, redisPool, lockKey)
			if err != nil {
				// Log the error but don't return it
				slog.Error("Error releasing lock", "err", err)
			}
		}()
	} else {
		// Wait for the lock to be released
		time.Sleep(lockTimeout)
	}

	// Fetch license data from Redis
	cachedDataStr, err := redisPool.Get(ctx, redisKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return "", err
	}

	var licenseData LicenseData
	if cachedDataStr != "" {
		// Unmarshal the cached data
		err = json.Unmarshal([]byte(cachedDataStr), &licenseData)
		if err != nil {
			// If unmarshaling fails, treat it as if no data is cached
			licenseData = LicenseData{}
		}
	}

	needToFetch := false
	if licenseData.LicenseKey == "" {
		// No license key cached
		needToFetch = true
	} else {
		_, err = DecodeLicenseJWT(licenseData.LicenseKey, GetSigningKey)
		if err != nil {
			slog.Info("Cached license verification failed. Refetching.")
			needToFetch = true
		}
		lastFetchedAt := time.Unix(licenseData.LastFetchedAt, 0)
		if time.Since(lastFetchedAt) > 24*time.Hour {
			slog.Info("Cached license key expired. Refetching.")
			needToFetch = true
		}
	}

	if needToFetch {
		if !gotLock {
			// If we didn't acquire the lock, we should not proceed to fetch
			// Return the cached data if available
			if licenseData.LicenseKey != "" {
				return licenseData.LicenseKey, nil
			}
			return "", fmt.Errorf("could not acquire lock to fetch license data")
		}

		// Fetch new license key via HTTP request
		newLicenseKey, err := fetchLicenseKey(licenseKey)
		if err != nil {
			if licenseData.LicenseKey != "" {
				slog.Error("Error fetching new license key, using stale key", "err", err)
				// Return the cached data if available
				return licenseData.LicenseKey, nil
			}
			return "", err
		}

		// Update license data
		licenseData = LicenseData{
			LicenseKey:    newLicenseKey,
			LastFetchedAt: time.Now().Unix(),
		}

		// Marshal license data to JSON
		licenseDataBytes, err := json.Marshal(licenseData)
		if err != nil {
			return "", err
		}

		// Store in Redis
		err = redisPool.Set(ctx, redisKey, licenseDataBytes, 0).Err()
		if err != nil {
			return "", err
		}

		return newLicenseKey, nil
	}

	// Use cached license key
	return licenseData.LicenseKey, nil
}

func acquireLock(ctx context.Context, redisPool redis.UniversalClient, lockKey string, timeout time.Duration) (bool, error) {
	success, err := redisPool.SetNX(ctx, lockKey, "locked", timeout).Result()
	if err != nil {
		return false, err
	}
	return success, nil
}

func releaseLock(ctx context.Context, redisPool redis.UniversalClient, lockKey string) error {
	_, err := redisPool.Del(ctx, lockKey).Result()
	return err
}

func GetSigningKey(token *jwt.Token) (interface{}, error) {
	// Old license, no JWKS
	keyId, ok := token.Header["kid"].(string)
	if !ok {
		return jwt.ParseRSAPublicKeyFromPEM([]byte(v1LicensePublicKey))
	}

	publicKeyPEM, ok := publicLicenseJWKS[keyId]
	if !ok {
		return nil, fmt.Errorf("public key not found for key ID: %s", keyId)
	}

	return jwt.ParseRSAPublicKeyFromPEM([]byte(publicKeyPEM))
}

func DecodeLicenseJWT(licenseJWT string, keyFunc func(token *jwt.Token) (interface{}, error)) (jwt.MapClaims, error) {
	licenseJWT = strings.TrimSpace(licenseJWT)

	token, err := jwt.Parse(licenseJWT, keyFunc)

	if err != nil {
		slog.Error("Error parsing token", "err", err)
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// Verify the audience
		if audience, ok := claims["aud"]; ok && audience == "langsmith" {
			return claims, nil
		}
	}

	slog.Error("Failed to verify token, invalid claims")
	return nil, errors.New("invalid claims")
}

func GetSigningKeyAndDecodeLicenseJWT(licenseJWT string) (jwt.MapClaims, error) {
	return DecodeLicenseJWT(licenseJWT, GetSigningKey)
}

func StartLicenseVerification(ctx context.Context, licenseKey string, exitFunc func(int), redisPool redis.UniversalClient, verifyInterval time.Duration) {
	oplog := httplog.LogEntry(ctx)
	go func() {
		licenseJWT, err := GetOrRefreshLicense(ctx, licenseKey, redisPool)
		if err != nil {
			oplog.Error("Error fetching license key:", "err", err)
			exitFunc(1)
			return
		}
		// Verify immediately
		_, err = DecodeLicenseJWT(licenseJWT, GetSigningKey)
		if err != nil {
			oplog.Error("Initial license verification failed. Exiting.")
			exitFunc(1)
			return
		}
		oplog.Info("Initial license verification passed.")

		ticker := time.NewTicker(verifyInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				oplog.Info("Performing periodic license verification...")
				licenseJWT, err = GetOrRefreshLicense(ctx, licenseKey, redisPool)
				if err != nil {
					oplog.Error("Error fetching license key:", "err", err)
					exitFunc(1)
					return
				}
				_, err = DecodeLicenseJWT(licenseJWT, GetSigningKey)
				if err != nil {
					oplog.Error("License verification failed. Exiting.")
					exitFunc(1)
				} else {
					oplog.Info("License verification passed.")
				}
			case <-ctx.Done():
				// Context canceled, exit the goroutine
				oplog.Info("License verification stopped.")
				return
			}
		}
	}()
}
