package license

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/rsa"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/ggicci/httpin"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/jwtauth/v5"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/auth"
	"langchain.com/smith/beacon"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil/leak"

	"github.com/golang-jwt/jwt/v4"
)

func generateRSAKeyPair() (*rsa.PrivateKey, *rsa.PublicKey, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, nil, err
	}
	return privateKey, &privateKey.PublicKey, nil
}

func generateTestToken(privateKey *rsa.PrivateKey, claims jwt.MapClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	return token.SignedString(privateKey)
}

func TestDecodeLicenseKey(t *testing.T) {
	validPrivateKey, validPublicKey, err := generateRSAKeyPair()
	if err != nil {
		t.Fatalf("Error generating RSA key pair: %v", err)
	}

	// Generate a second key pair for invalid signature tests
	invalidPrivateKey, _, err := generateRSAKeyPair()
	if err != nil {
		t.Fatalf("Error generating second RSA key pair: %v", err)
	}

	// Define test cases
	testCases := []struct {
		name      string
		tokenFunc func() (string, error)
		valid     bool
	}{
		{
			name: "Valid License Key",
			tokenFunc: func() (string, error) {
				return generateTestToken(validPrivateKey, jwt.MapClaims{
					"aud": "langsmith",
					"exp": time.Now().Add(10 * time.Minute).Unix(),
				})
			},
			valid: true,
		},
		{
			name: "Invalid License Key",
			tokenFunc: func() (string, error) {
				return "invalid_license_key", nil
			},
			valid: false,
		},
		{
			name: "Expired License Key",
			tokenFunc: func() (string, error) {
				return generateTestToken(validPrivateKey, jwt.MapClaims{
					"aud": "langsmith",
					"exp": time.Now().Add(-10 * time.Minute).Unix(),
				})
			},
			valid: false,
		},
		{
			name: "Incorrect Audience",
			tokenFunc: func() (string, error) {
				return generateTestToken(validPrivateKey, jwt.MapClaims{
					"aud": "wrong_audience",
					"exp": time.Now().Add(10 * time.Minute).Unix(),
				})
			},
			valid: false,
		},
		{
			name: "Invalid Signature",
			tokenFunc: func() (string, error) {
				// Generate a token with a different private key
				return generateTestToken(invalidPrivateKey, jwt.MapClaims{
					"aud": "langsmith",
					"exp": time.Now().Add(10 * time.Minute).Unix(),
				})
			},
			valid: false,
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tokenString, err := tc.tokenFunc()
			if err != nil {
				t.Fatalf("Error generating token: %v", err)
			}

			keyFunc := func(token *jwt.Token) (interface{}, error) {
				return validPublicKey, nil
			}

			result, err := DecodeLicenseJWT(tokenString, keyFunc)
			if result.Valid() != nil {
				if tc.valid {
					t.Errorf("Expected valid result, got %v", result.Valid())
				}
			}
		})
	}
}

func TestStartLicenseVerification(t *testing.T) {
	// Create a fake beacon server to issue a license
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer dbpool.Close()

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	// Create a new chi router
	r := chi.NewRouter()

	// Mount the middleware and handlers
	r.Route("/v1/beacon", func(r chi.Router) {
		r.Use(middleware.Timeout(60 * time.Second))

		beaconHandler := beacon.NewBeaconHandler(dbpool)

		r.With(
			httpin.NewInput(beacon.VerifyRequest{}),
		).Post("/verify", beaconHandler.VerifyLicenseKey)
		r.With(httpin.NewInput(beacon.IngestTracesRequest{})).Post("/ingest-traces", beaconHandler.IngestTraces)
		r.With(ah.XServiceKeyMiddleware,
			httpin.NewInput(beacon.CreateLicenseRequest{}),
		).Post("/internal/create-license", beaconHandler.InternalCreateLicense)
		r.With(ah.XServiceKeyMiddleware,
			httpin.NewInput(beacon.CreateCustomerRequest{}),
		).Post("/internal/create-customer", beaconHandler.InternalCreateCustomer)
	})

	// Create a test server using httptest
	ts := httptest.NewServer(r)
	defer ts.Close()

	config.Env.BeaconEndpoint = ts.URL
	expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
	jwtAuth := jwtauth.New(
		"HS256",
		[]byte(config.Env.XServiceAuthJwtSecret),
		nil,
	)

	claims := map[string]interface{}{
		"sub":       "unspecified",
		"tenant_id": expectedTenantId,
		"exp":       time.Now().Add(5 * time.Minute).Unix(),
	}

	_, xServiceToken, err := jwtAuth.Encode(claims)
	if err != nil {
		t.Fatalf("Failed to encode JWT: %v", err)
	}

	// Step 1: Create a customer using the InternalCreateCustomer endpoint
	createCustomerRequest := map[string]interface{}{
		"customer_name": "Ingest Test Customer",
		"users":         10,
	}

	reqBodyBytes, err := json.Marshal(createCustomerRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create customer request: %v", err)
	}

	// Create the request to the create-customer endpoint
	createCustomerReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-customer", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createCustomerReq.Header.Set("Content-Type", "application/json")
	createCustomerReq.Header.Set("X-Service-Key", xServiceToken)

	client := &http.Client{}
	createCustomerResp, err := client.Do(createCustomerReq)
	if err != nil {
		t.Fatalf("Failed to make create customer request: %v", err)
	}
	defer createCustomerResp.Body.Close()

	if createCustomerResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createCustomerResp.StatusCode)
	}

	var createCustomerResponse map[string]interface{}
	err = json.NewDecoder(createCustomerResp.Body).Decode(&createCustomerResponse)
	if err != nil {
		t.Fatalf("Failed to decode create customer response: %v", err)
	}

	// Step 2: Create a license using the InternalCreateLicense endpoint
	createLicenseRequest := map[string]interface{}{
		"customer_id":      createCustomerResponse["customer_id"],
		"license_type":     "langsmith",
		"expiration_weeks": 4,
	}

	reqBodyBytes, err = json.Marshal(createLicenseRequest)
	if err != nil {
		t.Fatalf("Failed to marshal create license request: %v", err)
	}

	// Create the request to the create-license endpoint
	createLicenseReq, err := http.NewRequest("POST", ts.URL+"/v1/beacon/internal/create-license", bytes.NewReader(reqBodyBytes))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	createLicenseReq.Header.Set("Content-Type", "application/json")
	createLicenseReq.Header.Set("X-Service-Key", xServiceToken)

	createLicenseResp, err := client.Do(createLicenseReq)
	if err != nil {
		t.Fatalf("Failed to make create license request: %v", err)
	}
	defer createLicenseResp.Body.Close()

	if createLicenseResp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, createLicenseResp.StatusCode)
	}

	var createLicenseResponse map[string]string
	err = json.NewDecoder(createLicenseResp.Body).Decode(&createLicenseResponse)
	if err != nil {
		t.Fatalf("Failed to decode create license response: %v", err)
	}

	v2LicenseKey, ok := createLicenseResponse["license_key"]
	if !ok {
		t.Fatalf("License key not found in create license response")
	}

	// Define test cases
	testCases := []struct {
		name       string
		token      string
		expectExit bool
	}{
		{
			name:       "Valid License Key",
			token:      config.Env.LangSmithLicenseKey,
			expectExit: false,
		},
		{
			name:       "Expired Invalid License Key",
			token:      "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************.Yri-j40nEOHwpIR-jxGl4OEMUlJ2r3JT1qHowlHiSTrAVu9PeXuqWy7-0pnf08gLIlQ2C7dtAkn0rwIDP8ZNTJbU1MmWQR6f8j3w981ZbGJKcyHkc35cyaRTNQE7IU1ydPhccq4hDaVXAxUC8bNlA6Jq0ApiX8X4Ik5Gb1c-kqHwVmP_Olip7-uG96_-VknEDXPuxhnUdfqL7eiQ7xkimKlBs6DDVvnbijFa6H5QSq2F-PbMxoEW819x9A-WrQSChvG4ZViN1nzQEkyYuDojKCCcU6R-sqw4gsjKLHOuRSwHZSisaZKxIbSEqFunMCRXknW9vHlcv-dH4kA7evsCCGxwJLFVF2qqvDolSDy2jNRIcWxETThVwKbmbPv8PWBa4-FqH6W_E8zZoPF65sog3cbRLrIa3XwUqF8FHuOmd2HHAa2xiQabLjlyUU7rvbl1fZV7dsbi_v9hQCwQmo2Kjwg_06M_uxcC2E4GyQxPgsusWEBHS0BHA_rzXVhzVlmSzOZzIUheOUVzTfnCtuX0U-j0IYjD0goHCZ8_lO01tQaawsBsNCk_QlZ_lhY88hDNefRKqd0MXwCSDVYxWs10Xik7qyuqW52J1VPAw3z1G8-AxiOzQbx6N_AseXDF-upvwcVunh8YlVp0Y8Kh-VHV3To42R_8-Jpo2Bo6JJDFqmw",
			expectExit: true,
		},
		{
			name:       "Invalid License Key",
			token:      "foo",
			expectExit: true,
		},
		{
			name:       "v2 License Key",
			token:      v2LicenseKey,
			expectExit: false,
		},
		{
			name:       "invalid v2 License Key",
			token:      "lcl_foo",
			expectExit: true,
		},
	}

	// Run test cases
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a channel to capture exit calls
			exitChan := make(chan int)

			// Define a custom exit function
			exitFunc := func(code int) {
				exitChan <- code
			}
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()
			StartLicenseVerification(ctx, tc.token, exitFunc, redisPool, 100*time.Millisecond)

			// Wait for the expected outcome
			select {
			case code := <-exitChan:
				if !tc.expectExit {
					t.Errorf("Unexpected exit with code %d", code)
				}
			case <-time.After(1 * time.Second):
				if tc.expectExit {
					t.Error("Expected exit function to be called")
				}
			}
			cancel()
		})
	}
}
