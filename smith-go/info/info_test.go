package info

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil/leak"
)

func TestInfoHandler(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	redisPool := lsredis.CachingRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	// Create a new chi router
	r := chi.NewRouter()

	// Mount the middleware and handlers
	r.Route("/v1/platform", func(r chi.Router) {
		r.Use(middleware.Timeout(60 * time.Second))

		infoHandler := NewInfoHandler(redisPool)
		r.Get("/info", infoHandler.GetServerInfoHandler)
	})

	// Create a test server using httptest
	ts := httptest.NewServer(r)
	defer ts.Close()

	runInfoTests(t, ts)
}

func runInfoTests(t *testing.T, ts *httptest.Server) {
	t.Run("valid request", func(t *testing.T) {
		resp, err := http.Get(ts.URL + "/v1/platform/info")
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		if cc := resp.Header.Get("Cache-Control"); cc != "public, max-age=60" {
			t.Fatalf("Expected cache-control header %s, got %s", "public, max-age=60", cc)
		}

		// Optionally, check the response body
		var responseData InfoGetResponse
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		if !(responseData.Version == "dev") {
			t.Fatalf("Expected version %s, got %s", "dev", responseData.Version)
		}
		if responseData.LicenseExpirationTime == nil {
			t.Fatalf("Expected license expiration time, got nil")
		}
		if responseData.BatchIngestConfig.UseMultipartEndpoint != config.Env.BatchIngestUseMultipartEndpoint {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestUseMultipartEndpoint, responseData.BatchIngestConfig.UseMultipartEndpoint)
		}
		if responseData.BatchIngestConfig.ScaleUpQsizeTrigger != config.Env.BatchIngestScaleUpQsizeTrigger {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestScaleUpQsizeTrigger, responseData.BatchIngestConfig.ScaleUpQsizeTrigger)
		}
		if responseData.BatchIngestConfig.ScaleUpNthreadsLimit != config.Env.BatchIngestScaleUpNthreadsLimit {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestScaleUpNthreadsLimit, responseData.BatchIngestConfig.ScaleUpNthreadsLimit)
		}
		if responseData.BatchIngestConfig.ScaleDownNemptyTrigger != config.Env.BatchIngestScaleDownNemptyTrigger {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestScaleDownNemptyTrigger, responseData.BatchIngestConfig.ScaleDownNemptyTrigger)
		}
		if responseData.BatchIngestConfig.SizeLimit != config.Env.BatchIngestSizeLimit {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestSizeLimit, responseData.BatchIngestConfig.SizeLimit)
		}
	})
	t.Run("nonlocal has no expiration time", func(t *testing.T) {
		config.Env.LangchainEnv = "prod"
		resp, err := http.Get(ts.URL + "/v1/platform/info")
		if err != nil {
			t.Fatalf("Failed to make request: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
		}

		// Optionally, check the response body
		var responseData InfoGetResponse
		err = json.NewDecoder(resp.Body).Decode(&responseData)
		if err != nil {
			t.Fatalf("Failed to decode response: %v", err)
		}

		if !(responseData.Version == "dev") {
			t.Fatalf("Expected version %s, got %s", "dev", responseData.Version)
		}
		if responseData.LicenseExpirationTime != nil {
			t.Fatalf("Expected nil license expiration time, got %v", responseData.LicenseExpirationTime)
		}
		if responseData.BatchIngestConfig.UseMultipartEndpoint != config.Env.BatchIngestUseMultipartEndpoint {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestUseMultipartEndpoint, responseData.BatchIngestConfig.UseMultipartEndpoint)
		}
		if responseData.BatchIngestConfig.ScaleUpQsizeTrigger != config.Env.BatchIngestScaleUpQsizeTrigger {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestScaleUpQsizeTrigger, responseData.BatchIngestConfig.ScaleUpQsizeTrigger)
		}
		if responseData.BatchIngestConfig.ScaleUpNthreadsLimit != config.Env.BatchIngestScaleUpNthreadsLimit {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestScaleUpNthreadsLimit, responseData.BatchIngestConfig.ScaleUpNthreadsLimit)
		}
		if responseData.BatchIngestConfig.ScaleDownNemptyTrigger != config.Env.BatchIngestScaleDownNemptyTrigger {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestScaleDownNemptyTrigger, responseData.BatchIngestConfig.ScaleDownNemptyTrigger)
		}
		if responseData.BatchIngestConfig.SizeLimit != config.Env.BatchIngestSizeLimit {
			t.Fatalf("Expected %v, got %v", config.Env.BatchIngestSizeLimit, responseData.BatchIngestConfig.SizeLimit)
		}
	})
}
