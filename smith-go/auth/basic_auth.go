package auth

import (
	"context"
	"crypto/subtle"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	lsredis "langchain.com/smith/redis"

	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/jwtauth/v5"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"golang.org/x/crypto/scrypt"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
)

// These are basically the same as auth.go's implementation except without user or provider user upsert,
// since those records are created/updated as part of the user management flow
// rather than from external JWTs.
const getOrgInfoForBasicAuthUserQuery = `
WITH org_permissions AS (
    SELECT ARRAY(
        SELECT permission
        FROM role_permissions rp
        WHERE rp.role_id = i.role_id
        ORDER BY permission
    ) AS permissions,
    i.id as identity_id,
    i.read_only
    FROM identities i
    WHERE i.ls_user_id = $1
      AND i.access_scope = 'organization'
      AND i.organization_id = $2
),
provider_user_info AS (
    SELECT
        id,
        ls_user_id,
        email,
        full_name
    FROM provider_users
    WHERE ls_user_id = $1
),
user_info AS (
	SELECT id, ls_user_id FROM users WHERE ls_user_id = $1
)
SELECT
    $2 as organization_id,
    o.is_personal,
    coalesce(o.metronome_customer_id, '') as metronome_customer_id,
    coalesce(o.stripe_customer_id, '') as stripe_customer_id,
    op.identity_id as organization_identity_id,
    coalesce(op.read_only, false) as organization_identity_read_only,
    coalesce(op.permissions, ARRAY[]::text[]) as permissions,
    o.config,
    o.disabled as organization_disabled,
	o.public_sharing_disabled as public_sharing_disabled,
	o.sso_only as sso_only,
    ui.id as user_id,
    $1 as ls_user_id,
    u.email as user_email,
    coalesce(u.full_name, '') as user_full_name,
	false as is_sso_user,
    '' as default_tenant_id,
	'' as service_identity
FROM organizations o
LEFT JOIN org_permissions op ON o.id = $2
LEFT JOIN provider_user_info u ON o.id = $2
LEFT JOIN user_info ui ON ui.ls_user_id = u.ls_user_id
WHERE o.id = $2
`

const listTenantsAndOrgsForBasicAuthUserQuery = `
SELECT
	u.id,
	u.ls_user_id,
	u.email,
	coalesce(u.full_name, ''),
	false,
	array(
		SELECT t.id FROM identities i
		INNER JOIN tenants t ON i.tenant_id = t.id
		WHERE i.ls_user_id = $1 AND i.access_scope = 'workspace'
		ORDER BY t.id
	) as tenant_ids,
	array(
		SELECT o.id FROM identities i
		INNER JOIN organizations o ON i.organization_id = o.id
		WHERE i.ls_user_id = $1 AND i.access_scope = 'organization'
		ORDER BY o.id
	) as organization_ids
FROM users u
WHERE u.ls_user_id = $1
`

const ListUsersQuery = `
SELECT
	u.id,
	u.ls_user_id,
	u.email,
	coalesce(u.full_name, ''),
	coalesce(u.hashed_password, '')
FROM users u
WHERE u.email = $1`

type HandlerBasicAuth struct {
	Pg              *database.AuditLoggedPool
	Jwt             *jwtauth.JWTAuth
	ApiKey          *HandlerApiKey
	XServiceKeyAuth *HandlerXServiceKeyAuth
	cache           *lsredis.Cache[uint64, *AuthInfo]
	orgCache        *lsredis.Cache[uint64, *OrgAuthInfo]
}

func NewBasicAuth(pg *database.AuditLoggedPool, redisClient redis.UniversalClient, cacheTTLSecs int) *HandlerBasicAuth {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	cache := lsredis.NewCache[uint64, *AuthInfo](redisClient, "authInfo", ttl)
	orgCache := lsredis.NewCache[uint64, *OrgAuthInfo](redisClient, "orgAuthInfo", ttl)
	return &HandlerBasicAuth{
		Pg:              pg,
		ApiKey:          &HandlerApiKey{pg, cache, orgCache},
		Jwt:             jwtauth.New("HS256", []byte(config.Env.BasicAuthJwtSecret), nil),
		XServiceKeyAuth: NewHandlerXServiceKeyAuth(pg, cache, orgCache),
		cache:           cache,
		orgCache:        orgCache,
	}
}

func ComputeAndCompareHashedPasswords(hashedPassword string, password string) error {
	// Returns nil if plaintext password matches hashed password.
	// Hashed password is in format <n>$<r>$<p>$<salt_b64>$<hash_hex>
	parts := strings.Split(hashedPassword, "$")
	if len(parts) != 5 {
		return errors.New("hashedPassword does not have the correct format")
	}

	// Parse scrypt parameters
	n, err := strconv.Atoi(parts[0])
	if err != nil {
		return err
	}
	r, err := strconv.Atoi(parts[1])
	if err != nil {
		return err
	}
	p, err := strconv.Atoi(parts[2])
	if err != nil {
		return err
	}

	// Decode the base64 salt
	salt, err := base64.StdEncoding.DecodeString(parts[3])
	if err != nil {
		return err
	}

	// Decode the hex-encoded hash
	expectedHash, err := hex.DecodeString(parts[4])
	if err != nil {
		return err
	}

	// Generate scrypt hash from the password provided
	derivedKey, err := scrypt.Key([]byte(password), salt, n, r, p, len(expectedHash))
	if err != nil {
		return err
	}

	if subtle.ConstantTimeCompare(expectedHash, derivedKey) == 1 {
		return nil
	}

	return errors.New("hashedPassword does not match the given password")
}

// Validate JWT, tenant access, and permissions
func (h *HandlerBasicAuth) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(r.Context())
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.Middleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.Middleware(next).ServeHTTP(w, r)
			return
		}

		// get tenant id header
		tenantId := r.Header.Get("X-Tenant-Id")
		if tenantId == "" {
			oplog.Warn("Missing tenant ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		authorization := r.Header.Get("Authorization")
		if authorization == "" || !strings.HasPrefix(authorization, "Bearer ") {
			oplog.Warn("Invalid or empty authorization header")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		authorization = authorization[len("Bearer "):]

		// verify jwt
		auth, err := h.cache.GetFresh(
			ctx,
			lsredis.StringToHash(getOrgInfoForBasicAuthUserQuery, getTenantInfoForUserQuery, authorization, tenantId),
			func() (*AuthInfo, error) {
				token, err := jwtauth.VerifyToken(h.Jwt, authorization)
				if err != nil {
					oplog.Error("JWT verification error", "err", err)
					return nil, err
				}
				userId := token.Subject()
				ctx = config.LogAndContextSetField(ctx, "user_id", slog.StringValue(userId))
				var email string
				if value, ok := token.Get("email"); ok {
					if value, ok := value.(string); ok {
						email = value
					}
				}
				ctx = config.LogAndContextSetField(ctx, "email", slog.StringValue(email))

				// validate access by retrieving org info then tenant info in one transaction
				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
				if err != nil {
					oplog.Error("Failed creating transaction", "err", err)
					return nil, err
				}
				defer func() {
					if err != nil {
						tx.Rollback(r.Context())
						return
					}
					err = tx.Commit(r.Context()) // commit, set return error here
				}()

				// retrieve org ID from tenant ID if not provided
				organizationId := r.Header.Get("X-Organization-Id")
				var resolvedOrgId string
				if organizationId == "" {
					orgIdRow, _ := tx.Query(
						ctx,
						getOrgIdFromTenantIdQuery,
						tenantId,
					)
					if orgId, err := pgx.CollectExactlyOneRow(orgIdRow, pgx.RowTo[string]); err == nil {
						resolvedOrgId = orgId
					} else if err != nil || resolvedOrgId == "" {
						oplog.Warn("No organization found", "err", err)
						return nil, err // unknown error (unable to find org), not cached
					}
				} else {
					resolvedOrgId = organizationId
				}

				providerUserRows, _ := tx.Query(
					ctx,
					ListProvidersQuery,
					nil,
					email,
					"email",
					nil,
				)
				providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
				if err == pgx.ErrNoRows {
					oplog.Info("No login method found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Error("pgx error collecting provider user info", "err", err)
					return nil, err // unknown error, not cached
				}
				lsUserId := providerInfo.LSUserID
				ctx = config.LogAndContextSetField(ctx, "ls_user_id", slog.StringValue(lsUserId))

				// org info
				orgRows, _ := tx.Query(
					ctx,
					getOrgInfoForBasicAuthUserQuery,
					lsUserId,
					resolvedOrgId,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					oplog.Info("No user found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Error("pgx error collecting org auth info", "err", err)
					return nil, err // unknown error, not cached
				}

				// tenant info
				rows, _ := tx.Query(
					ctx,
					getTenantInfoForUserQuery,
					lsUserId,
					tenantId,
				)
				tenantAuth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[TenantAuthInfo])
				if err == pgx.ErrNoRows {
					oplog.Warn("No identity found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Error("pgx error collecting workspace auth info", "err", err)
					return nil, err // unknown error, not cached
				}

				auth := &AuthInfo{
					OrganizationID:                  orgAuth.OrganizationID,
					OrganizationIsPersonal:          orgAuth.OrganizationIsPersonal,
					OrganizationMetronomeCustomerId: orgAuth.OrganizationMetronomeCustomerId,
					OrganizationStripeCustomerId:    orgAuth.OrganizationStripeCustomerId,
					OrganizationIdentityID:          orgAuth.IdentityID,
					OrganizationIdentityReadOnly:    orgAuth.IdentityReadOnly,
					OrganizationPermissions:         orgAuth.OrganizationPermissions,
					OrganizationConfig:              orgAuth.OrganizationConfig,
					OrganizationDisabled:            orgAuth.OrganizationDisabled,
					PublicSharingDisabled:           orgAuth.PublicSharingDisabled,
					SsoOnly:                         orgAuth.SsoOnly,
					UserID:                          orgAuth.UserID,
					LSUserID:                        lsUserId,
					UserEmail:                       orgAuth.UserEmail,
					UserFullName:                    orgAuth.UserFullName,
					TenantID:                        tenantAuth.TenantID,
					TenantHandle:                    tenantAuth.TenantHandle,
					TenantIsDeleted:                 tenantAuth.TenantIsDeleted,
					TenantConfig:                    tenantAuth.TenantConfig,
					TenantIdentityID:                tenantAuth.IdentityID,
					TenantIdentityReadOnly:          tenantAuth.IdentityReadOnly,
					IdentityPermissions:             tenantAuth.IdentityPermissions,
				}

				InitializeAndUnmarshalConfig(auth)

				return auth, nil
			},
		)
		if err != nil || auth == nil {
			oplog.Error("error", "err", err)
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerBasicAuth) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r, "X-Tenant-Id", "Authorization", "X-Api-Key", "X-Service-Key")
}

func (h *HandlerBasicAuth) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	if r.Header.Get("X-Api-Key") != "" {
		h.ApiKey.GetTenantlessAuth(w, r)
		return
	}

	token, err := jwtauth.VerifyRequest(h.Jwt, r, jwtauth.TokenFromHeader)
	if err != nil {
		oplog.Error("error verifying JWT", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	userId := token.Subject()
	ctx = config.LogAndContextSetField(ctx, "user_id", slog.StringValue(userId))

	// extract fields
	var email string
	if value, ok := token.Get("email"); ok {
		if value, ok := value.(string); ok {
			email = value
		}
	}
	ctx = config.LogAndContextSetField(ctx, "email", slog.StringValue(email))

	tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
	if err != nil {
		oplog.Error("Failed creating transaction", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	defer func() {
		if err != nil {
			tx.Rollback(r.Context())
			return
		}
		err = tx.Commit(r.Context()) // commit, set return error here
	}()

	providerUserRows, _ := tx.Query(
		ctx,
		ListProvidersQuery,
		nil,
		email,
		"email",
		nil,
	)
	providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
	if err == pgx.ErrNoRows {
		oplog.Info("No login method found")
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	} else if err != nil {
		oplog.Info("pgx error collecting provider user info", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	lsUserId := providerInfo.LSUserID
	ctx = config.LogAndContextSetField(ctx, "ls_user_id", slog.StringValue(lsUserId))

	// validate access
	rows, _ := tx.Query(
		ctx,
		listTenantsAndOrgsForBasicAuthUserQuery,
		lsUserId,
	)
	auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AllTenantsAuthInfo])
	if err != nil {
		oplog.Error("pgx error collecting auth info", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	if email != auth.UserEmail {
		oplog.Warn("mismatched emails", "jwtEmail", email, "dbEmail", auth.UserEmail)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

// Check basic auth credentials and issue JWT if valid
func (h *HandlerBasicAuth) Login(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	authorization := r.Header.Get("Authorization")
	if authorization == "" {
		oplog.Info("No authorization header")
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}

	email, password, ok := r.BasicAuth()
	if !ok {
		oplog.Warn("Decoding basic auth header failed")
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}

	// validate access
	// TODO: change sub to ls_user_id, look for login method (provider_users) instead of users record
	rows, _ := h.Pg.Query(
		ctx,
		ListUsersQuery,
		email,
	)
	userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[UserInfoWithHashedPassword])
	if err == pgx.ErrNoRows {
		oplog.Info("No user found")
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	} else if err != nil {
		oplog.Error("pgx error finding users", "err", err)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}

	passwordCheckErr := ComputeAndCompareHashedPasswords(userInfo.UserHashedPassword, password)
	if passwordCheckErr != nil {
		oplog.Warn("password check error: ", "err", passwordCheckErr)
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}

	claims := map[string]interface{}{
		"aud":       "authenticated",
		"sub":       userInfo.UserID,
		"email":     userInfo.UserEmail,
		"full_name": userInfo.UserFullName,
	}
	jwtauth.SetExpiryIn(claims, time.Duration(config.Env.BasicAuthJwtExpirationSeconds)*time.Second)
	_, tokenString, err := h.Jwt.Encode(claims)
	if err != nil {
		oplog.Error("JWT encoding failed", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}

	accessTokenInfo := &AccessTokenInfo{AccessToken: tokenString}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, accessTokenInfo)
}

func (h *HandlerBasicAuth) OrgMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(r.Context())
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		organizationId := r.Header.Get("X-Organization-Id")
		if organizationId == "" {
			oplog.Info("Missing organization ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		authorization := r.Header.Get("Authorization")
		if authorization == "" {
			oplog.Info("Invalid or empty authorization header")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		authorization = authorization[len("Bearer "):]

		// verify organization access
		auth, err := h.orgCache.GetFresh(
			ctx,
			lsredis.StringToHash(getOrgInfoForBasicAuthUserQuery, r.Header.Get("Authorization"), organizationId),
			func() (*OrgAuthInfo, error) {
				token, err := jwtauth.VerifyToken(h.Jwt, authorization)
				if err != nil {
					oplog.Error("JWT verification error", "err", err)
					return nil, err
				}
				userId := token.Subject()
				ctx = config.LogAndContextSetField(ctx, "user_id", slog.StringValue(userId))
				var email string
				if value, ok := token.Get("email"); ok {
					if value, ok := value.(string); ok {
						email = value
					}
				}
				ctx = config.LogAndContextSetField(ctx, "email", slog.StringValue(email))

				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
				if err != nil {
					return nil, err
				}
				defer func() {
					if err != nil {
						tx.Rollback(ctx)
						return
					}
					err = tx.Commit(ctx) // commit, set return error here
				}()

				providerUserRows, _ := tx.Query(
					ctx,
					ListProvidersQuery,
					nil,
					email,
					"email",
					nil,
				)
				providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
				if err == pgx.ErrNoRows {
					oplog.Info("No login method found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Info("pgx error collecting provider user info", "err", err)
					return nil, err // unknown error, not cached
				}
				lsUserId := providerInfo.LSUserID
				ctx = config.LogAndContextSetField(ctx, "ls_user_id", slog.StringValue(lsUserId))

				// validate access
				orgRows, _ := tx.Query(
					ctx,
					getOrgInfoForBasicAuthUserQuery,
					lsUserId,
					organizationId,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					oplog.Info("No user found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Info("pgx error collecting org auth info", "err", err)
					return nil, err // unknown error, not cached
				}

				InitializeAndUnmarshalOrgConfig(orgAuth)

				return orgAuth, nil
			},
		)
		if err != nil {
			oplog.Info("error retrieving org auth info", "err", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerBasicAuth) XServiceKeyMiddleware(next http.Handler) http.Handler {
	return h.XServiceKeyAuth.XServiceKeyMiddleware(next)
}

func (h *HandlerBasicAuth) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalAuth(w, r)
}

func (h *HandlerBasicAuth) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalOrgAuth(w, r)
}
