package auth_test

import (
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/gofrs/uuid"
	"github.com/gorilla/sessions"
	"github.com/jackc/pgx/v5"
	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/rbcervilla/redisstore/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

const oauthSessionedEmail = "<EMAIL>"
const oauthSessionedUserName = "hello"
const oauthSessionedUserId = "12345"

var oauthSessionedUserUuid = uuid.NewV5(uuid.NamespaceOID, oauthSessionedUserId).String()

func TestAuthHandlerOAuthSessioned_MiddlewareApiKey(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewOAuthSessioned(dbpool, redisPool, 0, 30)

	runApiKeyMiddlewareTests(t, dbpool, ah, true)
}

func setupUserAuthWithExpiry(duration time.Duration) {
	gothic.CompleteUserAuth = func(res http.ResponseWriter, req *http.Request) (goth.User, error) {
		return goth.User{
			UserID:    oauthSessionedUserId,
			Email:     oauthSessionedEmail,
			Name:      oauthSessionedUserName,
			ExpiresAt: time.Now().Add(duration),
		}, nil
	}
}

func setupMockUserAuth() {
	setupUserAuthWithExpiry(24 * time.Hour)
}

func setupExpiredUserAuth() {
	setupUserAuthWithExpiry(-24 * time.Hour)
}

func setupSession(t *testing.T, ah *auth.HandlerOAuthSessioned) (auth.StoredOAuthUserInfo, *http.Cookie) {
	w := httptest.NewRecorder()
	r := httptest.NewRequest("GET", "/", nil)
	rctx := chi.NewRouteContext()
	rctx.URLParams.Add("provider", "custom-oidc")
	r = r.WithContext(context.WithValue(r.Context(), chi.RouteCtxKey, rctx))
	ah.Callback(w, r)
	assert.Equal(t, http.StatusTemporaryRedirect, w.Code)
	locationHeader := w.Header().Get("Location")
	assert.Contains(t, locationHeader, "/oauth-callback?user=")
	parts := strings.Split(locationHeader, "?user=")
	assert.NotNil(t, parts)
	userData := parts[len(parts)-1]
	decoded, err := base64.StdEncoding.DecodeString(userData)
	assert.NoError(t, err)
	var user auth.StoredOAuthUserInfo
	err = json.Unmarshal([]byte(decoded), &user)
	assert.NoError(t, err)
	cookies := w.Result().Cookies()
	var cookie *http.Cookie
	for _, c := range cookies {
		if c.Name == auth.UserSessionCookieName {
			cookie = c
			break
		}
	}
	assert.NotNil(t, cookie)
	return user, cookie
}

func TestAuthHandlerOAuthSessioned_Callback(t *testing.T) {
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewOAuthSessioned(dbpool, redisPool, 0, 30)

	t.Run("user insert", func(t *testing.T) {
		setupMockUserAuth()
		user, _ := setupSession(t, ah)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			oauthSessionedEmail,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: oauthSessionedUserUuid, LSUserID: user.LSUserID, UserEmail: oauthSessionedEmail, UserFullName: oauthSessionedUserName, UserHashedPassword: ""},
			userInfo,
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)

		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "custom-oidc", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: oauthSessionedUserUuid, Valid: true},
			Email:          sql.NullString{String: oauthSessionedEmail, Valid: true},
			FullName:       sql.NullString{String: oauthSessionedUserName, Valid: true},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("user upsert", func(t *testing.T) {
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)

		// execute callback with updated user info
		newEmail := "<EMAIL>"
		newName := "changed"
		gothic.CompleteUserAuth = func(res http.ResponseWriter, req *http.Request) (goth.User, error) {
			return goth.User{
				UserID:    oauthSessionedUserId,
				Email:     newEmail,
				Name:      newName,
				ExpiresAt: time.Now().Add(24 * time.Hour),
			}, nil
		}
		setupSession(t, ah)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			newEmail,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: oauthSessionedUserUuid, LSUserID: providerInfo.LSUserID, UserEmail: newEmail, UserFullName: newName, UserHashedPassword: ""},
			userInfo,
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)

		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "custom-oidc", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: oauthSessionedUserUuid, Valid: true},
			Email:          sql.NullString{String: newEmail, Valid: true},
			FullName:       sql.NullString{String: newName, Valid: true},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("link to existing basic auth", func(t *testing.T) {
		setupMockUserAuth()
		userId, err := uuid.NewV4()
		assert.NoError(t, err)
		userIdStr := userId.String()
		basicAuthUser := UserSetup(t, dbpool, userIdStr, oauthSessionedEmail, oauthSessionedUserName, "email", nil, nil)
		setupSession(t, ah)
		lsUserId := basicAuthUser.LSUserID

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			oauthSessionedEmail,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userIdStr, LSUserID: lsUserId, UserEmail: oauthSessionedEmail, UserFullName: oauthSessionedUserName, UserHashedPassword: ""},
			userInfo,
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)

		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "email", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: "", Valid: false},
				Email:          sql.NullString{String: oauthSessionedEmail, Valid: true},
				FullName:       sql.NullString{String: oauthSessionedUserName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "custom-oidc", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: oauthSessionedUserUuid, Valid: true},
				Email:          sql.NullString{String: oauthSessionedEmail, Valid: true},
				FullName:       sql.NullString{String: oauthSessionedUserName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("auths work after linking", func(t *testing.T) {
		// create a basic auth user and link
		setupMockUserAuth()
		userId, err := uuid.NewV4()
		assert.NoError(t, err)
		userIdStr := userId.String()
		basicAuthUser := UserSetup(t, dbpool, userIdStr, oauthSessionedEmail, oauthSessionedUserName, "email", nil, nil)
		setupSession(t, ah)
		lsUserId := basicAuthUser.LSUserID

		// set up access
		orgId := OrgSetup(t, dbpool, "test org", false, userIdStr)
		wsId := TenantSetup(t, dbpool, orgId, "test tenant", "yo", &auth.TenantConfig{}, true)
		orgIdentityId := IdentitySetup(t, dbpool, userIdStr, orgId, nil, "ORGANIZATION_ADMIN", "organization", nil, lsUserId)
		wsIdentityId := IdentitySetup(t, dbpool, userIdStr, orgId, &wsId, "WORKSPACE_ADMIN", "workspace", &orgIdentityId, lsUserId)

		_, cookie := setupSession(t, ah)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.AddCookie(cookie)
		r.Header.Set("X-Organization-ID", orgId)

		expectedOrgAuthInfo := &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          oauthSessionedUserUuid,
			LSUserID:                        lsUserId,
			UserEmail:                       oauthSessionedEmail,
			UserFullName:                    oauthSessionedUserName,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}

		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.Equal(t, expectedOrgAuthInfo, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", wsId)
		r.AddCookie(cookie)

		expectedWsAuthInfo := &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                wsId,
			TenantHandle:            "yo",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        wsIdentityId,
			TenantIdentityReadOnly:  false,
			UserID:                  oauthSessionedUserUuid,
			LSUserID:                lsUserId,
			UserEmail:               oauthSessionedEmail,
			UserFullName:            oauthSessionedUserName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}

		called = false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.Equal(t, expectedWsAuthInfo, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		expectedAllTenantsAuthInfo := &auth.AllTenantsAuthInfo{
			UserID:          oauthSessionedUserUuid,
			LSUserID:        lsUserId,
			UserEmail:       oauthSessionedEmail,
			UserFullName:    oauthSessionedUserName,
			TenantIDs:       []string{wsId},
			OrganizationIds: []string{orgId},
		}

		called = false
		ah.GetTenantlessAuth(w, r)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())
		// get the json and assert it's equal to expected
		body, err := io.ReadAll(w.Body)
		assert.NoError(t, err)
		allTenantsAuthInfo := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, allTenantsAuthInfo))
		assert.Equal(t, expectedAllTenantsAuthInfo, allTenantsAuthInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("update email of linked login methods", func(t *testing.T) {
		setupMockUserAuth()
		userId, err := uuid.NewV4()
		assert.NoError(t, err)
		userIdStr := userId.String()
		basicAuthUser := UserSetup(t, dbpool, userIdStr, oauthSessionedEmail, oauthSessionedUserName, "email", nil, nil)
		setupSession(t, ah)
		lsUserId := basicAuthUser.LSUserID

		// execute callback with updated user info
		newEmail := "<EMAIL>"
		newName := "changed"
		gothic.CompleteUserAuth = func(res http.ResponseWriter, req *http.Request) (goth.User, error) {
			return goth.User{
				UserID:    oauthSessionedUserId,
				Email:     newEmail,
				Name:      newName,
				ExpiresAt: time.Now().Add(24 * time.Hour),
			}, nil
		}
		setupSession(t, ah)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			newEmail,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userIdStr, LSUserID: lsUserId, UserEmail: newEmail, UserFullName: newName, UserHashedPassword: ""},
			userInfo,
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)

		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "email", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: "", Valid: false},
				Email:          sql.NullString{String: newEmail, Valid: true},
				FullName:       sql.NullString{String: newName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "custom-oidc", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: oauthSessionedUserUuid, Valid: true},
				Email:          sql.NullString{String: newEmail, Valid: true},
				FullName:       sql.NullString{String: newName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("large session should not error", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		store, err := redisstore.NewRedisStore(context.Background(), redisPool)
		assert.NoError(t, err)
		session := sessions.NewSession(store, "test")
		session.Values = map[interface{}]interface{}{
			"test": strings.Repeat("a", 10000),
		}
		err = store.Save(r, w, session)
		assert.NoError(t, err)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
}

func TestAuthHandlerOAuthSessioned_WSMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewOAuthSessioned(dbpool, redisPool, 0, 30)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing session", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("expired session", func(t *testing.T) {
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		wsId := TenantSetup(t, dbpool, orgId, "test tenant", "yo", &auth.TenantConfig{MaxIdentities: util.IntPtr(4)}, false)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		orgIdentityId := IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, &wsId, "WORKSPACE_VIEWER", "workspace", &orgIdentityId, providerInfo.LSUserID)

		// setup session
		gothic.CompleteUserAuth = func(res http.ResponseWriter, req *http.Request) (goth.User, error) {
			return goth.User{
				UserID:    oauthSessionedUserId,
				Email:     oauthSessionedEmail,
				Name:      oauthSessionedUserName,
				ExpiresAt: time.Now().Add(-24 * time.Hour),
			}, nil
		}
		_, cookie := setupSession(t, ah)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.AddCookie(cookie)
		r.Header.Set("X-Organization-ID", orgId)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("present with admin permissions", func(t *testing.T) {
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		wsId := TenantSetup(t, dbpool, orgId, "test tenant", "yo", &auth.TenantConfig{}, true)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		orgIdentityId := IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)
		wsIdentityId := IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, &wsId, "WORKSPACE_ADMIN", "workspace", &orgIdentityId, providerInfo.LSUserID)

		// setup session
		setupMockUserAuth()
		_, cookie := setupSession(t, ah)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", wsId)
		r.AddCookie(cookie)

		expectedAuthInfo := &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                wsId,
			TenantHandle:            "yo",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        wsIdentityId,
			TenantIdentityReadOnly:  false,
			UserID:                  oauthSessionedUserUuid,
			LSUserID:                providerInfo.LSUserID,
			UserEmail:               oauthSessionedEmail,
			UserFullName:            oauthSessionedUserName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}

		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.Equal(t, expectedAuthInfo, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", wsId)
		req.AddCookie(cookie)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.Equal(t, expectedAuthInfo, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
}

func TestAuthHandlerOAuthSessioned_OrgMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewOAuthSessioned(dbpool, redisPool, 0, 30)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing session", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("expired session", func(t *testing.T) {
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)

		// setup session
		gothic.CompleteUserAuth = func(res http.ResponseWriter, req *http.Request) (goth.User, error) {
			return goth.User{
				UserID:    oauthSessionedUserId,
				Email:     oauthSessionedEmail,
				Name:      oauthSessionedUserName,
				ExpiresAt: time.Now().Add(-24 * time.Hour),
			}, nil
		}
		_, cookie := setupSession(t, ah)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.AddCookie(cookie)
		r.Header.Set("X-Organization-ID", orgId)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("present with org admin permissions", func(t *testing.T) {
		setupMockUserAuth()
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		orgIdentityId := IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_ADMIN", "organization", nil, providerInfo.LSUserID)
		_, cookie := setupSession(t, ah)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.AddCookie(cookie)
		r.Header.Set("X-Organization-ID", orgId)

		expectedAuthInfo := &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          oauthSessionedUserUuid,
			LSUserID:                        providerInfo.LSUserID,
			UserEmail:                       oauthSessionedEmail,
			UserFullName:                    oauthSessionedUserName,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}

		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.Equal(t, expectedAuthInfo, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
}

func TestAuthHandlerOAuthSessioned_GetTenantlessAuth(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewOAuthSessioned(dbpool, redisPool, 0, 30)

	t.Run("missing session", func(t *testing.T) {
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusUnauthorized, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		assert.Equal(t, []byte(`{"error":"Unauthorized"}
`), body)
	})

	t.Run("expired session", func(t *testing.T) {
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		wsId := TenantSetup(t, dbpool, orgId, "test tenant", "yo", &auth.TenantConfig{MaxIdentities: util.IntPtr(4)}, false)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		orgIdentityId := IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, &wsId, "WORKSPACE_VIEWER", "workspace", &orgIdentityId, providerInfo.LSUserID)

		// setup session
		gothic.CompleteUserAuth = func(res http.ResponseWriter, req *http.Request) (goth.User, error) {
			return goth.User{
				UserID:    oauthSessionedUserId,
				Email:     oauthSessionedEmail,
				Name:      oauthSessionedUserName,
				ExpiresAt: time.Now().Add(-24 * time.Hour),
			}, nil
		}
		_, cookie := setupSession(t, ah)

		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		req.AddCookie(cookie)
		assert.NoError(t, err)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusUnauthorized, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		assert.Equal(t, []byte(`{"error":"Unauthorized"}
`), body)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("multiple tenants found", func(t *testing.T) {
		setupMockUserAuth()
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		ws1Id := TenantSetup(t, dbpool, orgId, "test tenant", "yo", &auth.TenantConfig{MaxIdentities: util.IntPtr(4)}, false)
		ws2Id := TenantSetup(t, dbpool, orgId, "test tenant2", "yo2", &auth.TenantConfig{MaxIdentities: util.IntPtr(4)}, true)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		orgIdentityId := IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, &ws1Id, "WORKSPACE_VIEWER", "workspace", &orgIdentityId, providerInfo.LSUserID)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, &ws2Id, "WORKSPACE_VIEWER", "workspace", &orgIdentityId, providerInfo.LSUserID)

		_, cookie := setupSession(t, ah)

		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		req.AddCookie(cookie)
		assert.NoError(t, err)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		expectedTenantIDs := []string{ws1Id, ws2Id}
		assert.ElementsMatch(t, expectedTenantIDs, authResponse.TenantIDs)
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          oauthSessionedUserUuid,
			UserEmail:       oauthSessionedEmail,
			UserFullName:    oauthSessionedUserName,
			OrganizationIds: []string{orgId},
		}, &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			OrganizationIds: authResponse.OrganizationIds,
		})

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "custom-oidc", Valid: true},
			LSUserID:       lsUserId,
			ProviderUserID: sql.NullString{String: oauthSessionedUserUuid, Valid: true},
			Email:          sql.NullString{String: oauthSessionedEmail, Valid: true},
			FullName:       sql.NullString{String: oauthSessionedUserName, Valid: true},
		}, providerUserInfo)
	})
}

type CurrentUserResponse struct {
	User string `json:"user"`
}

func TestAuthHandlerOAuthSessioned_GetCurrentUser(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewOAuthSessioned(dbpool, redisPool, 0, 30)

	// returns json auth info
	rtr := chi.NewRouter()
	rtr.Get("/current-user", ah.CurrentUser)
	srv := httptest.NewServer(rtr)
	defer srv.Close()

	t.Run("missing session", func(t *testing.T) {
		req, err := http.NewRequest("GET", srv.URL+"/current-user", nil)
		assert.NoError(t, err)

		// setup session
		setupMockUserAuth()
		//_, cookie := setupSession(t, ah)
		//req.AddCookie(cookie)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusUnauthorized, res.StatusCode)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("expired session", func(t *testing.T) {
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)

		// setup session
		setupExpiredUserAuth()
		req, err := http.NewRequest("GET", srv.URL+"/current-user", nil)
		assert.NoError(t, err)

		_, cookie := setupSession(t, ah)
		req.AddCookie(cookie)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusUnauthorized, res.StatusCode)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("valid session", func(t *testing.T) {
		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)

		// setup session
		setupMockUserAuth()
		req, err := http.NewRequest("GET", srv.URL+"/current-user", nil)
		assert.NoError(t, err)

		_, cookie := setupSession(t, ah)
		req.AddCookie(cookie)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)

		var encodedUser CurrentUserResponse
		err = json.Unmarshal(body, &encodedUser)
		assert.NoError(t, err, "Should unmarshal into CurrentUserResponse")

		decodedUser, err := base64.StdEncoding.DecodeString(encodedUser.User)
		assert.NoError(t, err, "Should base64 decode the user field")

		var oauthUser auth.StoredOAuthUserInfo
		err = json.Unmarshal(decodedUser, &oauthUser)
		assert.NoError(t, err, "Should unmarshal into OAuthUserInfo")
		assert.Equal(t, oauthSessionedEmail, oauthUser.Email)
		assert.Equal(t, oauthSessionedUserName, oauthUser.Name)
		assert.True(t, oauthUser.ExpiresAt.After(time.Now()))
		assert.Equal(t, oauthUser.RefreshToken, "")
		assert.Equal(t, oauthUser.AccessToken, "")
	})

	t.Run("valid session with expiry override", func(t *testing.T) {
		// set longer expiry at the handler level
		config.Env.OauthOverrideTokenExpiry = true
		expirySeconds := 9000
		ah = auth.NewOAuthSessioned(dbpool, redisPool, 0, expirySeconds)

		orgId := OrgSetup(t, dbpool, "test org", false, oauthSessionedUserUuid)
		providerInfo := UserSetup(t, dbpool, oauthSessionedUserUuid, oauthSessionedEmail, oauthSessionedUserName, "custom-oidc", nil, &oauthSessionedUserUuid)
		IdentitySetup(t, dbpool, oauthSessionedUserUuid, orgId, nil, "ORGANIZATION_USER", "organization", nil, providerInfo.LSUserID)

		// setup session with short expiry
		setupUserAuthWithExpiry(1 * time.Minute)
		req, err := http.NewRequest("GET", srv.URL+"/current-user", nil)
		assert.NoError(t, err)

		_, cookie := setupSession(t, ah)
		req.AddCookie(cookie)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)

		var encodedUser CurrentUserResponse
		err = json.Unmarshal(body, &encodedUser)
		assert.NoError(t, err, "Should unmarshal into CurrentUserResponse")

		decodedUser, err := base64.StdEncoding.DecodeString(encodedUser.User)
		assert.NoError(t, err, "Should base64 decode the user field")

		// standard verificaiton along with longer expiry check
		var oauthUser auth.StoredOAuthUserInfo
		err = json.Unmarshal(decodedUser, &oauthUser)
		assert.NoError(t, err, "Should unmarshal into OAuthUserInfo")
		assert.Equal(t, oauthSessionedEmail, oauthUser.Email)
		assert.Equal(t, oauthSessionedUserName, oauthUser.Name)
		assert.True(t, oauthUser.ExpiresAt.After(time.Now().Add(time.Duration(expirySeconds-10)*time.Second)))
		assert.Equal(t, oauthUser.RefreshToken, "")
		assert.Equal(t, oauthUser.AccessToken, "")
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
}
