package auth_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

var oauthUserId = "97df50ed-54a8-5862-98c1-61a727d2b900" // this is stable and computed from the email address
const testEmail = "<EMAIL>"
const testName = "hello"

func TestAuthHandlerOAuth_WSMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	mockOauthServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/userinfo":
			if r.Header.Get("Authorization") != "Bearer valid" {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}
			// Write your response here.
			_, _ = w.Write([]byte(`{"sub": "00000000-0000-0000-0000-000000000002", "email": "<EMAIL>", "name": "hello"}`))
		default:
			http.Error(w, "Not found", http.StatusNotFound)
		}
	}))

	defer mockOauthServer.Close()

	mockDiscoveryServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/.well-known/openid-configuration":
			_, _ = w.Write([]byte(`{"userinfo_endpoint": "` + mockOauthServer.URL + `/userinfo"}`))
		default:
			http.Error(w, "Not found", http.StatusNotFound)
		}
	}))
	defer mockDiscoveryServer.Close()

	ah := auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("failed userinfo call", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		r.Header.Set("Authorization", "Bearer invalid")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("present with admin permissions", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant", "{}", "00000000-0000-0000-0000-000000000003", "test org",
			oauthUserId,
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  oauthUserId,
				UserEmail:               testEmail,
				UserFullName:            testName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  oauthUserId,
			UserEmail:               testEmail,
			UserFullName:            testName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("present with read only permissions", func(t *testing.T) {

		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled) select $1, $2, 'yo', $3, id, false from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005", "test tenant", `{"max_identities": 4}`, "00000000-0000-0000-0000-000000000003", "test org",
			oauthUserId,
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000006",
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			tenantConfig := testutil.GetDefaultTenantConfig()
			tenantConfig.MaxIdentities = util.IntPtr(4)
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  true,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000005",
				TenantHandle:            "yo",
				TenantConfig:            tenantConfig,
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  true,
				UserID:                  oauthUserId,
				UserEmail:               testEmail,
				UserFullName:            testName,
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		tenantConfig := testutil.GetDefaultTenantConfig()
		tenantConfig.MaxIdentities = util.IntPtr(4)
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  true,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000005",
			TenantHandle:            "yo",
			TenantConfig:            tenantConfig,
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  true,
			UserID:                  oauthUserId,
			UserEmail:               testEmail,
			UserFullName:            testName,
			IdentityPermissions:     READ_ONLY_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0) // clear the auth cache

	t.Run("present with org admin permissions", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant", "{}", "00000000-0000-0000-0000-000000000003", "test org",
			oauthUserId,
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  oauthUserId,
				UserEmail:               testEmail,
				UserFullName:            testName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  oauthUserId,
			UserEmail:               testEmail,
			UserFullName:            testName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0) // clear the auth cache

	t.Run("present with org admin permissions multiple tenants", func(t *testing.T) {
		tenantId1 := "00000000-0000-0000-0000-000000000001"
		tenant2 := "00000000-0000-0000-0000-000000000009"
		orgId := "00000000-0000-0000-0000-000000000003"
		orgIdentityId := "00000000-0000-0000-0000-000000000005"

		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
	values
		($1, $2, 'yo', $3, (select id from org), false),
		($8, 'test-tenant2', 'yo2', $3, (select id from org), true)
		returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $7, (select ls_user_id from usr)
from ten cross join org`,
			tenantId1,
			"test tenant",
			"{}",
			orgId,
			"test org",
			oauthUserId,
			orgIdentityId,
			tenant2,
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", tenantId1)
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                tenantId1,
				TenantHandle:            "yo",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantId1,
				TenantIdentityReadOnly:  false,
				UserID:                  oauthUserId,
				UserEmail:               testEmail,
				UserFullName:            testName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", tenantId1)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                tenantId1,
			TenantHandle:            "yo",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantId1,
			TenantIdentityReadOnly:  false,
			UserID:                  oauthUserId,
			UserEmail:               testEmail,
			UserFullName:            testName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0) // clear the auth cache

	t.Run("present with org user permissions", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant", "{}", "00000000-0000-0000-0000-000000000003", "test org",
			oauthUserId,
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  oauthUserId,
				UserEmail:               testEmail,
				UserFullName:            testName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  oauthUserId,
			UserEmail:               testEmail,
			UserFullName:            testName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_USER_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})
}

func TestAuthHandlerOAuth_MiddlewareApiKey(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewSupabase(dbpool, redisPool, 0)

	runApiKeyMiddlewareTests(t, dbpool, ah, true)
}

func TestAuthHandlerOAuth_GetTenantlessAuth(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	mockOauthServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/userinfo":
			if r.Header.Get("Authorization") != "Bearer valid" {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}
			// Write your response here.
			_, _ = w.Write([]byte(`{"sub": "00000000-0000-0000-0000-000000000002", "email": "<EMAIL>", "name": "hello"}`))
		default:
			http.Error(w, "Not found", http.StatusNotFound)
		}
	}))

	defer mockOauthServer.Close()

	mockDiscoveryServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/.well-known/openid-configuration":
			_, _ = w.Write([]byte(`{"userinfo_endpoint": "` + mockOauthServer.URL + `/userinfo"}`))
		default:
			http.Error(w, "Not found", http.StatusNotFound)
		}
	}))
	defer mockDiscoveryServer.Close()

	ah := auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)

	t.Run("invalid token", func(t *testing.T) {
		r := httptest.NewRequest("GET", "/current/tenants", nil)
		token := "invalid"
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		assert.Equal(t, []byte(`{"error":"Forbidden"}
`), body)
	})

	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)

	t.Run("multiple tenants found", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000003"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
values
    ($1, $2, 'yo', $3, (select id from org), false),
    ($7, 'test-tenant2', 'yo2', $3, (select id from org), true)
returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005",
			"test tenant",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			oauthUserId,
			"00000000-0000-0000-0000-000000000001",
			"00000000-0000-0000-0000-000000000006",
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          oauthUserId,
			UserEmail:       testEmail,
			UserFullName:    testName,
			TenantIDs:       []string{"00000000-0000-0000-0000-000000000001", "00000000-0000-0000-0000-000000000005"},
			OrganizationIds: []string{"00000000-0000-0000-0000-000000000003"},
		}, authResponse)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "oidc", Valid: true},
			LSUserID:       lsUserId,
			ProviderUserID: sql.NullString{String: oauthUserId, Valid: true},
			Email:          sql.NullString{String: testEmail, Valid: true},
			FullName:       sql.NullString{String: testName, Valid: true},
		}, providerUserInfo)
	})

	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)
	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("identities in multiple orgs", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000001"
		orgId2 := "00000000-0000-0000-0000-000000000002"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id)
				values
					($1, 'test org', true, $3),
					($2, 'test org 2', false, $3)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, '<EMAIL>', 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id)
				select u.ls_user_id, u.email, 'oidc', u.id from usr u
			)

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, organization_id, read_only, role_id, access_scope, ls_user_id)
			select org.id, usr.id, org.id, false, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', usr.ls_user_id
			from org cross join usr`,
			orgId1,
			orgId2,
			oauthUserId,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          oauthUserId,
			UserEmail:       testEmail,
			UserFullName:    testName,
			TenantIDs:       []string{},
			OrganizationIds: []string{orgId1, orgId2},
		}, authResponse)
	})
}

func TestOrgAuthHandlerOauth_OrgMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	mockOauthServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/userinfo":
			if r.Header.Get("Authorization") == "Bearer changed" {
				fmt.Printf("changed")
				_, _ = w.Write([]byte(`{"sub": "00000000-0000-0000-0000-000000000002", "email": "<EMAIL>", "name": "hello changed"}`))
			} else if r.Header.Get("Authorization") != "Bearer valid" {
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}
			// Write your response here.
			_, _ = w.Write([]byte(`{"sub": "00000000-0000-0000-0000-000000000002", "email": "<EMAIL>", "name": "hello"}`))
		default:
			http.Error(w, "Not found", http.StatusNotFound)
		}
	}))

	defer mockOauthServer.Close()

	mockDiscoveryServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/.well-known/openid-configuration":
			_, _ = w.Write([]byte(`{"userinfo_endpoint": "` + mockOauthServer.URL + `/userinfo"}`))
		default:
			http.Error(w, "Not found", http.StatusNotFound)
		}
	}))
	defer mockDiscoveryServer.Close()

	ah := auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("failed userinfo call", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		r.Header.Set("Authorization", "Bearer invalid")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("missing identity", func(t *testing.T) {
		dbpool.Exec(context.Background(), "delete from organizations")

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)
	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("user upsert org admin permissions", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		identityId := "00000000-0000-0000-0000-000000000002"
		userId := "97df50ed-54a8-5862-98c1-61a727d2b900"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id) values ($1, 'test org', $3) returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
			)

			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
			select $2, $3, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr)
			from org`,
			orgId,
			identityId,
			userId,
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// inserts new user record
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       testEmail,
				UserFullName:                    testName,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			testEmail,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: testEmail, UserFullName: testName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)

		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "oidc", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			Email:          sql.NullString{String: testEmail, Valid: true},
			FullName:       sql.NullString{String: testName, Valid: true},
		}, providerUserInfo)

		// upserts user record
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		r.Header.Set("Authorization", "Bearer "+"changed")

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       testEmail,
				UserFullName:                    "hello changed",
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			testEmail,
		)
		assert.NoError(t, err)
		userInfo, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: testEmail, UserFullName: "hello changed", UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)

		providerUserInfo = auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "oidc", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			Email:          sql.NullString{String: testEmail, Valid: true},
			FullName:       sql.NullString{String: "hello changed", Valid: true},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0) // clear the auth cache

	t.Run("present with org admin permissions multiple orgs", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000001"
		orgId2 := "00000000-0000-0000-0000-000000000002"
		userId := "97df50ed-54a8-5862-98c1-61a727d2b900"

		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org', $3),
					($2, 'test org 2', $3)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
			)

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
			select org.id, $3, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr)
			from org`,
			orgId1,
			orgId2,
			userId,
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId1)
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when org present
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId1,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgId1, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       testEmail,
				UserFullName:                    testName,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId1)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId1,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgId1, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       testEmail,
			UserFullName:                    testName,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewOAuth(dbpool, redisPool, mockDiscoveryServer.URL, 0)

	t.Run("present with org user permissions", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		identityId := "00000000-0000-0000-0000-000000000002"
		userId := "97df50ed-54a8-5862-98c1-61a727d2b900"

		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id)
					select $1, 'test org', $3
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'oidc', u.id from usr u
			)

			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
			select $2, $3, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
			from org`,
			orgId,
			identityId,
			userId,
			testEmail,
			testName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		token := "valid"
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when org present
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       testEmail,
				UserFullName:                    testName,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: identityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       testEmail,
			UserFullName:                    testName,
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})
}
