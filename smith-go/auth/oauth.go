package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	lsredis "langchain.com/smith/redis"

	"github.com/go-chi/render"
	"github.com/gofrs/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/database"
)

const authTypeOauth = "oauth"

type OIDCDiscovery struct {
	Issuer                string `json:"issuer"`
	AuthorizationEndpoint string `json:"authorization_endpoint"`
	TokenEndpoint         string `json:"token_endpoint"`
	UserInfoEndpoint      string `json:"userinfo_endpoint"`
	JwksURI               string `json:"jwks_uri"`
}

type UserInfo struct {
	Sub    string `json:"sub"`
	Email  string `json:"email"`
	Name   string `json:"name"`
	UserId string `json:"user_id"`
}

type HandlerOAuth struct {
	Pg              *database.AuditLoggedPool
	OAuthIssuerUrl  string
	ApiKey          *HandlerApiKey
	XServiceKeyAuth *HandlerXServiceKeyAuth

	cache              *lsredis.Cache[uint64, *AuthInfo]
	orgCache           *lsredis.Cache[uint64, *OrgAuthInfo]
	oidcDiscoveryCache *lsredis.Cache[string, *OIDCDiscovery]
}

func NewOAuth(pg *database.AuditLoggedPool, redisClient redis.UniversalClient, oauthIssuerUrl string, cacheTTLSecs int) *HandlerOAuth {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	cache := lsredis.NewCache[uint64, *AuthInfo](redisClient, "authInfo", ttl)
	orgCache := lsredis.NewCache[uint64, *OrgAuthInfo](redisClient, "orgAuthInfo", ttl)
	oidcDiscoveryCache := lsredis.NewCache[string, *OIDCDiscovery](redisClient, "oidcDiscovery", ttl)
	return &HandlerOAuth{
		Pg:                 pg,
		OAuthIssuerUrl:     oauthIssuerUrl,
		ApiKey:             &HandlerApiKey{pg, cache, orgCache},
		XServiceKeyAuth:    NewHandlerXServiceKeyAuth(pg, cache, orgCache),
		cache:              cache,
		oidcDiscoveryCache: oidcDiscoveryCache,
		orgCache:           orgCache,
	}
}

func fetchOIDCDiscoveryInfo(issuerURL string) (*OIDCDiscovery, error) {
	resp, err := http.Get(fmt.Sprintf("%s/.well-known/openid-configuration", issuerURL))
	if err != nil || resp == nil {
		return nil, fmt.Errorf("failed to fetch OIDC discovery info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: got %v", resp.StatusCode)
	}

	var discovery OIDCDiscovery
	if err := json.NewDecoder(resp.Body).Decode(&discovery); err != nil {
		return nil, fmt.Errorf("failed to decode OIDC discovery info: %w", err)
	}

	return &discovery, nil
}

func fetchUserInfo(userInfoUrl, accessToken string) (*UserInfo, error) {
	req, err := http.NewRequest("GET", userInfoUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create user info request: %w", err)
	}
	req.Header.Add("Authorization", "Bearer "+accessToken)
	resp, err := http.DefaultClient.Do(req)
	if err != nil || resp == nil {
		return nil, fmt.Errorf("failed to fetch user info %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: got %v", resp.StatusCode)
	}

	var userInfo UserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("failed to decode userinfo info: %w", err)
	}
	userInfo.Email = strings.ToLower(userInfo.Email)
	userInfo.UserId = uuid.NewV5(uuid.NamespaceOID, userInfo.Email).String()
	return &userInfo, nil
}

func (h *HandlerOAuth) OrgMiddleware(next http.Handler) http.Handler {
	orgInfoQuery := getOrgInfoForNonSupabaseUserQueryByType(authTypeOauth)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		// get organization id header
		organizationId := r.Header.Get("X-Organization-Id")
		if organizationId == "" {
			log.Println("Missing organization ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		accessToken := r.Header.Get("Authorization")
		if accessToken == "" {
			log.Println("Invalid or empty authorization header")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		accessToken = accessToken[len("Bearer "):]

		// verify organization access
		auth, err := h.orgCache.GetFresh(
			ctx,
			lsredis.StringToHash(orgInfoQuery, r.Header.Get("Authorization"), organizationId),
			func() (*OrgAuthInfo, error) {
				// fetch OIDC discovery info
				discovery, err := h.oidcDiscoveryCache.GetFresh(
					ctx,
					h.OAuthIssuerUrl,
					func() (*OIDCDiscovery, error) {
						return fetchOIDCDiscoveryInfo(h.OAuthIssuerUrl)
					},
				)
				if err != nil {
					return nil, err
				}
				// fetch user info
				userInfo, err := fetchUserInfo(discovery.UserInfoEndpoint, accessToken)
				if err != nil {
					return nil, err
				}

				// validate access
				orgRows, _ := h.Pg.Query(
					ctx,
					orgInfoQuery,
					userInfo.UserId,
					organizationId,
					userInfo.Email,
					userInfo.Name,
					"oidc",
					nil,
					userInfo.UserId,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No user with user ID %s found", userInfo.UserId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Println("pgx error collecting org auth info: ", err)
					return nil, err // unknown error, not cached
				}

				InitializeAndUnmarshalOrgConfig(orgAuth)

				return orgAuth, nil
			},
		)
		if err != nil {
			fmt.Printf("error: %v\n", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerOAuth) Middleware(next http.Handler) http.Handler {
	orgInfoQuery := getOrgInfoForNonSupabaseUserQueryByType(authTypeOauth)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.Middleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.Middleware(next).ServeHTTP(w, r)
			return
		}

		// get tenant id header
		tenantId := r.Header.Get("X-Tenant-Id")
		if tenantId == "" {
			log.Println("Missing tenant ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		accessToken := r.Header.Get("Authorization")
		if accessToken == "" {
			log.Println("Invalid or empty authorization header")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		accessToken = accessToken[len("Bearer "):]

		// verify tenant access
		auth, err := h.cache.GetFresh(
			ctx,
			lsredis.StringToHash(getTenantInfoForUserQuery, orgInfoQuery, accessToken, tenantId),
			func() (*AuthInfo, error) {
				// fetch OIDC discovery info
				discovery, err := h.oidcDiscoveryCache.GetFresh(
					ctx,
					h.OAuthIssuerUrl,
					func() (*OIDCDiscovery, error) {
						return fetchOIDCDiscoveryInfo(h.OAuthIssuerUrl)
					},
				)
				if err != nil {
					return nil, err
				}
				userInfo, err := fetchUserInfo(discovery.UserInfoEndpoint, accessToken)
				if err != nil {
					return nil, err
				}

				// validate access by retrieving org info then tenant info in one transaction
				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
				if err != nil {
					return nil, err
				}
				defer func() {
					if err != nil {
						tx.Rollback(r.Context())
						return
					}
					err = tx.Commit(r.Context()) // commit, set return error here
				}()

				// retrieve org ID from tenant ID if not provided
				organizationId := r.Header.Get("X-Organization-Id")
				var resolvedOrgId string
				if organizationId == "" {
					orgIdRow, _ := tx.Query(
						ctx,
						getOrgIdFromTenantIdQuery,
						tenantId,
					)
					if orgId, err := pgx.CollectExactlyOneRow(orgIdRow, pgx.RowTo[string]); err == nil {
						resolvedOrgId = orgId
					} else if err != nil || resolvedOrgId == "" {
						return nil, err // unknown error (unable to find org), not cached
					}
				} else {
					resolvedOrgId = organizationId
				}

				// org info
				orgRows, _ := tx.Query(
					ctx,
					orgInfoQuery,
					userInfo.UserId,
					resolvedOrgId,
					userInfo.Email,
					userInfo.Name,
					"oidc",
					nil,
					userInfo.UserId,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No user found in workspace middleware for provider user ID %s, email %s, provider %s, SAML provider ID %v, org ID %s, and workspace ID %s", userInfo.UserId, userInfo.Email, "oidc", nil, resolvedOrgId, tenantId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Printf("pgx error collecting org auth info in workspace middleware for provider user ID %s, email %s, provider %s, SAML provider ID %v, org ID %s, and workspace ID %s: %v", userInfo.UserId, userInfo.Email, "oidc", nil, resolvedOrgId, tenantId, err)
					return nil, err // unknown error, not cached
				}

				providerUserRows, _ := tx.Query(
					ctx,
					ListProvidersQuery,
					userInfo.UserId,
					userInfo.Email,
					"oidc",
					nil,
				)
				providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No login method for provider user ID %s, email %s, provider %s, SAML provider ID %v, org ID %s, and workspace ID %s found", userInfo.UserId, userInfo.Email, "oidc", nil, resolvedOrgId, tenantId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Printf("pgx error collecting provider user info in workspace middleware for user ID %s, email %s, provider %s, SAML provider ID %v, org ID %s, and workspace ID %s: %v", userInfo.UserId, userInfo.Email, "oidc", nil, resolvedOrgId, tenantId, err)
					return nil, err // unknown error, not cached
				}

				// tenant info
				rows, _ := tx.Query(
					ctx,
					getTenantInfoForUserQuery,
					providerInfo.LSUserID,
					tenantId,
				)
				tenantAuth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[TenantAuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No identity found for user %s (ls_user_id %s) on tenant %s", userInfo.UserId, providerInfo.LSUserID, tenantId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Printf("pgx error collecting workspace auth info in workspace middleware for user ID %s (ls_user_id %s), email %s, provider %s, SAML provider ID %v, org ID %s, and workspace ID %s: %v", userInfo.UserId, providerInfo.LSUserID, userInfo.Email, "oidc", nil, resolvedOrgId, tenantId, err)
					return nil, err // unknown error, not cached
				}

				auth := &AuthInfo{
					OrganizationID:                  orgAuth.OrganizationID,
					OrganizationIsPersonal:          orgAuth.OrganizationIsPersonal,
					OrganizationMetronomeCustomerId: orgAuth.OrganizationMetronomeCustomerId,
					OrganizationStripeCustomerId:    orgAuth.OrganizationStripeCustomerId,
					OrganizationIdentityID:          orgAuth.IdentityID,
					OrganizationIdentityReadOnly:    orgAuth.IdentityReadOnly,
					OrganizationPermissions:         orgAuth.OrganizationPermissions,
					OrganizationConfig:              orgAuth.OrganizationConfig,
					OrganizationDisabled:            orgAuth.OrganizationDisabled,
					PublicSharingDisabled:           orgAuth.PublicSharingDisabled,
					SsoOnly:                         orgAuth.SsoOnly,
					UserID:                          orgAuth.UserID,
					LSUserID:                        orgAuth.LSUserID,
					UserEmail:                       orgAuth.UserEmail,
					UserFullName:                    orgAuth.UserFullName,
					TenantID:                        tenantAuth.TenantID,
					TenantHandle:                    tenantAuth.TenantHandle,
					TenantIsDeleted:                 tenantAuth.TenantIsDeleted,
					TenantConfig:                    tenantAuth.TenantConfig,
					TenantIdentityID:                tenantAuth.IdentityID,
					TenantIdentityReadOnly:          tenantAuth.IdentityReadOnly,
					IdentityPermissions:             tenantAuth.IdentityPermissions,
				}

				InitializeAndUnmarshalConfig(auth)

				return auth, nil
			},
		)
		if err != nil {
			fmt.Printf("error: %v\n", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerOAuth) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r, "X-Tenant-Id", "Authorization", "X-Api-Key", "X-Service-Key")
}

func (h *HandlerOAuth) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	if r.Header.Get("X-Api-Key") != "" {
		h.ApiKey.GetTenantlessAuth(w, r)
		return
	}

	accessToken := r.Header.Get("Authorization")
	if accessToken == "" {
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}
	accessToken = accessToken[len("Bearer "):]

	// fetch OIDC discovery info
	discovery, err := h.oidcDiscoveryCache.GetFresh(
		ctx,
		h.OAuthIssuerUrl,
		func() (*OIDCDiscovery, error) {
			return fetchOIDCDiscoveryInfo(h.OAuthIssuerUrl)
		},
	)
	if err != nil {
		fmt.Printf("error: %v\n", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	userInfo, err := fetchUserInfo(discovery.UserInfoEndpoint, accessToken)
	if err != nil {
		fmt.Printf("error: %v\n", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	userIDFromEmail := uuid.NewV5(uuid.NamespaceOID, userInfo.Email)

	// validate access
	rows, _ := h.Pg.Query(
		ctx,
		listTenantsAndOrgsForNonSSOUserQueryByType(authTypeOauth),
		userIDFromEmail.String(),
		userInfo.Email,
		userInfo.Name,
		"oidc",
		nil,
		userIDFromEmail.String(),
	)
	auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AllTenantsAuthInfo])
	if err != nil {
		fmt.Printf("error: %v\n", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func (h *HandlerOAuth) XServiceKeyMiddleware(next http.Handler) http.Handler {
	return h.XServiceKeyAuth.XServiceKeyMiddleware(next)
}

func (h *HandlerOAuth) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalAuth(w, r)
}

func (h *HandlerOAuth) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalOrgAuth(w, r)
}
