package auth_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/jwtauth/v5"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

func TestHandlerBasicAuth_WSMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		r.Header.Set("Authorization", "Bearer invalid")

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("wrong signature jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := jwtauth.New("HS256", []byte("wrong"), nil).Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"})
		r.Header.Set("Authorization", "Bearer "+token)

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("invalid sub", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-00000000000"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("missing identity", func(t *testing.T) {
		dbpool.Exec(context.Background(), "delete from organizations")

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("present with admin permissions", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, '<EMAIL>', 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant", "{}", "00000000-0000-0000-0000-000000000003", "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		claims := map[string]interface{}{
			"aud":       "authenticated",
			"sub":       "00000000-0000-0000-0000-000000000002",
			"email":     "<EMAIL>",
			"full_name": "hello",
		}
		_, token, _ := ah.Jwt.Encode(claims)
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("present with read only permissions", func(t *testing.T) {
		email := "<EMAIL>"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled) select $1, $2, 'yo', $3, id, false from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005", "test tenant", `{"max_identities": 4}`, "00000000-0000-0000-0000-000000000003", "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000006",
			email,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002", "email": email})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		testConfig := testutil.GetDefaultTenantConfig()
		testConfig.MaxIdentities = util.IntPtr(4)
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  true,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000005",
				TenantHandle:            "yo",
				TenantConfig:            testConfig,
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  true,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  true,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000005",
			TenantHandle:            "yo",
			TenantConfig:            testConfig,
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  true,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     READ_ONLY_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
	ah = auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("present with org admin permissions", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, '<EMAIL>', 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant", "{}", "00000000-0000-0000-0000-000000000003", "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000006")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		claims := map[string]interface{}{
			"aud":       "authenticated",
			"sub":       "00000000-0000-0000-0000-000000000002",
			"email":     "<EMAIL>",
			"full_name": "hello",
		}
		_, token, _ := ah.Jwt.Encode(claims)
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
	ah = auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("present with org admin permissions multiple tenants", func(t *testing.T) {
		tenantId1 := "00000000-0000-0000-0000-000000000001"
		tenant2 := "00000000-0000-0000-0000-000000000009"
		orgId := "00000000-0000-0000-0000-000000000003"
		userId := "00000000-0000-0000-0000-000000000002"
		orgIdentityId := "00000000-0000-0000-0000-000000000005"

		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
	values
		($1, $2, 'yo', $3, (select id from org), false),
		($8, 'test-tenant2', 'yo2', $3, (select id from org), true)
		returning id
),

usr as (
	insert into users (id, email, full_name) select $6, '<EMAIL>', 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $7, (select ls_user_id from usr)
from ten cross join org`,
			tenantId1,
			"test tenant",
			"{}",
			orgId,
			"test org",
			userId,
			orgIdentityId,
			tenant2,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", tenantId1)

		claims := map[string]interface{}{
			"aud":       "authenticated",
			"sub":       "00000000-0000-0000-0000-000000000002",
			"email":     "<EMAIL>",
			"full_name": "hello",
		}
		_, token, _ := ah.Jwt.Encode(claims)
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                tenantId1,
				TenantHandle:            "yo",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantId1,
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", tenantId1)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                tenantId1,
			TenantHandle:            "yo",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantId1,
			TenantIdentityReadOnly:  false,
			UserID:                  userId,
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
	ah = auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("present with org user permissions", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, '<EMAIL>', 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant", "{}", "00000000-0000-0000-0000-000000000003", "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000006")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		claims := map[string]interface{}{
			"aud":       "authenticated",
			"sub":       "00000000-0000-0000-0000-000000000002",
			"email":     "<EMAIL>",
			"full_name": "hello",
		}
		_, token, _ := ah.Jwt.Encode(claims)
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		TestLogger(t)(ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          "00000000-0000-0000-0000-000000000003",
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_USER_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})
}

func TestHandlerBasicAuth_OrgMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")

		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		r.Header.Set("Authorization", "Bearer invalid")

		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("wrong signature jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := jwtauth.New("HS256", []byte("wrong"), nil).Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"})
		r.Header.Set("Authorization", "Bearer "+token)

		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("invalid sub", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-00000000000"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("missing identity", func(t *testing.T) {
		dbpool.Exec(context.Background(), "delete from organizations")

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		}))).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("present with org admin permissions multiple orgs", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000001"
		orgId2 := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"

		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org', $3),
					($2, 'test org 2', $3)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, full_name)
				select u.ls_user_id, u.email, 'email', u.full_name from usr u
			)

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, $3, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
			`,
			orgId1,
			orgId2,
			userId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId1)
		claims := map[string]interface{}{
			"aud":       "authenticated",
			"sub":       userId,
			"email":     email,
			"full_name": fullName,
		}
		_, token, _ := ah.Jwt.Encode(claims)
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when org present
		called := false
		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId1,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgId1, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    fullName,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId1)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId1,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgId1, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       email,
			UserFullName:                    fullName,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewBasicAuth(dbpool, redisPool, 0) // clear the auth cache

	t.Run("present with org user permissions", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		identityId := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"

		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id)
					select $1, 'test org', $3
				returning id
			),

			usr as (
				insert into users (id, email) select $3, $4 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider)
				select u.ls_user_id, u.email, 'email' from usr u
			)

			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $2, $3, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
			`,
			orgId,
			identityId,
			userId,
			email,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		claims := map[string]interface{}{
			"aud":   "authenticated",
			"sub":   userId,
			"email": email,
		}
		_, token, _ := ah.Jwt.Encode(claims)
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when org present
		called := false
		TestLogger(t)(ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    "",
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		}))).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: identityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       email,
			UserFullName:                    "",
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})
}

func TestHandlerBasicAuth_MiddlewareApiKey(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	runApiKeyMiddlewareTests(t, dbpool, ah, false)
}

func TestHandlerBasicAuth_GetTenantlessAuth(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("invalid jwt", func(t *testing.T) {
		r := httptest.NewRequest("GET", "/current/tenants", nil)
		r.Header.Set("Authorization", "Bearer invalid")

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer invalid")
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		assert.Equal(t, []byte(`{"error":"Forbidden"}
`), body)
	})

	ah = auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("multiple tenants found", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000003"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
values
    ($1, $2, 'yo', $3, (select id from org), false),
    ($7, 'test-tenant2', 'yo2', $3, (select id from org), true)
returning id
),

usr as (
	insert into users (id, email, full_name) select $6, '<EMAIL>', 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, ls_user_id, user_id, organization_id, role_id, access_scope)
	select $8, usr.ls_user_id, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization' from org, usr returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005",
			"test tenant",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000001",
			"00000000-0000-0000-0000-000000000006")
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"aud":       "authenticated",
			"sub":       "00000000-0000-0000-0000-000000000002",
			"email":     "<EMAIL>",
			"full_name": "hello",
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          "00000000-0000-0000-0000-000000000002",
			UserEmail:       "<EMAIL>",
			UserFullName:    "hello",
			TenantIDs:       []string{"00000000-0000-0000-0000-000000000001", "00000000-0000-0000-0000-000000000005"},
			OrganizationIds: []string{"00000000-0000-0000-0000-000000000003"},
		}, authInfo)
	})

	ah = auth.NewBasicAuth(dbpool, redisPool, 0)
	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("identities in multiple orgs", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000001"
		orgId2 := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000003"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id)
				values
					($1, 'test org', true, $3),
					($2, 'test org 2', false, $3)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, '<EMAIL>', 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, full_name)
				select u.ls_user_id, u.email, 'email', u.full_name from usr u
			)

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, ls_user_id, user_id, organization_id, read_only, role_id, access_scope)
			select org.id, usr.ls_user_id, usr.id, org.id, false, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization'
			from org cross join usr`,
			orgId1,
			orgId2,
			userId,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"aud":       "authenticated",
			"sub":       userId,
			"email":     "<EMAIL>",
			"full_name": "hello",
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userId,
			UserEmail:       "<EMAIL>",
			UserFullName:    "hello",
			TenantIDs:       []string{},
			OrganizationIds: []string{orgId1, orgId2},
		}, authInfo)
	})
}

func TestHandlerBasicAuthLogin(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	// const password = "2%AgN$({^ptda/hhb91Jw%[R"
	const hashedPassword = "16384$8$1$OAUuXm6+gTZuU7sT7wWZ9g==$de9c0233bcc956ffc5345074d47921d7fa69100ff5a7cc108e7364ec9269aca7ba88f63a961e21a04926b6bf21ed5794da5007a6e0950dd4f73b13826273b7f9"
	const basicAuthHeader = "Basic ********************************************************"

	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.Login(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")

		ah.Login(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid basic auth", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("Authorization", "Basic invalid")

		ah.Login(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("login success", func(t *testing.T) {
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled) select $1, $2, 'yo', $3, id, false from org returning id
),

usr as (
	insert into users (id, email, full_name, hashed_password) select $6, '<EMAIL>', 'hello', $8 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, full_name)
	select u.ls_user_id, u.email, 'email', u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $9, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $9, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005", "test tenant", `{"max_identities": 4}`, "00000000-0000-0000-0000-000000000003", "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			hashedPassword,
			"00000000-0000-0000-0000-000000000006")
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("POST", "/token", nil)
		r.Header.Set("Authorization", basicAuthHeader)

		ah.Login(w, r)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json access token info
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).Post("/token", ah.Login)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("POST", srv.URL+"/token", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", basicAuthHeader)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()

		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		// read the body into a AccessTokenInfo struct:
		accessTokenInfo := &auth.AccessTokenInfo{}
		assert.NoError(t, json.Unmarshal(body, &accessTokenInfo))
		accessToken := accessTokenInfo.AccessToken
		token, err := jwtauth.VerifyToken(ah.Jwt, accessToken)
		assert.NoError(t, err)
		assert.Equal(t, []string{"authenticated"}, token.Audience())
		assert.Equal(t, "00000000-0000-0000-0000-000000000002", token.Subject())
	})
}
