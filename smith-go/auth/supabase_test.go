package auth_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/jwtauth/v5"
	"github.com/gofrs/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

const emailSupabase = "<EMAIL>"
const userNameSupabase = "supabase base"

func tokenForSSO(ah *auth.HandlerSupabase, userId string, email string, name string, samlProviderId string) string {
	_, token, _ := ah.Jwt.Encode(map[string]interface{}{
		"sub":           userId,
		"email":         email,
		"user_metadata": map[string]interface{}{"full_name": name},
		"app_metadata":  map[string]interface{}{"provider": fmt.Sprintf("sso:%s", samlProviderId)},
	})
	return token
}

func TestAuthHandlerSupabase_WSMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewSupabase(dbpool, redisPool, 0)
	orgId := "00000000-0000-0000-0000-000000000011"

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		r.Header.Set("Authorization", "Bearer invalid")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("wrong signature jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := jwtauth.New("HS256", []byte("wrong"), nil).Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"})
		r.Header.Set("Authorization", "Bearer "+token)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid sub", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-00000000000"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("missing identity", func(t *testing.T) {
		dbpool.Exec(context.Background(), "delete from organizations")

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewSupabase(dbpool, redisPool, 0)

	t.Run("present with admin permissions", func(t *testing.T) {
		email := "<EMAIL>"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email) select $6, $9 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant present", "{}", orgId, "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			email,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           "00000000-0000-0000-0000-000000000002",
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": "hello"},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               "<EMAIL>",
				UserFullName:            "hello",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               "<EMAIL>",
			UserFullName:            "hello",
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("present with read only permissions", func(t *testing.T) {
		email := "<EMAIL>"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled) select $1, $2, 'yo', $3, id, false from org returning id
),

usr as (
	insert into users (id, email) select $6, $9 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005", "test tenant read only", `{"max_identities": 4}`, orgId, "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000006",
			email,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002", "email": email})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			tenantConfig := testutil.GetDefaultTenantConfig()
			tenantConfig.MaxIdentities = util.IntPtr(4)
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  true,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000005",
				TenantHandle:            "yo",
				TenantConfig:            tenantConfig,
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  true,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               email,
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		tenantConfig := testutil.GetDefaultTenantConfig()
		tenantConfig.MaxIdentities = util.IntPtr(4)
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  true,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000005",
			TenantHandle:            "yo",
			TenantConfig:            tenantConfig,
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  true,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               email,
			IdentityPermissions:     READ_ONLY_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	// This test is for backwards compatibility until flags are removed from the DB
	t.Run("present with boolean and string flags which are ignored", func(t *testing.T) {
		email := "<EMAIL>"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id, config) values ($4, $5, true, $6, $10) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled) select $1, $2, 'yo', $3, id, false from org returning id
),

usr as (
	insert into users (id, email) select $6, $9 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005", "test tenant read only", `{"max_identities": 4}`, orgId, "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000006",
			email,
			`{"flags": {"payment_enabled": true, "pat_enabled": "true"}}`,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002", "email": email})
		r.Header.Set("Authorization", "Bearer "+token)

		tenantConfig := testutil.GetDefaultTenantConfig()
		tenantConfig.MaxIdentities = util.IntPtr(4)
		orgConfig := testutil.GetDefaultOrgConfig()
		tenantConfig.OrganizationConfig = orgConfig
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""

			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  true,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000005",
				TenantHandle:            "yo",
				TenantConfig:            tenantConfig,
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  true,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               email,
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      orgConfig,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000005")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  true,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000006", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000005",
			TenantHandle:            "yo",
			TenantConfig:            tenantConfig,
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  true,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               email,
			IdentityPermissions:     READ_ONLY_PERMISSIONS,
			OrganizationPermissions: []string{"organization:read"},
			OrganizationConfig:      orgConfig,
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("user upsert", func(t *testing.T) {
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		newEmail := "<EMAIL>"
		userFullName := "hello"
		newName := "hello changed"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email) select $6, $9 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001",
			"test tenant upsert",
			"{}",
			orgId,
			"test org",
			userId,
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			email,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": userFullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// upserts user record
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				UserEmail:               email,
				UserFullName:            userFullName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: userFullName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			SAMLProviderID: sql.NullString{},
			Email:          sql.NullString{String: email, Valid: true},
			FullName:       sql.NullString{String: userFullName, Valid: true},
			HashedPassword: sql.NullString{},
		}, providerUserInfo)
		// upserts user record
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		newName = "hello changed"
		_, token, _ = ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": newName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		called = false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				UserEmail:               email,
				UserFullName:            newName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: newName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo = auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			SAMLProviderID: sql.NullString{},
			Email:          sql.NullString{String: email, Valid: true},
			FullName:       sql.NullString{String: newName, Valid: true},
			HashedPassword: sql.NullString{},
		}, providerUserInfo)

		// updates name to empty string, email address
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ = ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         newEmail,
			"user_metadata": map[string]interface{}{"full_name": ""},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		called = false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				UserEmail:               newEmail,
				UserFullName:            "",
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// user with prev email should not exist
		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		_, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.Error(t, err)
		assert.True(t, strings.Contains(err.Error(), "no rows in result set"), err.Error())

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			newEmail,
		)
		assert.NoError(t, err)
		userInfo, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: newEmail, UserFullName: "", UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo = auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			Email:          sql.NullString{String: newEmail, Valid: true},
			FullName:       sql.NullString{String: "", Valid: true},
		}, providerUserInfo)

		// update back to original email and name
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ = ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": userFullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		called = false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				UserEmail:               email,
				UserFullName:            userFullName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: []string{"organization:read"},
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// user with prev email should now exist
		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)

		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: userFullName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo = auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
			LSUserID:       userInfo.LSUserID,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			Email:          sql.NullString{String: email, Valid: true},
			FullName:       sql.NullString{String: userFullName, Valid: true},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
	ah = auth.NewSupabase(dbpool, redisPool, 0) // clear the auth cache

	t.Run("present with org admin permissions", func(t *testing.T) {
		email := "<EMAIL>"
		fullName := "hello"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant org admin", "{}", orgId, "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           "00000000-0000-0000-0000-000000000002",
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               email,
				UserFullName:            fullName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               email,
			UserFullName:            fullName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewSupabase(dbpool, redisPool, 0) // clear the auth cache

	t.Run("present with org admin permissions multiple tenants", func(t *testing.T) {
		tenantId1 := "00000000-0000-0000-0000-000000000001"
		tenant2 := "00000000-0000-0000-0000-000000000009"
		orgId := orgId
		userId := "00000000-0000-0000-0000-000000000002"
		orgIdentityId := "00000000-0000-0000-0000-000000000005"
		email := "<EMAIL>"
		fullName := "hello"

		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
	values
		($1, $2, 'yo', $3, (select id from org), false),
		($8, 'test-tenant2', 'yo2', $3, (select id from org), true)
		returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $7, (select ls_user_id from usr)
from ten cross join org`,
			tenantId1,
			"test tenant org admin multiple",
			"{}",
			orgId,
			"test org",
			userId,
			orgIdentityId,
			tenant2,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", tenantId1)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                tenantId1,
				TenantHandle:            "yo",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantId1,
				TenantIdentityReadOnly:  false,
				UserID:                  userId,
				UserEmail:               email,
				UserFullName:            fullName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, auth.GetAuthInfo(r))
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", tenantId1)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                tenantId1,
			TenantHandle:            "yo",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantId1,
			TenantIdentityReadOnly:  false,
			UserID:                  userId,
			UserEmail:               email,
			UserFullName:            fullName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewSupabase(dbpool, redisPool, 0) // clear the auth cache

	t.Run("present with org user permissions", func(t *testing.T) {
		email := "<EMAIL>"
		fullName := "hello"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000001", "test tenant org user", "{}", orgId, "test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           "00000000-0000-0000-0000-000000000002",
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
				TenantID:                "00000000-0000-0000-0000-000000000001",
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
				TenantIdentityReadOnly:  false,
				UserID:                  "00000000-0000-0000-0000-000000000002",
				UserEmail:               email,
				UserFullName:            fullName,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", "00000000-0000-0000-0000-000000000001")
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: "00000000-0000-0000-0000-000000000005", Valid: true},
			TenantID:                "00000000-0000-0000-0000-000000000001",
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        "00000000-0000-0000-0000-000000000004",
			TenantIdentityReadOnly:  false,
			UserID:                  "00000000-0000-0000-0000-000000000002",
			UserEmail:               email,
			UserFullName:            fullName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_USER_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("deleted workspace", func(t *testing.T) {
		email := "<EMAIL>"
		fullName := "hello"
		userId, err := uuid.NewV4()
		assert.NoError(t, err)
		userIdStr := userId.String()
		user := UserSetup(t, dbpool, userIdStr, oauthSessionedEmail, oauthSessionedUserName, "supabase:non-sso", nil, &userIdStr)
		lsUserId := user.LSUserID
		orgId := OrgSetup(t, dbpool, "test org", false, userIdStr)
		wsId := TenantSetup(t, dbpool, orgId, "test tenant", "handle", &auth.TenantConfig{}, true)
		_, err = dbpool.Exec(context.Background(), "UPDATE tenants SET is_deleted = true WHERE id = $1", wsId)
		assert.NoError(t, err)
		orgIdentityId := IdentitySetup(t, dbpool, userIdStr, orgId, nil, "ORGANIZATION_ADMIN", "organization", nil, lsUserId)
		wsIdentityId := IdentitySetup(t, dbpool, userIdStr, orgId, &wsId, "WORKSPACE_ADMIN", "workspace", &orgIdentityId, lsUserId)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Tenant-ID", wsId)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdStr,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		called := false
		expectedAuthInfo := &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                wsId,
			TenantHandle:            "handle",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        wsIdentityId,
			TenantIdentityReadOnly:  false,
			TenantIsDeleted:         true,
			UserID:                  userIdStr,
			UserEmail:               email,
			UserFullName:            fullName,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, expectedAuthInfo, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", wsId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, expectedAuthInfo, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("existing SSO only user deleted then linked", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		userIdSso := "00000000-0000-0000-0000-000000000002"
		userIdNonSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with org as (
				insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values ($1, $2, $3, (select id from org))
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, $10 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{}`,
			orgId,
			"test org",
			userIdSso,
			orgIdentityId,
			samlProviderId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		tokenSSO := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+tokenSSO)
		r.Header.Set("X-Organization-ID", orgId)
		r.Header.Set("X-Tenant-ID", tenantId)

		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                tenantId,
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantId,
				TenantIdentityReadOnly:  true,
				UserID:                  userIdSso,
				UserEmail:               email,
				UserFullName:            fullName,
				IsSsoUser:               true,
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", tenantId)
		req.Header.Set("Authorization", "Bearer "+tokenSSO)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                tenantId,
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantId,
			TenantIdentityReadOnly:  true,
			UserID:                  userIdSso,
			UserEmail:               email,
			UserFullName:            fullName,
			IsSsoUser:               true,
			IdentityPermissions:     READ_ONLY_PERMISSIONS,
			OrganizationPermissions: ORG_USER_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
		}, authResponse)

		// delete the SAML provider
		_, err = dbpool.Exec(
			context.Background(),
			"delete from saml_providers where provider_id = $1",
			samlProviderId,
		)
		assert.NoError(t, err)

		// should not be able to authenticate
		req, err = http.NewRequest("GET", srv.URL+"/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Tenant-ID", tenantId)
		req.Header.Set("Authorization", "Bearer "+tokenSSO)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)

		// now login via non-SSO
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})

		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("Authorization", "Bearer "+tokenNonSso)
		r.Header.Set("X-Organization-ID", orgId)
		r.Header.Set("X-Tenant-ID", tenantId)

		called = false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                tenantId,
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantId,
				TenantIdentityReadOnly:  true,
				UserID:                  userIdNonSso,
				UserEmail:               email,
				UserFullName:            fullName,
				IsSsoUser:               false,
				IdentityPermissions:     READ_ONLY_PERMISSIONS,
				OrganizationPermissions: ORG_USER_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

}

func TestAuthHandlerSupabase_OrgMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewSupabase(dbpool, redisPool, 0)

	t.Run("missing all headers", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing authorization header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		r.Header.Set("Authorization", "Bearer invalid")

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("wrong signature jwt", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := jwtauth.New("HS256", []byte("wrong"), nil).Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"})
		r.Header.Set("Authorization", "Bearer "+token)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid sub", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-00000000000"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("missing identity", func(t *testing.T) {
		dbpool.Exec(context.Background(), "delete from organizations")

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", "00000000-0000-0000-0000-000000000001")
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{"sub": "00000000-0000-0000-0000-000000000002"}) // missing last digit
		r.Header.Set("Authorization", "Bearer "+token)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewSupabase(dbpool, redisPool, 0)
	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("user upsert org admin permissions", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		identityId := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000004"
		email := "<EMAIL>"
		fullName := "hello"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id) values ($1, 'test org', $3) returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
			)

			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
			select $2, $3, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr)
			from org`,
			orgId,
			identityId,
			userId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// inserts new user record
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    fullName,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: fullName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		// upserts user record
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		_, token, _ = ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": "hello changed"},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    "hello changed",
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: "hello changed", UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")
	ah = auth.NewSupabase(dbpool, redisPool, 0) // clear the auth cache

	t.Run("present with org admin permissions multiple orgs", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000001"
		orgId2 := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000004"
		email := "<EMAIL>"
		fullName := "hello"

		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org', $3),
					($2, 'test org 2', $3)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
			)

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, $3, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
			`,
			orgId1,
			orgId2,
			userId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId1)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when org present
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId1,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgId1, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    fullName,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId1)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId1,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgId1, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       email,
			UserFullName:                    fullName,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewSupabase(dbpool, redisPool, 0) // clear the auth cache

	t.Run("present with org user permissions", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		identityId := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000004"
		email := "<EMAIL>"
		fullName := "hello"

		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id)
					select $1, 'test org', $3
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, $4, $5 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
			)

			insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $2, $3, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
			`,
			orgId,
			identityId,
			userId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-ID", orgId)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// accepts requests when org present
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: identityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    fullName,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: identityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       email,
			UserFullName:                    fullName,
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewSupabase(dbpool, redisPool, 0) // clear the auth cache

	t.Run("existing SSO only user", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with org as (
				insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, $10 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userId,
			orgIdentityId,
			samlProviderId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		token := tokenForSSO(ah, userId, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userId,
			UserEmail:                       email,
			UserFullName:                    fullName,
			IsSsoUser:                       true,
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)

		// sso only should succeed as well
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userId,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
				SsoOnly:                         true,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("link new SSO user to existing non-SSO user", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with org as (
				insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, $10 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		token := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userIdSso,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userIdSso,
			UserEmail:                       email,
			UserFullName:                    fullName,
			IsSsoUser:                       true,
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		lsUserId := userInfo.LSUserID
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userIdNonSso, UserEmail: email, UserFullName: fullName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// sso_only should succeed only for SSO user
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userIdSso,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
				SsoOnly:                         true,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		_, token, _ = ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("linked login methods already exist", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with org as (
				insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, $10 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', u.id from usr u
			),

			provider_user_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', $11, (select provider_id from saml_provider) from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			fullName,
			userIdSso,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		token := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userIdSso,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userIdSso,
			UserEmail:                       email,
			UserFullName:                    fullName,
			IsSsoUser:                       true,
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		lsUserId := userInfo.LSUserID
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userIdNonSso, UserEmail: email, UserFullName: fullName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// sso_only should succeed only for SSO user
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userIdSso,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
				SsoOnly:                         true,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		_, token, _ = ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)
		r.Header.Set("X-Organization-ID", orgId)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("link new non-SSO user to existing SSO user", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000001"
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values ($1, $2, $3, (select id from org))
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{}`,
			orgId,
			"test org",
			userIdSso,
			orgIdentityId,
			samlProviderId,
			email,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         strings.ToUpper(string(email[0])) + email[1:],
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+tokenNonSso)
		r.Header.Set("X-Organization-ID", orgId)

		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userIdNonSso,
				UserEmail:                       email,
				UserFullName:                    fullName,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId)
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          userIdNonSso,
			UserEmail:                       email,
			UserFullName:                    fullName,
			OrganizationPermissions:         ORG_USER_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		lsUserId := userInfo.LSUserID
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userIdSso, UserEmail: email, UserFullName: fullName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// sso_only should succeed only for SSO user
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+tokenSso)
		r.Header.Set("X-Organization-ID", orgId)

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          userIdSso,
				UserEmail:                       email,
				UserFullName:                    fullName,
				IsSsoUser:                       true,
				OrganizationPermissions:         ORG_USER_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
				SsoOnly:                         true,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)
		r.Header.Set("Authorization", "Bearer "+tokenNonSso)
		r.Header.Set("X-Organization-ID", orgId)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("user with same email address can be used across SSO providers", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000008"
		orgId2 := "00000000-0000-0000-0000-000000000009"
		tenantId1 := "00000000-0000-0000-0000-000000000010"
		tenantId2 := "00000000-0000-0000-0000-000000000011"
		userIdExisting := "00000000-0000-0000-0000-000000000002"
		userIdNew := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId1 := orgId1
		samlProviderId2 := orgId2
		_, err := dbpool.Exec(
			context.Background(),
			`
			WITH org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org sso 1', $3),
					($2, 'test org sso 2', $3)
				returning *
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values
					($4, 'test tenant sso', $6, $1),
					($5, 'test tenant non-sso', $6, $2)
				returning *
			),

			-- use org ID as provider ID for simplicity and to avoid primary key conflict
			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select org.id, org.id, (select 'https://' || org.id || '.com'), array[ten.id], (select id from roles where name = 'WORKSPACE_ADMIN')
				from org join ten on ten.organization_id = org.id
				returning *
			),

			usr as (
				insert into users (id, email, full_name) select $3, $7, $8 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider where organization_id = $1), now() from usr u
			),

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, (select id from usr), org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
				from org returning id
			)

			-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, (select id from usr), ten.id, ten.organization_id, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', org.id, (select ls_user_id from usr)
			from ten join org on ten.organization_id = org.id`,
			orgId1,
			orgId2,
			userIdExisting,
			tenantId1,
			tenantId2,
			`{}`,
			email,
			fullName,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/orgs/auth", nil)
		tokenSso := tokenForSSO(ah, userIdNew, email, fullName, samlProviderId2)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// should succeed to insert a new SSO user with the same email address
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()
		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId2)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailOrUserIdQuery,
			email,
			userIdNew,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
			providers[i].LSUserID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userIdExisting, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userIdNew, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("user with same provider user ID can be used across SSO providers", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000008"
		orgId2 := "00000000-0000-0000-0000-000000000009"
		tenantId1 := "00000000-0000-0000-0000-000000000010"
		tenantId2 := "00000000-0000-0000-0000-000000000011"
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		emailNew := "<EMAIL>"
		fullName := "sso"
		samlProviderId1 := orgId1
		samlProviderId2 := orgId2
		_, err := dbpool.Exec(
			context.Background(),
			`
			WITH org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org sso 1', $3),
					($2, 'test org sso 2', $3)
				returning *
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values
					($4, 'test tenant sso', $6, $1),
					($5, 'test tenant non-sso', $6, $2)
				returning *
			),

			-- use org ID as provider ID for simplicity and to avoid primary key conflict
			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select org.id, org.id, (select 'https://' || org.id || '.com'), array[ten.id], (select id from roles where name = 'WORKSPACE_ADMIN')
				from org join ten on ten.organization_id = org.id
				returning *
			),

			usr as (
				insert into users (id, email, full_name) select $3, $7, $8 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider where organization_id = $1), now() from usr u
			),

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, (select id from usr), org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
				from org returning id
			)

			-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, (select id from usr), ten.id, ten.organization_id, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', org.id, (select ls_user_id from usr)
			from ten join org on ten.organization_id = org.id`,
			orgId1,
			orgId2,
			userId,
			tenantId1,
			tenantId2,
			`{}`,
			email,
			fullName,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso := tokenForSSO(ah, userId, email, fullName, samlProviderId2)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// should succeed to insert a new SSO user with the same email address
		// and same user ID from different provider
		rtr := chi.NewRouter()
		rtr.With(TestLogger(t)).With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()
		req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		req.Header.Set("X-Organization-ID", orgId2)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailOrUserIdQuery,
			email,
			userId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
			providers[i].LSUserID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// should fail to insert a new SSO user with a different email address
		// and same user ID from different provider
		req, err = http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
		assert.NoError(t, err)
		tokenNewEmail := tokenForSSO(ah, userId, emailNew, fullName, samlProviderId2)
		req.Header.Set("X-Organization-ID", orgId2)
		req.Header.Set("Authorization", "Bearer "+tokenNewEmail)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailQuery,
			emailNew,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 0)
		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailQuery,
			email,
		)
		assert.NoError(t, err)
		providersOldEmail, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providersOldEmail, 2)
		for i := range providersOldEmail {
			providersOldEmail[i].ID = ""
			providersOldEmail[i].LSUserID = ""
		}
		expectedProviderUsers = []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providersOldEmail)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	// TODO: uncomment when we support email change
	// t.Run("SSO only user insert and upsert", func(t *testing.T) {
	// 	orgId := "00000000-0000-0000-0000-000000000001"
	// 	userId := "00000000-0000-0000-0000-000000000002"
	// 	email := "<EMAIL>"
	// 	fullName := "sso"
	// 	samlProviderId := "00000000-0000-0000-0000-000000000007"
	// 	_, err := dbpool.Exec(
	// 		context.Background(),
	// 		`with org as (
	// 			insert into organizations (id, display_name, created_by_user_id) values ($1, $2, $3) returning id
	// 		)

	// 		insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
	// 		select $4, org.id, 'https://fake.com' from org returning provider_id
	// 		`,
	// 		orgId,
	// 		"test org",
	// 		userId,
	// 		samlProviderId,
	// 	)
	// 	assert.NoError(t, err)

	// 	w := httptest.NewRecorder()
	// 	r := httptest.NewRequest("GET", "/", nil)
	// 	token := tokenForSSO(ah, userId, email, fullName, samlProviderId)
	// 	r.Header.Set("Authorization", "Bearer "+token)
	// 	r.Header.Set("X-Organization-ID", orgId)

	// 	called := false
	// 	ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	// 		called = true
	// 		authInfo := auth.GetOrgAuthInfo(r)
	// 		assert.NotEmpty(t, authInfo.LSUserID)
	// 		authInfo.LSUserID = ""
	// 		assert.Equal(t, &auth.OrgAuthInfo{
	// 			OrganizationID:                  orgId,
	// 			OrganizationIsPersonal:          false,
	// 			OrganizationMetronomeCustomerId: "",
	// 			OrganizationStripeCustomerId:    "",
	// 			IdentityID:                      sql.NullString{String: "", Valid: false},
	// 			IdentityReadOnly:                false,
	// 			UserID:                          userId,
	// 			UserEmail:                       email,
	// 			UserFullName:                    fullName,
	// 			OrganizationPermissions:         []string{},
	// 			OrganizationConfig:              &auth.OrganizationConfig{},
	// 			ServiceIdentity:                 "",
	// 		}, authInfo)
	// 	})).ServeHTTP(w, r)
	// 	assert.True(t, called)
	// 	assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

	// rows, err := dbpool.Query(
	// 	context.Background(),
	// 	auth.ListUsersQuery,
	// 	email,
	// )
	// assert.NoError(t, err)
	// userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
	// assert.NoError(t, err)
	// assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
	// assert.Equal(t,
	// 	&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: fullName, UserHashedPassword: ""},
	// 	&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
	// )

	// rows, err = dbpool.Query(
	// 	context.Background(),
	// 	auth.ListProvidersForUserQuery,
	// 	userInfo.LSUserID,
	// )
	// assert.NoError(t, err)
	// providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
	// assert.NoError(t, err)
	// assert.Len(t, providers, 1)
	// providerUserInfo := auth.ProviderUserInfo{
	// 	Provider:       providers[0].Provider,
	// 	LSUserID:       providers[0].LSUserID,
	// 	ProviderUserID: providers[0].ProviderUserID,
	// 	SAMLProviderID: providers[0].SAMLProviderID,
	// 	Email:          providers[0].Email,
	// 	FullName:       providers[0].FullName,
	// 	HashedPassword: providers[0].HashedPassword,
	// }
	// assert.Equal(t, auth.ProviderUserInfo{
	// 	Provider:       sql.NullString{String: "supabase:sso", Valid: true},
	// 	LSUserID:       userInfo.LSUserID,
	// 	ProviderUserID: sql.NullString{String: userId, Valid: true},
	// 	SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
	// 	Email:          sql.NullString{String: email, Valid: true},
	// 	FullName:       sql.NullString{String: fullName, Valid: true},
	// 	HashedPassword: sql.NullString{},
	// }, providerUserInfo)

	// 	// returns json auth info
	// 	rtr := chi.NewRouter()
	// 	rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
	// 	srv := httptest.NewServer(rtr)
	// 	defer srv.Close()

	// 	req, err := http.NewRequest("GET", srv.URL+"/orgs/auth", nil)
	// 	assert.NoError(t, err)
	// 	req.Header.Set("X-Organization-ID", orgId)
	// 	req.Header.Set("Authorization", "Bearer "+token)
	// 	res, err := srv.Client().Do(req)

	// 	assert.NoError(t, err)
	// 	defer res.Body.Close()
	// 	assert.Equal(t, http.StatusOK, res.StatusCode)
	// 	assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
	// 	body, err := io.ReadAll(res.Body)
	// 	assert.NoError(t, err)
	// 	authResponse := &auth.OrgAuthInfo{}
	// 	assert.NoError(t, json.Unmarshal(body, &authResponse))
	// 	assert.NotEmpty(t, authResponse.LSUserID)
	// 	authResponse.LSUserID = ""
	// 	assert.Equal(t, &auth.OrgAuthInfo{
	// 		OrganizationID:                  orgId,
	// 		OrganizationIsPersonal:          false,
	// 		OrganizationMetronomeCustomerId: "",
	// 		OrganizationStripeCustomerId:    "",
	// 		IdentityID:                      sql.NullString{String: "", Valid: false},
	// 		IdentityReadOnly:                false,
	// 		UserID:                          userId,
	// 		UserEmail:                       email,
	// 		UserFullName:                    fullName,
	// 		OrganizationPermissions:         []string{},
	// 		OrganizationConfig:              &auth.OrganizationConfig{},
	// 		ServiceIdentity:                 "",
	// 	}, authResponse)

	// 	newEmail := "<EMAIL>"
	// 	newName := "ssonew"
	// 	w = httptest.NewRecorder()
	// 	r = httptest.NewRequest("GET", "/", nil)
	// 	token = tokenForSSO(ah, userId, newEmail, newName, samlProviderId)
	// 	r.Header.Set("Authorization", "Bearer "+token)
	// 	r.Header.Set("X-Organization-ID", orgId)

	// 	called = false
	// 	ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
	// 		called = true
	// 		authInfo := auth.GetOrgAuthInfo(r)
	// 		assert.NotEmpty(t, authInfo.LSUserID)
	// 		authInfo.LSUserID = ""
	// 		assert.Equal(t, &auth.OrgAuthInfo{
	// 			OrganizationID:                  orgId,
	// 			OrganizationIsPersonal:          false,
	// 			OrganizationMetronomeCustomerId: "",
	// 			OrganizationStripeCustomerId:    "",
	// 			IdentityID:                      sql.NullString{String: "", Valid: false},
	// 			IdentityReadOnly:                false,
	// 			UserID:                          userId,
	// 			UserEmail:                       newEmail,
	// 			UserFullName:                    newName,
	// 			OrganizationPermissions:         []string{},
	// 			OrganizationConfig:              &auth.OrganizationConfig{},
	// 			ServiceIdentity:                 "",
	// 		}, authInfo)
	// 	})).ServeHTTP(w, r)
	// 	assert.True(t, called)
	// 	assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

	// 	rows, err = dbpool.Query(
	// 		context.Background(),
	// 		auth.ListUsersQuery,
	// 		newEmail,
	// 	)
	// 	assert.NoError(t, err)
	// 	userInfo, err = pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
	// 	assert.NoError(t, err)
	// 	assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
	// 	assert.Equal(t,
	// 		&auth.UserInfoWithHashedPassword{UserID: userId, UserEmail: email, UserFullName: fullName, UserHashedPassword: ""},
	// 		&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
	// 	)

	// 	rows, err = dbpool.Query(
	// 		context.Background(),
	// 		auth.ListProvidersForUserQuery,
	// 		userInfo.LSUserID,
	// 	)
	// 	assert.NoError(t, err)
	// 	providers, err = pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
	// 	assert.NoError(t, err)
	// 	assert.Len(t, providers, 1)
	// 	providerUserInfo = auth.ProviderUserInfo{
	// 		Provider:       providers[0].Provider,
	// 		LSUserID:       providers[0].LSUserID,
	// 		ProviderUserID: providers[0].ProviderUserID,
	// 		SAMLProviderID: providers[0].SAMLProviderID,
	// 		Email:          providers[0].Email,
	// 		FullName:       providers[0].FullName,
	// 		HashedPassword: providers[0].HashedPassword,
	// 	}
	// 	assert.Equal(t, auth.ProviderUserInfo{
	// 		Provider:       sql.NullString{String: "supabase:sso", Valid: true},
	// 		LSUserID:       userInfo.LSUserID,
	// 		ProviderUserID: sql.NullString{String: userId, Valid: true},
	// 		SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
	// 		Email:          sql.NullString{String: newEmail, Valid: true},
	// 		FullName:       sql.NullString{String: newName, Valid: true},
	// 		HashedPassword: sql.NullString{},
	// 	}, providerUserInfo)
	// })
}

func TestAuthHandlerSupabase_ApiKeyMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewSupabase(dbpool, redisPool, 0)

	runApiKeyMiddlewareTests(t, dbpool, ah, true)
}

func TestAuthHandlerSupabase_GetTenantlessAuth(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewSupabase(dbpool, redisPool, 0)
	orgId := "00000000-0000-0000-0000-000000000011"

	t.Run("invalid jwt", func(t *testing.T) {
		r := httptest.NewRequest("GET", "/current/tenants", nil)
		r.Header.Set("Authorization", "Bearer invalid")

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer invalid")
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		assert.Equal(t, []byte(`{"error":"Forbidden"}
`), body)
	})

	ah = auth.NewSupabase(dbpool, redisPool, 0)

	t.Run("user insert", func(t *testing.T) {
		userId := "00000000-0000-0000-0000-000000000002"
		tenantId := "00000000-0000-0000-0000-000000000001"
		email := "<EMAIL>"
		fullName := "hello"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
			)
			
			insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
			`,
			tenantId,
			"test tenant upsert",
			"{}",
			orgId,
			"test org",
			userId,
		)
		assert.NoError(t, err)

		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userId,
			UserEmail:       email,
			UserFullName:    fullName,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
			LSUserID:       lsUserId,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			SAMLProviderID: sql.NullString{},
			Email:          sql.NullString{String: email, Valid: true},
			FullName:       sql.NullString{String: fullName, Valid: true},
			HashedPassword: sql.NullString{},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("multiple tenants found", func(t *testing.T) {
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		fullName := "hello"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
values
    ($1, $2, 'yo', $3, (select id from org), false),
    ($7, 'test-tenant2', 'yo2', $3, (select id from org), true)
returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005",
			"test tenant multiple",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userId,
			"00000000-0000-0000-0000-000000000001",
			"00000000-0000-0000-0000-000000000006",
			email,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         strings.ToUpper(string(email[0])) + email[1:],
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userId,
			UserEmail:       email,
			UserFullName:    fullName,
			TenantIDs:       []string{"00000000-0000-0000-0000-000000000001", "00000000-0000-0000-0000-000000000005"},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
			LSUserID:       lsUserId,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			SAMLProviderID: sql.NullString{},
			Email:          sql.NullString{String: email, Valid: true},
			FullName:       sql.NullString{String: fullName, Valid: true},
			HashedPassword: sql.NullString{},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	t.Run("multiple tenants plus organization identity", func(t *testing.T) {
		email := "<EMAIL>"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
),

ten as (
insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
values
    ($1, $2, 'yo', $3, (select id from org), false),
    ($7, 'test-tenant2', 'yo2', $3, (select id from org), true)
returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, 'hello' returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
	select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			"00000000-0000-0000-0000-000000000005",
			"test tenant multiple org identity",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			"00000000-0000-0000-0000-000000000002",
			"00000000-0000-0000-0000-000000000001",
			"00000000-0000-0000-0000-000000000006",
			email,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           "00000000-0000-0000-0000-000000000002",
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": "hello"},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          "00000000-0000-0000-0000-000000000002",
			UserEmail:       "<EMAIL>",
			UserFullName:    "hello",
			TenantIDs:       []string{"00000000-0000-0000-0000-000000000001", "00000000-0000-0000-0000-000000000005"},
			OrganizationIds: []string{orgId},
		}, authInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")

	// test that organization member isn't counted for tenantless auth
	t.Run("organization member without tenant identity should not have available tenants", func(t *testing.T) {
		email := "<EMAIL>"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values
					($1, $2, 'yo-org-member', $3, (select id from org), false)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $7, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
			)

			insert into identities (user_id, organization_id, read_only, role_id, access_scope, ls_user_id)
			select $6, org.id, false, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
			from ten cross join org`,
			"00000000-0000-0000-0000-000000000006",
			"test tenant no tenant identity",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			orgId,
			email,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           orgId,
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": "hello"},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          orgId,
			UserEmail:       email,
			UserFullName:    "hello",
			TenantIDs:       []string{},
			OrganizationIds: []string{orgId},
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("identities in multiple orgs", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000001"
		orgId2 := "00000000-0000-0000-0000-000000000002"
		userId := "00000000-0000-0000-0000-000000000004"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id)
				values
					($1, 'test org', true, $3),
					($2, 'test org 2', false, $3)
				returning id
			),

			usr as (
				insert into users (id, email, full_name) select $3, '<EMAIL>', 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
			)

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, organization_id, read_only, role_id, access_scope, ls_user_id)
			select org.id, usr.id, org.id, false, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', usr.ls_user_id
			from org cross join usr`,
			orgId1,
			orgId2,
			userId,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, token, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userId,
			"email":         "<EMAIL>",
			"user_metadata": map[string]interface{}{"full_name": "hello"},
		})
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userId,
			UserEmail:       "<EMAIL>",
			UserFullName:    "hello",
			TenantIDs:       []string{},
			OrganizationIds: []string{orgId1, orgId2},
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("existing SSO only user", func(t *testing.T) {
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id)
				values ($1, $2, 'yo', $3, (select id from org))
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, $10 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, $6, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userId,
			orgIdentityId,
			samlProviderId,
			email,
			fullName,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		token := tokenForSSO(ah, userId, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+token)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userId,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			Provider:       sql.NullString{String: "supabase:sso", Valid: true},
			LSUserID:       lsUserId,
			ProviderUserID: sql.NullString{String: userId, Valid: true},
			SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
			Email:          sql.NullString{String: email, Valid: true},
			FullName:       sql.NullString{String: fullName, Valid: true},
		}, providerUserInfo)

		// sso only should succeed as well
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userId,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("SSO user with non-SSO in separate org", func(t *testing.T) {
		orgIdSso := "00000000-0000-0000-0000-000000000008"
		orgIdNonSso := "00000000-0000-0000-0000-000000000009"
		tenantIdSso := "00000000-0000-0000-0000-000000000010"
		tenantIdNonSso := "00000000-0000-0000-0000-000000000011"
		userIdSso := "00000000-0000-0000-0000-000000000002"
		userIdNonSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		_, err := dbpool.Exec(
			context.Background(),
			`
			WITH org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org sso', $3),
					($2, 'test org non-sso', $3)
				returning *
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values
					($4, 'test tenant sso', $6, $1),
					($5, 'test tenant non-sso', $6, $2)
				returning *
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $7, $1, 'https://fake.com', array[$4], (select id from roles where name = 'WORKSPACE_ADMIN')
				returning *
			),

			usr as (
				insert into users (id, email, full_name) select $3, $8, $9 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider), now() from usr u
			),

			provider_user_non_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:non-sso', $10 from usr u
			),

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, (select id from usr), org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
				from org returning id
			)

			-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, (select id from usr), ten.id, ten.organization_id, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', org.id, (select ls_user_id from usr)
			from ten join org on ten.organization_id = org.id`,
			orgIdSso,
			orgIdNonSso,
			userIdSso,
			tenantIdSso,
			tenantIdNonSso,
			`{}`,
			samlProviderId,
			email,
			fullName,
			userIdNonSso,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// returns json auth info for SSO user with access to only the single org
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantIdSso},
			OrganizationIds: []string{orgIdSso},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// returns json auth info for non-SSO user with access to both orgs
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         strings.ToUpper(string(email[0])) + email[1:],
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       false,
			TenantIDs:       []string{tenantIdSso, tenantIdNonSso},
			OrganizationIds: []string{orgIdSso, orgIdNonSso},
		}, authInfo)

		// only SSO user should see org in sso_only mode
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgIdSso,
		)
		assert.NoError(t, err)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantIdSso},
			OrganizationIds: []string{orgIdSso},
		}, authInfo)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       false,
			TenantIDs:       []string{tenantIdNonSso},
			OrganizationIds: []string{orgIdNonSso},
		}, authInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("user with same email address can be used across SSO providers", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000008"
		orgId2 := "00000000-0000-0000-0000-000000000009"
		tenantId1 := "00000000-0000-0000-0000-000000000010"
		tenantId2 := "00000000-0000-0000-0000-000000000011"
		userIdExisting := "00000000-0000-0000-0000-000000000002"
		userIdNew := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId1 := orgId1
		samlProviderId2 := orgId2
		_, err := dbpool.Exec(
			context.Background(),
			`
			WITH org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org sso 1', $3),
					($2, 'test org sso 2', $3)
				returning *
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values
					($4, 'test tenant sso', $6, $1),
					($5, 'test tenant non-sso', $6, $2)
				returning *
			),

			-- use org ID as provider ID for simplicity and to avoid primary key conflict
			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select org.id, org.id, (select 'https://' || org.id || '.com'), array[ten.id], (select id from roles where name = 'WORKSPACE_ADMIN')
				from org join ten on ten.organization_id = org.id
				returning *
			),

			usr as (
				insert into users (id, email, full_name) select $3, $7, $8 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider where organization_id = $1), now() from usr u
			),

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, (select id from usr), org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
				from org returning id
			)

			-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, (select id from usr), ten.id, ten.organization_id, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', org.id, (select ls_user_id from usr)
			from ten join org on ten.organization_id = org.id`,
			orgId1,
			orgId2,
			userIdExisting,
			tenantId1,
			tenantId2,
			`{}`,
			email,
			fullName,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso := tokenForSSO(ah, userIdNew, email, fullName, samlProviderId2)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// should succeed to insert a new SSO user with the same email address
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailOrUserIdQuery,
			email,
			userIdNew,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
			providers[i].LSUserID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userIdExisting, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userIdNew, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("user with same provider user ID can be used across SSO providers", func(t *testing.T) {
		orgId1 := "00000000-0000-0000-0000-000000000008"
		orgId2 := "00000000-0000-0000-0000-000000000009"
		tenantId1 := "00000000-0000-0000-0000-000000000010"
		tenantId2 := "00000000-0000-0000-0000-000000000011"
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		emailNew := "<EMAIL>"
		fullName := "sso"
		samlProviderId1 := orgId1
		samlProviderId2 := orgId2
		_, err := dbpool.Exec(
			context.Background(),
			`
			WITH org as (
				insert into organizations (id, display_name, created_by_user_id)
				values
					($1, 'test org sso 1', $3),
					($2, 'test org sso 2', $3)
				returning *
			),

			ten as (
				insert into tenants (id, display_name, config, organization_id)
				values
					($4, 'test tenant sso', $6, $1),
					($5, 'test tenant non-sso', $6, $2)
				returning *
			),

			-- use org ID as provider ID for simplicity and to avoid primary key conflict
			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select org.id, org.id, (select 'https://' || org.id || '.com'), array[ten.id], (select id from roles where name = 'WORKSPACE_ADMIN')
				from org join ten on ten.organization_id = org.id
				returning *
			),

			usr as (
				insert into users (id, email, full_name) select $3, $7, $8 returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider where organization_id = $1), now() from usr u
			),

			-- use org ID as identity ID for simplicity and to avoid primary key conflict
			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select org.id, (select id from usr), org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr)
				from org returning id
			)

			-- use tenant ID as identity ID for simplicity and to avoid primary key conflict
			insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, (select id from usr), ten.id, ten.organization_id, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', org.id, (select ls_user_id from usr)
			from ten join org on ten.organization_id = org.id`,
			orgId1,
			orgId2,
			userId,
			tenantId1,
			tenantId2,
			`{}`,
			email,
			fullName,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso := tokenForSSO(ah, userId, email, fullName, samlProviderId2)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// should succeed to insert a new SSO user with the same email address
		// and same user ID from different provider
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailOrUserIdQuery,
			email,
			userId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
			providers[i].LSUserID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// should fail to insert a new SSO user with a different email address
		// and same user ID from different provider
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		tokenNewEmail := tokenForSSO(ah, userId, emailNew, fullName, samlProviderId2)
		req.Header.Set("Authorization", "Bearer "+tokenNewEmail)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailQuery,
			emailNew,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 0)
		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForEmailQuery,
			email,
		)
		assert.NoError(t, err)
		providersOldEmail, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providersOldEmail, 2)
		for i := range providersOldEmail {
			providersOldEmail[i].ID = ""
			providersOldEmail[i].LSUserID = ""
		}
		expectedProviderUsers = []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				ProviderUserID: sql.NullString{String: userId, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providersOldEmail)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("link new SSO user to existing non-SSO user", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)

		// set email_confirmed_at
		_, err = dbpool.Exec(
			context.Background(),
			`update provider_users set email_confirmed_at = now() where provider_user_id = $1`,
			userIdSso,
		)
		assert.NoError(t, err)

		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// only SSO user should see org in sso_only mode
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       false,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("link multiple new SSO users with same email and user ID to existing non-SSO user", func(t *testing.T) {
		// create non-sso user in one org and two other orgs with SAML providers
		userId, err := uuid.NewV4()
		assert.NoError(t, err)
		userIdStr := userId.String()
		userNonSso := UserSetup(t, dbpool, userIdStr, emailSupabase, userNameSupabase, "supabase:non-sso", nil, &userIdStr)
		orgIdNonSso := OrgSetup(t, dbpool, "test org non sso", false, userIdStr)
		wsIdNonSso := TenantSetup(t, dbpool, orgIdNonSso, "test tenant", "non-sso", &auth.TenantConfig{}, true)
		orgIdentityIdNonSso := IdentitySetup(t, dbpool, userIdStr, orgIdNonSso, nil, "ORGANIZATION_USER", "organization", nil, userNonSso.LSUserID)
		IdentitySetup(t, dbpool, userIdStr, orgIdNonSso, &wsIdNonSso, "WORKSPACE_ADMIN", "workspace", &orgIdentityIdNonSso, userNonSso.LSUserID)
		orgIdSso1 := OrgSetup(t, dbpool, "test org sso 1", false, userIdStr)
		wsIdSso1 := TenantSetup(t, dbpool, orgIdSso1, "test tenant sso", "sso1", &auth.TenantConfig{}, true)
		samlProviderId1 := SamlProviderSetup(t, dbpool, orgIdSso1, "https://fake1.com", []string{wsIdSso1}, "WORKSPACE_ADMIN")
		orgIdentityIdSso1 := IdentitySetup(t, dbpool, userIdStr, orgIdSso1, nil, "ORGANIZATION_USER", "organization", nil, userNonSso.LSUserID)
		IdentitySetup(t, dbpool, userIdStr, orgIdSso1, &wsIdSso1, "WORKSPACE_ADMIN", "workspace", &orgIdentityIdSso1, userNonSso.LSUserID)
		orgIdSso2 := OrgSetup(t, dbpool, "test org sso 2", false, userIdStr)
		wsIdSso2 := TenantSetup(t, dbpool, orgIdSso2, "test tenant sso", "sso2", &auth.TenantConfig{}, true)
		samlProviderId2 := SamlProviderSetup(t, dbpool, orgIdSso2, "https://fake2.com", []string{wsIdSso2}, "WORKSPACE_ADMIN")
		orgIdentityIdSso2 := IdentitySetup(t, dbpool, userIdStr, orgIdSso2, nil, "ORGANIZATION_USER", "organization", nil, userNonSso.LSUserID)
		IdentitySetup(t, dbpool, userIdStr, orgIdSso2, &wsIdSso2, "WORKSPACE_ADMIN", "workspace", &orgIdentityIdSso2, userNonSso.LSUserID)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso1 := tokenForSSO(ah, userIdStr, emailSupabase, userNameSupabase, samlProviderId1)
		r.Header.Set("Authorization", "Bearer "+tokenSso1)

		// returns json auth info without access to org before email is confirmed
		rtr := chi.NewRouter().With(TestLogger(t))
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso1)
		res, err := srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdStr,
			UserEmail:       emailSupabase,
			UserFullName:    userNameSupabase,
			IsSsoUser:       true,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)

		// set email_confirmed_at
		_, err = dbpool.Exec(
			context.Background(),
			`update provider_users set email_confirmed_at = now() where provider_user_id = $1 and saml_provider_id = $2`,
			userIdStr,
			samlProviderId1,
		)
		assert.NoError(t, err)

		// returns SSO auth info
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso1)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdStr,
			UserEmail:       emailSupabase,
			UserFullName:    userNameSupabase,
			IsSsoUser:       true,
			TenantIDs:       []string{wsIdSso1},
			OrganizationIds: []string{orgIdSso1},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdStr, Valid: true},
				Email:          sql.NullString{String: emailSupabase, Valid: true},
				FullName:       sql.NullString{String: userNameSupabase, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdStr, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: emailSupabase, Valid: true},
				FullName:       sql.NullString{String: userNameSupabase, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// only SSO user should see org in sso_only mode
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id in ($1, $2)`,
			orgIdSso1,
			orgIdSso2,
		)
		assert.NoError(t, err)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso1)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdStr,
			UserEmail:       emailSupabase,
			UserFullName:    userNameSupabase,
			IsSsoUser:       true,
			TenantIDs:       []string{wsIdSso1},
			OrganizationIds: []string{orgIdSso1},
		}, authInfo)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdStr,
			"email":         emailSupabase,
			"user_metadata": map[string]interface{}{"full_name": userNameSupabase},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdStr,
			UserEmail:       emailSupabase,
			UserFullName:    userNameSupabase,
			IsSsoUser:       false,
			TenantIDs:       []string{wsIdNonSso},
			OrganizationIds: []string{orgIdNonSso},
		}, authInfo)

		// link 2nd SSO user
		r = httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso2 := tokenForSSO(ah, userIdStr, emailSupabase, userNameSupabase, samlProviderId2)
		r.Header.Set("Authorization", "Bearer "+tokenSso2)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso2)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdStr,
			UserEmail:       emailSupabase,
			UserFullName:    userNameSupabase,
			IsSsoUser:       true,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)

		// set email_confirmed_at
		_, err = dbpool.Exec(
			context.Background(),
			`update provider_users set email_confirmed_at = now() where provider_user_id = $1 and saml_provider_id = $2`,
			userIdStr,
			samlProviderId2,
		)
		assert.NoError(t, err)

		// returns SSO auth info
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso2)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId = authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdStr,
			UserEmail:       emailSupabase,
			UserFullName:    userNameSupabase,
			IsSsoUser:       true,
			TenantIDs:       []string{wsIdSso2},
			OrganizationIds: []string{orgIdSso2},
		}, authInfo)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 3)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers = []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdStr, Valid: true},
				Email:          sql.NullString{String: emailSupabase, Valid: true},
				FullName:       sql.NullString{String: userNameSupabase, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdStr, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId1, Valid: true},
				Email:          sql.NullString{String: emailSupabase, Valid: true},
				FullName:       sql.NullString{String: userNameSupabase, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdStr, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId2, Valid: true},
				Email:          sql.NullString{String: emailSupabase, Valid: true},
				FullName:       sql.NullString{String: userNameSupabase, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("linked login methods already exist", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
			),

			provider_user_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', $10, (select provider_id from saml_provider) from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			userIdSso,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		r.Header.Set("Authorization", "Bearer "+tokenSso)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)

		// set email_confirmed_at
		_, err = dbpool.Exec(
			context.Background(),
			`update provider_users set email_confirmed_at = now() where provider_user_id = $1`,
			userIdSso,
		)
		assert.NoError(t, err)

		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// only SSO user should see org in sso_only mode
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       false,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("delete linked SSO login method SAML provider", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			-- use SSO user ID to simulate SSO-first user
			usr as (
				insert into users (id, email, full_name) select $10, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', $6, u.full_name from usr u
			),

			provider_user_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', $10, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			userIdSso,
		)
		assert.NoError(t, err)

		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)

		// returns json auth info for both users
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		// SSO user
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		// non-SSO user
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		// delete SAML provider
		_, err = dbpool.Exec(
			context.Background(),
			`delete from saml_providers where provider_id = $1`,
			samlProviderId,
		)
		assert.NoError(t, err)

		// SSO user should fail auth
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)

		// non-SSO user should still be able to access org
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("change linked non-SSO login method email SSO-first", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		newEmail := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			-- use SSO user ID to simulate SSO-first user
			usr as (
				insert into users (id, email, full_name) select $10, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', $6, u.full_name from usr u
			),

			provider_user_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', $10, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			userIdSso,
		)
		assert.NoError(t, err)

		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)

		// returns json auth info for both users
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		// SSO user
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		// non-SSO user
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		// change non-SSO email should fail
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSsoNewEmail, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         newEmail,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSsoNewEmail)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)

		// change SSO email should also fail
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		tokenSsoNewEmail := tokenForSSO(ah, userIdSso, newEmail, fullName, samlProviderId)
		req.Header.Set("Authorization", "Bearer "+tokenSsoNewEmail)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("change linked non-SSO login method email non-SSO first", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		newEmail := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
			),

			provider_user_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', $10, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			userIdSso,
		)
		assert.NoError(t, err)

		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)

		// returns json auth info for both users
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		// SSO user
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		// non-SSO user
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		// change non-SSO email should fail
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSsoNewEmail, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         newEmail,
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSsoNewEmail)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)

		// change SSO email should also fail
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		tokenSsoNewEmail := tokenForSSO(ah, userIdSso, newEmail, fullName, samlProviderId)
		req.Header.Set("Authorization", "Bearer "+tokenSsoNewEmail)
		res, err = srv.Client().Do(req)
		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("change linked login method full name", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"
		newName := "changed"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, provider, provider_user_id, full_name)
				select u.ls_user_id, u.email, 'supabase:non-sso', u.id, u.full_name from usr u
			),

			provider_user_sso as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', $10, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdNonSso,
			orgIdentityId,
			samlProviderId,
			email,
			userIdSso,
		)
		assert.NoError(t, err)

		tokenSso := tokenForSSO(ah, userIdSso, email, newName, samlProviderId)

		// returns json auth info for both users
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		// SSO user
		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    newName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: newName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// non-SSO user
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         email,
			"user_metadata": map[string]interface{}{"full_name": newName},
		})
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)

		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    newName,
			IsSsoUser:       false,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers = []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: newName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: newName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// users record should be updated as well
		rows, err = dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			email,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: userIdNonSso, UserEmail: email, UserFullName: newName, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("link new non-SSO user to existing SSO user", func(t *testing.T) {
		userIdNonSso := "00000000-0000-0000-0000-000000000002"
		userIdSso := "00000000-0000-0000-0000-000000000003"
		email := "<EMAIL>"
		fullName := "hello"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		orgIdentityId := "00000000-0000-0000-0000-000000000006"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			),

			ten as (
				insert into tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				values ($1, $2, 'yo', $3, (select id from org), false)
				returning id
			),

			saml_provider as (
				insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
				select $8, org.id, 'https://fake.com', array[(SELECT id FROM ten)], (select id from roles where name = 'WORKSPACE_ADMIN') 
				from org returning provider_id
			),

			usr as (
				insert into users (id, email, full_name) select $6, $9, 'hello' returning *
			),

			provider_user as (
				insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id, saml_provider_id, email_confirmed_at)
				select u.ls_user_id, u.email, u.full_name, 'supabase:sso', u.id, (select provider_id from saml_provider), now() from usr u
			),

			org_identity as (
				insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
				select $7, usr.id, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', usr.ls_user_id from org left join usr on true returning id
			)

			insert into identities (id, user_id, tenant_id, organization_id, read_only, role_id, access_scope, parent_identity_id, ls_user_id)
			select ten.id, usr.id, ten.id, org.id, true, (select id from roles where name = 'WORKSPACE_VIEWER'), 'workspace', $7, ls_user_id
			from ten cross join org left join usr on true`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userIdSso,
			orgIdentityId,
			samlProviderId,
			email,
		)
		assert.NoError(t, err)

		r := httptest.NewRequest("GET", "/current/tenants", nil)
		_, tokenNonSso, _ := ah.Jwt.Encode(map[string]interface{}{
			"sub":           userIdNonSso,
			"email":         strings.ToUpper(string(email[0])) + email[1:],
			"user_metadata": map[string]interface{}{"full_name": fullName},
		})
		r.Header.Set("Authorization", "Bearer "+tokenNonSso)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		lsUserId := authResponse.LSUserID
		authResponse.LSUserID = ""
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			lsUserId,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 2)
		for i := range providers {
			providers[i].ID = ""
		}
		expectedProviderUsers := []auth.ProviderUserInfo{
			{
				Provider:       sql.NullString{String: "supabase:non-sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdNonSso, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
			{
				Provider:       sql.NullString{String: "supabase:sso", Valid: true},
				LSUserID:       lsUserId,
				ProviderUserID: sql.NullString{String: userIdSso, Valid: true},
				SAMLProviderID: sql.NullString{String: samlProviderId, Valid: true},
				Email:          sql.NullString{String: email, Valid: true},
				FullName:       sql.NullString{String: fullName, Valid: true},
			},
		}
		assert.ElementsMatch(t, expectedProviderUsers, providers)

		// only SSO user should see org in sso_only mode
		_, err = dbpool.Exec(
			context.Background(),
			`update organizations set sso_only = true where id = $1`,
			orgId,
		)
		assert.NoError(t, err)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		tokenSso := tokenForSSO(ah, userIdSso, email, fullName, samlProviderId)
		req.Header.Set("Authorization", "Bearer "+tokenSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       true,
			TenantIDs:       []string{tenantId},
			OrganizationIds: []string{orgId},
		}, authInfo)
		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+tokenNonSso)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			IsSsoUser:       authResponse.IsSsoUser,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          userIdNonSso,
			UserEmail:       email,
			UserFullName:    fullName,
			IsSsoUser:       false,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users")

	t.Run("SSO user fails without SAML provider", func(t *testing.T) {
		userId := "00000000-0000-0000-0000-000000000002"
		email := "<EMAIL>"
		fullName := "sso"
		samlProviderId := "00000000-0000-0000-0000-000000000007"
		tenantId := "00000000-0000-0000-0000-000000000005"
		_, err := dbpool.Exec(
			context.Background(),
			`with
			org as (
				insert into organizations (id, display_name, is_personal, created_by_user_id) values ($4, $5, true, $6) returning id
			)

			insert into tenants (id, display_name, tenant_handle, config, organization_id)
				values ($1, $2, 'yo', $3, (select id from org))
			returning id
			`,
			tenantId,
			"test tenant sso",
			`{"max_identities": 4}`,
			orgId,
			"test org",
			userId,
		)
		assert.NoError(t, err)

		token := tokenForSSO(ah, userId, email, fullName, samlProviderId)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusForbidden, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		assert.Equal(t, []byte(`{"error":"Forbidden"}
`), body)
	})
}
