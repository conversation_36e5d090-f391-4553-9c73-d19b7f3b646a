package auth_test

import (
	"context"
	"testing"
	"time"

	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/metronome"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/util"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
)

func TestIsPaymentEnabled(t *testing.T) {
	originalAuthType := config.Env.AuthType
	originalPaymentEnabled := config.Env.PaymentEnabled
	defer func() {
		config.Env.AuthType = originalAuthType
		config.Env.PaymentEnabled = originalPaymentEnabled
	}()

	testCases := []struct {
		authType       string
		paymentEnabled bool
		expected       bool
	}{
		{"none", false, false},
		{"none", true, false},
		{"some_auth", false, false},
		{"some_auth", true, true},
	}

	for _, tc := range testCases {
		config.Env.AuthType = tc.authType
		config.Env.PaymentEnabled = tc.paymentEnabled
		result := auth.IsPaymentEnabled()
		if result != tc.expected {
			t.Errorf("isPaymentEnabled() with AuthType=%s, PaymentEnabled=%v expected %v, got %v",
				tc.authType, tc.paymentEnabled, tc.expected, result)
		}
	}
}

func TestUpdateTenantConfigFromMetronomeObj(t *testing.T) {
	tenantConfig := auth.TenantConfig{
		MaxRunRules: 1, // initial value
	}

	metronomeObj := map[string]interface{}{
		"data": map[string]interface{}{
			"custom_fields": map[string]interface{}{
				"max_run_rules": 10,
			},
		},
	}

	updatedConfig, err := auth.UpdateTenantConfigFromMetronomeObj(tenantConfig, metronomeObj)
	assert.NoError(t, err)
	assert.Equal(t, 10, updatedConfig.MaxRunRules)
}

func TestUpdateOrganizationConfigFromMetronomeObj(t *testing.T) {
	orgConfig := auth.OrganizationConfig{
		MaxIdentities: 5, // initial value
	}

	metronomeObj := map[string]interface{}{
		"data": map[string]interface{}{
			"custom_fields": map[string]interface{}{
				"max_identities": 50,
			},
		},
	}

	updatedConfig, err := auth.UpdateOrganizationConfigFromMetronomeObj(orgConfig, metronomeObj)
	assert.NoError(t, err)
	assert.Equal(t, 50, updatedConfig.MaxIdentities)
}

func TestGetOrgTenantConfig(t *testing.T) {
	// Set up environment variables
	config.Env.AuthType = "some_auth"
	config.Env.PaymentEnabled = true

	// Connect to database
	dbpool := database.PgConnect()
	defer dbpool.Close()
	defer func() {
		_, err := dbpool.Exec(context.Background(), "DELETE FROM organizations;")
		if err != nil {
			t.Error(err)
		}
	}()

	// Connect to Redis
	queueingRedisClient := lsredis.SingleRedisConnect()
	defer func() {
		err := queueingRedisClient.Close()
		if err != nil {
			t.Error(err)
		}
	}()

	ctx := context.Background()

	// Create an organization
	orgID := uuid.New()
	_, err := dbpool.Exec(ctx, `
        INSERT INTO organizations (id, display_name, is_personal, metronome_customer_id, created_by_user_id)
        VALUES ($1, $2, $3, $4, $5)
    `, orgID, "Test Org", false, "test-customer-id", uuid.New())
	assert.NoError(t, err)

	planID := uuid.New()

	// Insert metronome cache data into the database
	cacheRow := metronome.MetronomeCacheRow{
		ID:             uuid.New(),
		OrganizationID: orgID,
		CustomerID:     nil,
		PlanID:         &planID,
		CustomerDetails: map[string]interface{}{
			"data": map[string]interface{}{
				"custom_fields": map[string]interface{}{
					"max_identities": 50,
				},
			},
		},
		PlanDetails: map[string]interface{}{
			"data": map[string]interface{}{
				"custom_fields": map[string]interface{}{
					"max_run_rules": 10,
				},
			},
		},
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}

	_, err = dbpool.Exec(ctx, `
        INSERT INTO org_metronome_cache
        (id, organization_id, customer_id, plan_id, customer_details, plan_details, created_at, updated_at)
        VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8)
    `, cacheRow.ID, cacheRow.OrganizationID, cacheRow.CustomerID, cacheRow.PlanID,
		cacheRow.CustomerDetails, cacheRow.PlanDetails, cacheRow.CreatedAt, cacheRow.UpdatedAt)
	assert.NoError(t, err)

	// Prepare initial configs
	orgConfig := auth.OrganizationConfig{
		MaxIdentities: 5, // initial value
	}
	tenantConfig := auth.TenantConfig{
		MaxRunRules: 1,
	}

	// Call GetOrgTenantConfig
	updatedOrgConfig, updatedTenantConfig, hasPlan, err := auth.GetOrgTenantConfigSource(
		ctx,
		dbpool,
		queueingRedisClient,
		orgConfig,
		tenantConfig,
		orgID.String(),
		false, // isPersonal
		"test-customer-id",
	)
	assert.NoError(t, err)

	// Verify that the configs are updated
	assert.Equal(t, 50, updatedOrgConfig.MaxIdentities)  // From CustomerDetails
	assert.Equal(t, 10, updatedTenantConfig.MaxRunRules) // From PlanDetails
	assert.NotNil(t, hasPlan)
	assert.True(t, *hasPlan)
}

func TestGetResolvedOrgConfig(t *testing.T) {
	// Set up environment variables
	config.Env.AuthType = "some_auth"
	config.Env.PaymentEnabled = true

	// Connect to database
	dbpool := database.PgConnect()
	defer dbpool.Close()
	defer func() {
		_, err := dbpool.Exec(context.Background(), "DELETE FROM organizations;")
		if err != nil {
			t.Error(err)
		}
	}()

	// Connect to Redis
	queueingRedisClient := lsredis.SingleRedisConnect()
	defer func() {
		err := queueingRedisClient.Close()
		if err != nil {
			t.Error(err)
		}
	}()

	ctx := context.Background()

	// Create an organization
	orgID := uuid.New()
	_, err := dbpool.Exec(ctx, `
        INSERT INTO organizations (id, display_name, is_personal, metronome_customer_id, created_by_user_id)
        VALUES ($1, $2, $3, $4, $5)
    `, orgID, "Test Org", false, "test-customer-id", uuid.New())
	assert.NoError(t, err)

	planID := uuid.New()

	// Insert metronome cache data into the database
	cacheRow := metronome.MetronomeCacheRow{
		ID:             uuid.New(),
		OrganizationID: orgID,
		CustomerID:     nil,
		PlanID:         &planID,
		CustomerDetails: map[string]interface{}{
			"data": map[string]interface{}{
				"custom_fields": map[string]interface{}{
					"max_identities": 100,
				},
			},
		},
		PlanDetails: map[string]interface{}{
			"data": map[string]interface{}{
				"custom_fields": map[string]interface{}{
					"additional_field": "value",
				},
			},
		},
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}

	_, err = dbpool.Exec(ctx, `
        INSERT INTO org_metronome_cache
        (id, organization_id, customer_id, plan_id, customer_details, plan_details, created_at, updated_at)
        VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8)
    `, cacheRow.ID, cacheRow.OrganizationID, cacheRow.CustomerID, cacheRow.PlanID,
		cacheRow.CustomerDetails, cacheRow.PlanDetails, cacheRow.CreatedAt, cacheRow.UpdatedAt)
	assert.NoError(t, err)

	// Prepare initial config
	orgConfig := auth.OrganizationConfig{
		MaxIdentities: 5, // initial value
	}

	// Call GetResolvedOrgConfig
	updatedOrgConfig, hasPlan, err := auth.GetResolvedOrgConfig(
		ctx,
		dbpool,
		queueingRedisClient,
		orgConfig,
		orgID.String(),
		false, // isPersonal
		"test-customer-id",
	)
	assert.NoError(t, err)

	// Verify that the config is updated
	assert.Equal(t, 100, updatedOrgConfig.MaxIdentities) // From CustomerDetails
	assert.NotNil(t, hasPlan)
	assert.True(t, *hasPlan)
}

func TestGetOrgTenantConfig_PersonalOrg(t *testing.T) {
	// Set up environment variables
	config.Env.AuthType = "some_auth"
	config.Env.PaymentEnabled = false // To avoid Metronome client calls
	orgID := uuid.New().String()

	ctx := context.Background()

	// Prepare initial configs
	orgConfig := auth.OrganizationConfig{
		MaxIdentities: 5, // initial value
	}
	tenantConfig := auth.TenantConfig{
		MaxRunRules: 1, // initial value
	}

	// Call GetOrgTenantConfig with isPersonal=true
	updatedOrgConfig, _, _, err := auth.GetOrgTenantConfigSource(
		ctx,
		nil, // dbpool not needed
		nil, // queueingRedisClient not needed
		orgConfig,
		tenantConfig,
		orgID,
		true, // isPersonal
		"",
	)
	assert.NoError(t, err)

	// Verify that MaxIdentities is set to 1
	assert.Equal(t, 1, updatedOrgConfig.MaxIdentities)
}

func TestGetResolvedOrgConfig_PaymentDisabled(t *testing.T) {
	// Set up environment variables
	config.Env.AuthType = "some_auth"
	config.Env.PaymentEnabled = false // Payment is disabled
	orgID := uuid.New().String()

	ctx := context.Background()

	// Prepare initial config
	orgConfig := auth.OrganizationConfig{
		MaxIdentities: 5, // initial value
	}

	// Call GetResolvedOrgConfig
	updatedOrgConfig, hasPlan, err := auth.GetResolvedOrgConfig(
		ctx,
		nil, // dbpool not needed
		nil, // queueingRedisClient not needed
		orgConfig,
		orgID,
		false, // isPersonal
		"",
	)
	assert.NoError(t, err)

	// Verify that MaxIdentities remains unchanged
	assert.Equal(t, 5, updatedOrgConfig.MaxIdentities)
	// Verify that hasPlan is nil when payment is disabled
	assert.Nil(t, hasPlan)
}

func TestUpdateConfigFromOrgConfig(t *testing.T) {
	// Case 1: TenantConfig fields are unset, OrgConfig fields are set
	tenantConfig := auth.TenantConfig{}
	orgConfig := auth.OrganizationConfig{
		MaxIdentities:           100,
		CanUseRbac:              util.BoolPtr(true),
		CanAddSeats:             util.BoolPtr(false),
		StartupPlanApprovalDate: util.StringPtr("2023-01-01"),
		PremierPlanApprovalDate: util.StringPtr("2023-06-01"),
		// Add other fields as needed
	}

	updatedConfig := auth.UpdateConfigFromOrgConfig(tenantConfig, orgConfig)

	// Verify that fields are updated from OrgConfig
	assert.NotNil(t, updatedConfig.MaxIdentities)
	assert.Equal(t, 100, *updatedConfig.MaxIdentities)
	assert.NotNil(t, updatedConfig.CanUseRbac)
	assert.True(t, *updatedConfig.CanUseRbac)
	assert.NotNil(t, updatedConfig.CanAddSeats)
	assert.False(t, *updatedConfig.CanAddSeats)
	assert.NotNil(t, updatedConfig.StartupPlanApprovalDate)
	assert.Equal(t, "2023-01-01", *updatedConfig.StartupPlanApprovalDate)
	assert.NotNil(t, updatedConfig.PremierPlanApprovalDate)
	assert.Equal(t, "2023-06-01", *updatedConfig.PremierPlanApprovalDate)

	// Case 2: TenantConfig fields are set, OrgConfig fields are set
	tenantConfig = auth.TenantConfig{
		MaxIdentities:           util.IntPtr(50),
		CanUseRbac:              util.BoolPtr(false),
		StartupPlanApprovalDate: util.StringPtr("2022-01-01"),
		// Other fields as needed
	}
	orgConfig = auth.OrganizationConfig{
		MaxIdentities:           100,
		CanUseRbac:              util.BoolPtr(true),
		StartupPlanApprovalDate: util.StringPtr("2023-01-01"),
	}

	updatedConfig = auth.UpdateConfigFromOrgConfig(tenantConfig, orgConfig)

	// Verify that TenantConfig fields are not overwritten
	assert.NotNil(t, updatedConfig.MaxIdentities)
	assert.Equal(t, 50, *updatedConfig.MaxIdentities)
	assert.NotNil(t, updatedConfig.CanUseRbac)
	assert.False(t, *updatedConfig.CanUseRbac)
	assert.NotNil(t, updatedConfig.StartupPlanApprovalDate)
	assert.Equal(t, "2022-01-01", *updatedConfig.StartupPlanApprovalDate)

	// Case 3: TenantConfig fields are unset, OrgConfig fields are unset
	tenantConfig = auth.TenantConfig{}
	orgConfig = auth.OrganizationConfig{}

	updatedConfig = auth.UpdateConfigFromOrgConfig(tenantConfig, orgConfig)

	// Verify that fields remain unset
	assert.Nil(t, updatedConfig.MaxIdentities)
	assert.Nil(t, updatedConfig.CanUseRbac)
	assert.Nil(t, updatedConfig.CanAddSeats)
	assert.Nil(t, updatedConfig.StartupPlanApprovalDate)
	assert.Nil(t, updatedConfig.PremierPlanApprovalDate)
}
