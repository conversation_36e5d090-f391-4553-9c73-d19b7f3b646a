package auth

// Permission represents a specific permission as a string.
type Permission string

const (
	// Workspace permissions

	// Annotation Queues
	AnnotationQueuesCreate Permission = "annotation-queues:create"
	AnnotationQueuesDelete Permission = "annotation-queues:delete"
	AnnotationQueuesRead   Permission = "annotation-queues:read"
	AnnotationQueuesUpdate Permission = "annotation-queues:update"

	// Charts
	ChartsCreate Permission = "charts:create"
	ChartsDelete Permission = "charts:delete"
	ChartsRead   Permission = "charts:read"
	ChartsUpdate Permission = "charts:update"

	// Datasets
	DatasetsCreate Permission = "datasets:create"
	DatasetsDelete Permission = "datasets:delete"
	DatasetsRead   Permission = "datasets:read"
	DatasetsShare  Permission = "datasets:share"
	DatasetsUpdate Permission = "datasets:update"

	// Deployments
	DeploymentsCreate Permission = "deployments:create"
	DeploymentsDelete Permission = "deployments:delete"
	DeploymentsRead   Permission = "deployments:read"
	DeploymentsUpdate Permission = "deployments:update"

	// Feedback
	FeedbackCreate Permission = "feedback:create"
	FeedbackDelete Permission = "feedback:delete"
	FeedbackRead   Permission = "feedback:read"
	FeedbackUpdate Permission = "feedback:update"

	// Projects
	ProjectsCreate Permission = "projects:create"
	ProjectsDelete Permission = "projects:delete"
	ProjectsRead   Permission = "projects:read"
	ProjectsUpdate Permission = "projects:update"

	// Prompts
	PromptsCreate Permission = "prompts:create"
	PromptsDelete Permission = "prompts:delete"
	PromptsRead   Permission = "prompts:read"
	PromptsUpdate Permission = "prompts:update"
	PromptsShare  Permission = "prompts:share"

	// Rules
	RulesCreate Permission = "rules:create"
	RulesDelete Permission = "rules:delete"
	RulesRead   Permission = "rules:read"
	RulesUpdate Permission = "rules:update"

	// Runs
	RunsCreate Permission = "runs:create"
	RunsRead   Permission = "runs:read"
	RunsShare  Permission = "runs:share"
	RunsDelete Permission = "runs:delete"
	// Workspaces
	WorkspacesManage Permission = "workspaces:manage"
	WorkspacesRead   Permission = "workspaces:read"

	// Alerts
	AlertsCreate Permission = "alerts:create"
	AlertsUpdate Permission = "alerts:update"
	AlertsDelete Permission = "alerts:delete"
	AlertsRead   Permission = "alerts:read"
)
