package auth_test

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/util"
)

func TestInitializeAndUnmarshalConfig(t *testing.T) {
	os.Setenv("DEFAULT_FEATURE_MAX_EVENTS_INGESTED_PER_MINUTE", "30000")
	os.Setenv("DEFAULT_ORG_FEATURE_CAN_USE_RBAC", "true")

	defer func() {
		os.Unsetenv("DEFAULT_FEATURE_MAX_EVENTS_INGESTED_PER_MINUTE")
		os.Unsetenv("DEFAULT_ORG_FEATURE_CAN_USE_RBAC")
	}()

	// DB values (simulated)
	dbTenantConfig := &auth.TenantConfig{
		MaxEventsIngestedPerMinute: 20000, // Set from DB
	}
	dbOrgConfig := &auth.OrganizationConfig{
		MaxWorkspaces: 5, // Set from DB
	}

	// AuthInfo with configs from DB
	authInfo := &auth.AuthInfo{
		TenantConfig:       dbTenantConfig,
		OrganizationConfig: dbOrgConfig,
	}

	auth.InitializeAndUnmarshalConfig(authInfo)

	// Assertions
	// TenantConfig assertions
	assert.Equal(t, 20000, authInfo.TenantConfig.MaxEventsIngestedPerMinute, "TenantConfig.MaxEventsIngestedPerMinute should be from DB")
	assert.Equal(t, 105000, authInfo.TenantConfig.MaxHourlyTracingRequests, "TenantConfig.MaxHourlyTracingRequests should be default")

	// OrganizationConfig assertions
	assert.Equal(t, 5, authInfo.OrganizationConfig.MaxWorkspaces, "OrganizationConfig.MaxWorkspaces should be from DB")
	assert.Equal(t, util.BoolPtr(true), authInfo.OrganizationConfig.CanUseRbac, "OrganizationConfig.CanUseRbac should be from env var")

	// Ensure defaults are set
	assert.Equal(t, 5, authInfo.OrganizationConfig.MaxIdentities, "OrganizationConfig.MaxIdentities should be default")
	assert.Equal(t, util.BoolPtr(true), authInfo.OrganizationConfig.CanAddSeats, "OrganizationConfig.CanAddSeats should be default")
}

func TestInitializeAndUnmarshalOrgConfig(t *testing.T) {
	os.Setenv("DEFAULT_ORG_FEATURE_MAX_WORKSPACES", "10")
	os.Setenv("DEFAULT_ORG_FEATURE_CAN_USE_SAML_SSO", "true")

	// Clean up after test
	defer func() {
		os.Unsetenv("DEFAULT_ORG_FEATURE_MAX_WORKSPACES")
		os.Unsetenv("DEFAULT_ORG_FEATURE_CAN_USE_SAML_SSO")
	}()

	// DB values (simulated)
	dbOrgConfig := &auth.OrganizationConfig{
		MaxIdentities: 50, // Set from DB
	}

	// OrgAuthInfo with config from DB
	orgAuthInfo := &auth.OrgAuthInfo{
		OrganizationConfig: dbOrgConfig,
	}

	// Call the function under test
	auth.InitializeAndUnmarshalOrgConfig(orgAuthInfo)

	// Assertions
	// OrganizationConfig assertions
	assert.Equal(t, 50, orgAuthInfo.OrganizationConfig.MaxIdentities, "MaxIdentities should be from DB")
	assert.Equal(t, 10, orgAuthInfo.OrganizationConfig.MaxWorkspaces, "MaxWorkspaces should be from env var")
	assert.Equal(t, util.BoolPtr(true), orgAuthInfo.OrganizationConfig.CanUseSamlSso, "CanUseSamlSso should be from env var")

	// Ensure defaults are set
	assert.Equal(t, util.BoolPtr(true), orgAuthInfo.OrganizationConfig.CanAddSeats, "CanAddSeats should be default")
	assert.Equal(t, util.BoolPtr(false), orgAuthInfo.OrganizationConfig.CanUseRbac, "CanUseRbac should be default")
}
func TestInitializeAndUnmarshalConfig_NilValues(t *testing.T) {
	os.Setenv("DEFAULT_ORG_FEATURE_CAN_USE_RBAC", "true")
	defer os.Unsetenv("DEFAULT_ORG_FEATURE_CAN_USE_RBAC")

	// Test with completely nil configs
	authInfo := &auth.AuthInfo{}

	auth.InitializeAndUnmarshalConfig(authInfo)

	// Verify initialization
	assert.NotNil(t, authInfo.TenantConfig, "TenantConfig should be initialized")
	assert.NotNil(t, authInfo.OrganizationConfig, "OrganizationConfig should be initialized")
	assert.NotNil(t, authInfo.TenantConfig.OrganizationConfig, "Nested OrganizationConfig should be initialized")

	// Verify they point to the same instance
	assert.Equal(t, authInfo.OrganizationConfig, authInfo.TenantConfig.OrganizationConfig,
		"OrganizationConfig instances should be the same")

	// Verify default values are set
	assert.Equal(t, 105000, authInfo.TenantConfig.MaxHourlyTracingRequests,
		"Should set default value for MaxHourlyTracingRequests")
	assert.Equal(t, util.BoolPtr(true), authInfo.OrganizationConfig.CanUseRbac,
		"Should set env var value for CanUseRbac")
}

func TestInitializeAndUnmarshalConfig_BooleanOverrides(t *testing.T) {
	os.Setenv("DEFAULT_ORG_FEATURE_CAN_USE_RBAC", "true")
	os.Setenv("DEFAULT_ORG_FEATURE_CAN_USE_SAML_SSO", "true")
	defer func() {
		os.Unsetenv("DEFAULT_ORG_FEATURE_CAN_USE_RBAC")
		os.Unsetenv("DEFAULT_ORG_FEATURE_CAN_USE_SAML_SSO")
	}()

	// Set up auth info with false boolean values
	authInfo := &auth.AuthInfo{
		OrganizationConfig: &auth.OrganizationConfig{
			CanUseRbac:    util.BoolPtr(false), // Explicitly set to false
			CanUseSamlSso: nil,                 // Not set, should pick up env var
		},
	}

	auth.InitializeAndUnmarshalConfig(authInfo)

	// Verify that explicit false is not overridden
	assert.Equal(t, util.BoolPtr(false), authInfo.OrganizationConfig.CanUseRbac,
		"Explicit false should not be overridden by env var true")

	// Verify that nil is set from env var
	assert.Equal(t, util.BoolPtr(true), authInfo.OrganizationConfig.CanUseSamlSso,
		"Nil boolean should be set from env var")
}
