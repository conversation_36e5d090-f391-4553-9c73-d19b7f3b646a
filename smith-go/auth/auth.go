package auth

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"slices"
	"strings"

	"github.com/go-chi/render"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"
)

type ctxKeyAuth int
type ctxKeyOrgAuth int
type ctxKeyXServiceToken int

const AuthCtxKey ctxKeyAuth = 0
const orgAuthCtxKey ctxKeyOrgAuth = 1
const xServiceTokenKey ctxKeyXServiceToken = 2

func GetAuthInfo(r *http.Request) *AuthInfo {
	return r.Context().Value(AuthCtxKey).(*AuthInfo)
}

func MaybeGetAuthInfo(r *http.Request) (*AuthInfo, bool) {
	auth, ok := r.Context().Value(AuthCtxKey).(*AuthInfo)
	return auth, ok
}

func MaybeGetOrgAuthInfo(r *http.Request) (*OrgAuthInfo, bool) {
	auth, ok := r.Context().Value(orgAuthCtxKey).(*OrgAuthInfo)
	return auth, ok
}

func SetAuthCtxFields(ctx context.Context, auth *AuthInfo) context.Context {
	if auth != nil {
		ctx = context.WithValue(ctx, config.UserIDCtxKey, auth.UserID)
		ctx = context.WithValue(ctx, config.LsUserIDCtxKey, auth.LSUserID)
		ctx = context.WithValue(ctx, config.TenantIDCtxKey, auth.TenantID)
		ctx = context.WithValue(ctx, config.OrgIDCtxKey, auth.OrganizationID)
	}
	return ctx
}

func SetPublicAuthCtxFields(ctx context.Context, auth *PublicAuthInfo) context.Context {
	if auth != nil {
		ctx = context.WithValue(ctx, config.UserIDCtxKey, auth.UserID)
		ctx = context.WithValue(ctx, config.LsUserIDCtxKey, auth.LSUserID)
		ctx = context.WithValue(ctx, config.TenantIDCtxKey, auth.TenantID)
		ctx = context.WithValue(ctx, config.OrgIDCtxKey, auth.OrganizationID)
	}
	return ctx
}

func SetOrgAuthCtxFields(ctx context.Context, auth *OrgAuthInfo) context.Context {
	if auth != nil {
		ctx = context.WithValue(ctx, config.UserIDCtxKey, auth.UserID)
		ctx = context.WithValue(ctx, config.LsUserIDCtxKey, auth.LSUserID)
		ctx = context.WithValue(ctx, config.OrgIDCtxKey, auth.OrganizationID)
	}
	return ctx
}

func SetAllTenantsAuthCtxFields(ctx context.Context, auth *AllTenantsAuthInfo) context.Context {
	if auth != nil {
		ctx = context.WithValue(ctx, config.UserIDCtxKey, auth.UserID)
		ctx = context.WithValue(ctx, config.LsUserIDCtxKey, auth.LSUserID)
	}
	return ctx
}

func GetPublicAuthInfo(r *http.Request) *PublicAuthInfo {
	auth := GetAuthInfo(r)
	return &PublicAuthInfo{
		OrganizationID: auth.OrganizationID,
		UserID:         auth.UserID,
		LSUserID:       auth.LSUserID,
		UserEmail:      auth.UserEmail,
		TenantID:       auth.TenantID,
	}
}

func GetOrgAuthInfo(r *http.Request) *OrgAuthInfo {
	return r.Context().Value(orgAuthCtxKey).(*OrgAuthInfo)
}

type TenantConfig struct {
	// MaxIdentities is the maximum number of identities allowed in this tenant.
	MaxIdentities *int `json:"max_identities" env:"DEFAULT_FEATURE_MAX_IDENTITIES"`

	// MaxHourlyTracingRequests is the maximum number of tracing requests allowed per hour.
	// 2 reqs per run (POST + PATCH) + buffer for retries means this supports 50k runs per hour.
	MaxHourlyTracingRequests int `json:"max_hourly_tracing_requests" env:"DEFAULT_FEATURE_MAX_HOURLY_TRACING_REQUESTS,default=105000"`

	// MaxHourlyTracingBytes is the maximum total size (in bytes) of tracing payloads per hour.
	MaxHourlyTracingBytes uint64 `json:"max_hourly_tracing_bytes" env:"DEFAULT_FEATURE_MAX_HOURLY_TRACING_BYTES,default=2000000000"`

	// MaxMonthlyTotalUniqueTraces is the maximum total number of root runs per month.
	// This is the metric used for billing.
	MaxMonthlyTotalUniqueTraces uint64 `json:"max_monthly_total_unique_traces" env:"DEFAULT_FEATURE_MAX_MONTHLY_TOTAL_UNIQUE_TRACES,default=10000000"`

	// MaxEventsIngestedPerMinute is the maximum number of ingestion events allowed per minute.
	MaxEventsIngestedPerMinute int `json:"max_events_ingested_per_minute" env:"DEFAULT_FEATURE_MAX_EVENTS_INGESTED_PER_MINUTE,default=20000"`

	// MaxRunRules is the maximum number of run rules allowed for this tenant.
	MaxRunRules int `json:"max_run_rules" env:"DEFAULT_FEATURE_MAX_RUN_RULES,default=100"`

	// CanUseRbac indicates whether this tenant can create new users using roles based on their plan.
	CanUseRbac *bool `json:"can_use_rbac,omitempty" env:"DEFAULT_FEATURE_CAN_USE_RBAC"`

	// CanAddSeats indicates whether this tenant can invite new users based on their plan.
	CanAddSeats *bool `json:"can_add_seats,omitempty" env:"DEFAULT_FEATURE_CAN_ADD_SEATS"`

	// StartupPlanApprovalDate is the date when the tenant was approved for the startup plan in YYYY-MM-DD format.
	StartupPlanApprovalDate *string `json:"startup_plan_approval_date" env:"DEFAULT_FEATURE_STARTUP_PLAN_APPROVAL_DATE"`

	// PremierPlanApprovalDate is the date when the tenant was approved for the premier plan in YYYY-MM-DD format.
	PremierPlanApprovalDate *string `json:"premier_plan_approval_date" env:"DEFAULT_FEATURE_PREMIER_PLAN_APPROVAL_DATE"`

	// OrganizationConfig is the corresponding organization's config, for accessing organization-specific settings.
	OrganizationConfig *OrganizationConfig `json:"organization_config"`

	// AdditionalFields are additional fields that are not part of the public API.
	AdditionalFields map[string]*json.RawMessage `json:"-"`
}

type OrganizationConfig struct {
	// MaxIdentities is the maximum number of identities allowed in this org.
	MaxIdentities int `json:"max_identities" env:"DEFAULT_ORG_FEATURE_MAX_IDENTITIES,default=5"`

	// MaxWorkspaces is the maximum number of workspaces allowed in this org. -1 means no limit.
	MaxWorkspaces int `json:"max_workspaces" env:"DEFAULT_ORG_FEATURE_MAX_WORKSPACES,default=1"`

	// CanUseRbac indicates whether this org can create new users using roles based on their plan.
	CanUseRbac *bool `json:"can_use_rbac,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_USE_RBAC,default=false"`

	// CanAddSeats indicates whether this org can invite new users based on their plan.
	CanAddSeats *bool `json:"can_add_seats,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_ADD_SEATS,default=true"`

	// StartupPlanApprovalDate is the date when the org was approved for the startup plan in YYYY-MM-DD format.
	StartupPlanApprovalDate *string `json:"startup_plan_approval_date" env:"DEFAULT_ORG_FEATURE_STARTUP_PLAN_APPROVAL_DATE"`

	// PremierPlanApprovalDate is the date when the org was approved for the premier plan in YYYY-MM-DD format.
	PremierPlanApprovalDate *string `json:"premier_plan_approval_date" env:"DEFAULT_ORG_FEATURE_PREMIER_PLAN_APPROVAL_DATE"`

	// CanDisablePublicSharing indicates whether this org can disable public sharing of resources like traces, datasets, and prompts.
	CanDisablePublicSharing *bool `json:"can_disable_public_sharing,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_DISABLE_PUBLIC_SHARING,default=false"`

	// CanServeDatasets indicates whether datasets can be served for few shot prompting.
	CanServeDatasets *bool `json:"can_serve_datasets,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_SERVE_DATASETS,default=false"`

	// CanUseLanggraphCloud indicates whether this org can use LangGraph Platform.
	CanUseLanggraphCloud *bool `json:"can_use_langgraph_cloud,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_USE_LANGGRAPH_CLOUD,default=false"`

	// MaxLanggraphCloudDeployments is the maximum number of LangGraph Platform deployments allowed for this org.
	MaxLanggraphCloudDeployments int `json:"max_langgraph_cloud_deployments" env:"DEFAULT_ORG_FEATURE_MAX_LANGGRAPH_CLOUD_DEPLOYMENTS,default=3"`

	// MaxFreeLanggraphCloudDeployments is the maximum number of free LangGraph Platform deployments allowed for this org.
	MaxFreeLanggraphCloudDeployments int `json:"max_free_langgraph_cloud_deployments" env:"DEFAULT_ORG_FEATURE_MAX_FREE_LANGGRAPH_CLOUD_DEPLOYMENTS,default=0"`

	// CanUseSamlSso indicates whether this org can configure SAML SSO.
	CanUseSamlSso *bool `json:"can_use_saml_sso,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_USE_SAML_SSO,default=false"`

	// CanUseBulkExport indicates whether this org can create bulk exports.
	CanUseBulkExport *bool `json:"can_use_bulk_export,omitempty" env:"DEFAULT_ORG_FEATURE_CAN_USE_BULK_EXPORT,default=false"`

	// DemoLgpNewGraphEnabled indicates whether this org can use the demo page for creating new graphs.
	DemoLgpNewGraphEnabled *bool `json:"demo_lgp_new_graph_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_DEMO_LGP_NEW_GRAPH_ENABLED,default=false"`

	// ShowUpdatedSidenav indicates whether to show updated side nav to users in this org.
	ShowUpdatedSidenav *bool `json:"show_updated_sidenav,omitempty" env:"DEFAULT_ORG_FEATURE_SHOW_UPDATED_SIDENAV,default=false"`

	// ShowUpdatedResourceTags indicates whether to show updated resource tags to users in this org.
	ShowUpdatedResourceTags *bool `json:"show_updated_resource_tags,omitempty" env:"DEFAULT_ORG_FEATURE_SHOW_UPDATED_RESOURCE_TAGS,default=false"`

	// KvDatasetMessageSupport indicates whether to use the new messages experience for KV datasets.
	KvDatasetMessageSupport *bool `json:"kv_dataset_message_support,omitempty" env:"DEFAULT_ORG_FEATURE_KV_DATASET_MESSAGE_SUPPORT,default=true"`

	// ShowPlaygroundPromptCanvas indicates whether to show the playground prompt canvas.
	ShowPlaygroundPromptCanvas *bool `json:"show_playground_prompt_canvas,omitempty" env:"DEFAULT_ORG_FEATURE_SHOW_PLAYGROUND_PROMPT_CANVAS,default=false"`

	// AllowCustomIframes indicates whether to allow custom iframes for trace rendering.
	AllowCustomIframes *bool `json:"allow_custom_iframes,omitempty" env:"DEFAULT_ORG_FEATURE_ALLOW_CUSTOM_IFRAMES,default=false"`

	// DatadogRumSessionSampleRate indicates the sampling rate for datadog RUM sessions.
	DatadogRumSessionSampleRate int `json:"datadog_rum_session_sample_rate" env:"DEFAULT_ORG_FEATURE_DATADOG_RUM_SESSION_SAMPLE_RATE,default=20"`

	// EnableLanggraphPricing indicates whether to show Agent marketplace in Langgraph tab.
	EnableLanggraphPricing *bool `json:"enable_langgraph_pricing,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_LANGGRAPH_PRICING,default=false"`

	// EnableThreadViewPlayground indicates whether to allow opening top-level thread view runs in the playground.
	EnableThreadViewPlayground *bool `json:"enable_thread_view_playground,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_THREAD_VIEW_PLAYGROUND,default=false"`

	// EnableOrgUsageCharts indicates whether to show organization usage charts backed by ClickHouse queries instead of Metronome.
	EnableOrgUsageCharts *bool `json:"enable_org_usage_charts,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_ORG_USAGE_CHARTS,default=false"`

	// EnableSelectAllTraces indicates whether to enable the "select all traces" button in the runs table.
	EnableSelectAllTraces *bool `json:"enable_select_all_traces,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_SELECT_ALL_TRACES,default=false"`

	// LangGraphDeployOwnCloudEnabled indicates whether the org can deploy LangGraph cloud to their own cloud.
	LangGraphDeployOwnCloudEnabled *bool `json:"langgraph_deploy_own_cloud_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_LANGGRAPH_DEPLOY_OWN_CLOUD_ENABLED,default=false"`

	// EnableK8sVanillaPlatform indicates whether to enable vanilla k8s platform for this org.
	EnableK8sVanillaPlatform *bool `json:"enable_k8s_vanilla_platform,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_VANILLA_K8S_PLATFORM,default=false"`

	// LangGraphRemoteReconcilerEnabled indicates whether an org's LangGraph deployments are reconciled via a remote reconciler instance.
	LangGraphRemoteReconcilerEnabled *bool `json:"langgraph_remote_reconciler_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_LANGGRAPH_REMOTE_RECONCILER_ENABLED,default=false"`

	// LgpTemplatesEnabled indicates whether to enable LGP templates for this org.
	LgpTemplatesEnabled *bool `json:"lgp_templates_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_LGP_TEMPLATES_ENABLED,default=false"`

	// EnablePrebuiltDashboards indicates whether to enable the new prebuilt dashboards UI for this org.
	EnablePrebuiltDashboards *bool `json:"enable_prebuilt_dashboards,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_PREBUILT_DASHBOARDS,default=false"`

	// LangsmithAlertsPocEnabled indicates whether to enable the legacy alerts POC for this org.
	LangsmithAlertsPocEnabled *bool `json:"langsmith_alerts_poc_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_LANGCHAIN_ALERTS_POC_ENABLED,default=true"`

	// ExperimentalSearchEnabled indicates whether experimental search (Quickwit-powered) is enabled for this org.
	ExperimentalSearchEnabled *bool `json:"langsmith_experimental_search_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_EXPERIMENTAL_SEARCH_ENABLED,default=false"`

	// LangGraphPlatformGAEnabled indicates whether to enable the LangGraph Platform GA for this org.
	LangGraphPlatformGAEnabled *bool `json:"langgraph_platform_ga_enabled,omitempty" env:"DEFAULT_ORG_FEATURE_LANGGRAPH_PLATFORM_GA_ENABLED,default=false"`

	// EnableAlignEvaluators indicates whether to enable the align evaluators flow for this org.
	EnableAlignEvaluators *bool `json:"enable_align_evaluators,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_ALIGN_EVALUATORS,default=false"`

	// PlaygroundEvaluatorStrategy indicates the method of running evaluators in the playground
	// options are "cron", "background", or "sync"
	PlaygroundEvaluatorStrategy *string `json:"playground_evaluator_strategy,omitempty" env:"DEFAULT_ORG_FEATURE_PLAYGROUND_EVALUATOR_STRATEGY,default=sync"`

	// EnableNewFilterBar indicates whether to enable the new filter bar for this org.
	EnableNewFilterBar *bool `json:"enable_new_filter_bar,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_NEW_FILTER_BAR,default=false"`
  
	// EnableMonthlyUsageCharts indicates whether to show monthly organization usage charts backed by Metronome for self hosted customers
	EnableMonthlyUsageCharts *bool `json:"enable_monthly_usage_charts,omitempty" env:"DEFAULT_ORG_FEATURE_ENABLE_MONTHLY_USAGE_CHARTS,default=false"`

	// AdditionalFields are additional fields that are not part of the public API.
	AdditionalFields map[string]*json.RawMessage `json:"-"`
}

type OrgAuthInfo struct {
	// org attrs
	OrganizationID                  string              `json:"organization_id"`
	OrganizationIsPersonal          bool                `json:"organization_is_personal"`
	OrganizationMetronomeCustomerId string              `json:"organization_metronome_customer_id,omitempty"`
	OrganizationStripeCustomerId    string              `json:"organization_stripe_customer_id,omitempty"`
	IdentityID                      sql.NullString      `json:"identity_id,omitempty"`
	IdentityReadOnly                bool                `json:"identity_read_only"`
	OrganizationPermissions         []string            `json:"organization_permissions"`
	OrganizationConfig              *OrganizationConfig `json:"organization_config"`
	OrganizationDisabled            bool                `json:"organization_disabled"`
	PublicSharingDisabled           bool                `json:"public_sharing_disabled"`
	SsoOnly                         bool                `json:"sso_only"`

	// user attrs
	UserID       string `json:"user_id,omitempty"`
	LSUserID     string `json:"ls_user_id,omitempty"`
	UserEmail    string `json:"user_email,omitempty"`
	UserFullName string `json:"user_full_name,omitempty"`
	IsSsoUser    bool   `json:"is_sso_user"`

	// API Key attrs
	DefaultTenantID string `json:"default_tenant_id,omitempty"`

	// service attrs
	ServiceIdentity string `json:"service_identity,omitempty"`
}

type TenantAuthInfo struct {
	// tenant attrs
	TenantID        string        `json:"tenant_id"`
	TenantHandle    string        `json:"tenant_handle,omitempty"`
	TenantIsDeleted bool          `json:"tenant_is_deleted"`
	TenantConfig    *TenantConfig `json:"tenant_config"`

	// identity attrs - IdentityPermissions are workspace-level
	IdentityID          string   `json:"identity_id,omitempty"`
	IdentityReadOnly    bool     `json:"identity_read_only"`
	IdentityPermissions []string `json:"identity_permissions"`
}

// Can't use embedded struct because this is serialized externally as JSON
type AuthInfo struct {
	// org attrs
	OrganizationID                  string              `json:"organization_id"`
	OrganizationIsPersonal          bool                `json:"organization_is_personal"`
	OrganizationMetronomeCustomerId string              `json:"organization_metronome_customer_id,omitempty"`
	OrganizationStripeCustomerId    string              `json:"organization_stripe_customer_id,omitempty"`
	OrganizationIdentityID          sql.NullString      `json:"organization_identity_id,omitempty"`
	OrganizationIdentityReadOnly    bool                `json:"organization_identity_read_only,omitempty"`
	OrganizationPermissions         []string            `json:"organization_permissions"`
	OrganizationConfig              *OrganizationConfig `json:"organization_config"`
	OrganizationDisabled            bool                `json:"organization_disabled"`
	PublicSharingDisabled           bool                `json:"public_sharing_disabled"`
	SsoOnly                         bool                `json:"sso_only"`

	// identity attrs
	TenantIdentityID       string `json:"identity_id,omitempty"`
	TenantIdentityReadOnly bool   `json:"identity_read_only"`

	// user attrs
	UserID       string `json:"user_id,omitempty"`
	LSUserID     string `json:"ls_user_id,omitempty"`
	UserEmail    string `json:"user_email,omitempty"`
	UserFullName string `json:"user_full_name,omitempty"`
	IsSsoUser    bool   `json:"is_sso_user"`

	// tenant attrs
	TenantID        string        `json:"tenant_id"`
	TenantHandle    string        `json:"tenant_handle,omitempty"`
	TenantIsDeleted bool          `json:"tenant_is_deleted"`
	TenantConfig    *TenantConfig `json:"tenant_config"`

	// IdentityPermissions are workspace-level
	IdentityPermissions []string `json:"identity_permissions"`

	// service attrs
	ServiceIdentity string `json:"service_identity,omitempty"`
}

type PublicAuthInfo struct {
	// auth attrs that are exposed through a public endpoint
	OrganizationID string `json:"organization_id"`
	UserID         string `json:"user_id,omitempty"`
	LSUserID       string `json:"ls_user_id,omitempty"`
	UserEmail      string `json:"user_email,omitempty"`
	TenantID       string `json:"tenant_id"`
}

type AccessTokenInfo struct {
	AccessToken string `json:"access_token"`
}

// For passing hashed password internally only
type AuthInfoWithHashedPassword struct {
	AuthInfo
	UserHashedPassword string `json:"user_hashed_password,omitempty"`
}

type UserInfoWithHashedPassword struct {
	UserID             string `json:"user_id"`
	LSUserID           string `json:"ls_user_id"`
	UserEmail          string `json:"user_email"`
	UserFullName       string `json:"user_full_name,omitempty"`
	UserHashedPassword string `json:"user_hashed_password,omitempty"`
}

type LinkableUserInfo struct {
	Email          string
	FullName       string
	Provider       string
	ProviderUserId string
	SamlProviderId interface{}
}

type ProviderUserInfo struct {
	ID             string         `json:"id"`
	Provider       sql.NullString `json:"provider,omitempty"`
	LSUserID       string         `json:"ls_user_id"`
	SAMLProviderID sql.NullString `json:"saml_provider_id,omitempty"`
	ProviderUserID sql.NullString `json:"provider_user_id,omitempty"`
	Email          sql.NullString `json:"email,omitempty"`
	FullName       sql.NullString `json:"full_name,omitempty"`
	HashedPassword sql.NullString `json:"hashed_password,omitempty"`
}

type SamlProviderInfoSlim struct {
	ID         string `json:"id"`
	ProviderId string `json:"provider_id,omitempty"`
	SsoOnly    bool   `json:"sso_only"`
}

type AllTenantsAuthInfo struct {
	// user attrs
	UserID       string `json:"user_id,omitempty"`
	LSUserID     string `json:"ls_user_id,omitempty"`
	UserEmail    string `json:"user_email,omitempty"`
	UserFullName string `json:"user_full_name,omitempty"`
	IsSsoUser    bool   `json:"is_sso_user"`

	// tenant attrs
	TenantIDs       []string `json:"tenant_ids"`
	OrganizationIds []string `json:"organization_ids"`
}

func logFieldsFromUserInfo(ctx context.Context, userInfo LinkableUserInfo) context.Context {
	ctx = config.LogAndContextSetField(ctx, "user_id", slog.StringValue(userInfo.ProviderUserId))
	ctx = config.LogAndContextSetField(ctx, "email", slog.StringValue(userInfo.Email))
	ctx = config.LogAndContextSetField(ctx, "provider", slog.StringValue(userInfo.Provider))
	if userInfo.SamlProviderId != nil {
		ctx = config.LogAndContextSetField(ctx, "saml_provider_id", slog.StringValue(fmt.Sprintf("%v", userInfo.SamlProviderId)))
	}
	return ctx
}

const getTenantInfoForUserQuery = `
WITH
workspace_permissions AS (
    SELECT ARRAY(
        SELECT permission
        FROM role_permissions rp
        WHERE rp.role_id = i.role_id
        ORDER BY permission
    ) AS permissions,
    i.ls_user_id
    FROM identities i
    WHERE i.ls_user_id = $1
        AND i.access_scope = 'workspace'
        AND i.tenant_id = $2
)
SELECT
    t.id as tenant_id,
    coalesce(t.tenant_handle, '') as tenant_handle,
	t.is_deleted as tenant_is_deleted,
    t.config as tenant_config,
	i.id,
	i.read_only,
    workspace_permissions.permissions
FROM identities i
INNER JOIN tenants t ON i.tenant_id = t.id
LEFT JOIN workspace_permissions ON i.ls_user_id = workspace_permissions.ls_user_id
WHERE i.ls_user_id = $1 AND i.tenant_id = $2 AND i.access_scope = 'workspace';
`

// For now, this retrieves organization info even if there is
// no identity for the user in the organization (but permissions will be empty).
// This can be changed once all users have an organization identity.
const getOrgInfoForNonSSOUserQuery = `
WITH user_upsert as (
	INSERT INTO users (id, email, full_name)
	VALUES ($1, $3, NULLIF($4, ''))
	ON CONFLICT (id) DO UPDATE
	SET email = coalesce($3, users.email),
		full_name = coalesce($4, users.full_name),
		updated_at = NOW()
	WHERE ($3 != '' and users.email != $3)
		OR coalesce(users.full_name, '') != $4
	RETURNING id, ls_user_id
),
existing_provider_user as (
	SELECT ls_user_id
	FROM provider_users
	WHERE email = $3
		AND (provider = $5 OR ($5 IS NULL AND provider IS NULL))
        AND (saml_provider_id = $6 OR ($6 IS NULL AND saml_provider_id IS NULL))
        AND (provider_user_id = $7 OR ($7 IS NULL AND provider_user_id IS NULL))
),
provider_user_upsert as (
	INSERT INTO provider_users (provider, ls_user_id, saml_provider_id, provider_user_id, email, full_name)
	SELECT
		$5 as provider,
		user_upsert.ls_user_id,
		$6 as saml_provider_id,
		$7 as provider_user_id,
		$3 as email,
		NULLIF($4, '') as full_name
	FROM user_upsert
	ON CONFLICT %s DO UPDATE
	SET email = coalesce($3, provider_users.email),
		full_name = coalesce($4, provider_users.full_name),
		updated_at = NOW()
	WHERE ($3 != '' and provider_users.email != $3)
		OR coalesce(provider_users.full_name, '') != $4
	RETURNING id, ls_user_id
),
resolved_user as (
	SELECT coalesce(
		(select ls_user_id from user_upsert),
		(select ls_user_id from provider_user_upsert),
		(select ls_user_id from existing_provider_user)
    ) as ls_user_id
),
org_permissions AS (
    SELECT ARRAY(
        SELECT permission
        FROM role_permissions rp
        WHERE rp.role_id = i.role_id
        ORDER BY permission
    ) AS permissions,
	i.id as identity_id,
	i.read_only
    FROM identities i
    WHERE i.ls_user_id = (select ls_user_id from resolved_user)
      AND i.access_scope = 'organization'
	  AND i.organization_id = $2
)
SELECT
	$2 as organization_id,
	o.is_personal,
	coalesce(o.metronome_customer_id, '') as metronome_customer_id,
	coalesce(o.stripe_customer_id, '') as stripe_customer_id,
	org_permissions.identity_id as organization_identity_id,
	coalesce(org_permissions.read_only, false) as organization_identity_read_only,
	coalesce(org_permissions.permissions, ARRAY[]::text[]) as permissions,
	o.config,
	o.disabled as organization_disabled,
	o.public_sharing_disabled as public_sharing_disabled,
	o.sso_only as sso_only,
	$1 as user_id,
	(select ls_user_id from resolved_user) as ls_user_id,
	$3 as email,
	$4 as full_name,
	false as is_sso_user,
	'' as service_identity,
    '' as default_tenant_id
FROM organizations o
LEFT JOIN org_permissions ON o.id = $2
WHERE o.id = $2
`

// For now, this retrieves organization info even if there is
// no identity for the user in the organization (but permissions will be empty).
// This can be changed once all users have an organization identity.
// This query assumes that the users/provider_users record already exists.
const getOrgInfoForProvisionedUserQuery = `
WITH existing_provider_user as (
	SELECT ls_user_id, provider, email_confirmed_at
	FROM provider_users
	WHERE provider_user_id = $1 AND ls_user_id = $2
	AND (saml_provider_id = $6 OR ($6 IS NULL AND saml_provider_id IS NULL))
),
org_permissions AS (
    SELECT ARRAY(
        SELECT permission
        FROM role_permissions rp
        WHERE rp.role_id = i.role_id
        ORDER BY permission
    ) AS permissions,
	i.id as identity_id,
	i.read_only
    FROM identities i
    WHERE i.ls_user_id = $2
      AND i.access_scope = 'organization'
	  AND i.organization_id = $5
)
SELECT
	$5 as organization_id,
	o.is_personal,
	coalesce(o.metronome_customer_id, '') as metronome_customer_id,
	coalesce(o.stripe_customer_id, '') as stripe_customer_id,
	org_permissions.identity_id as organization_identity_id,
	coalesce(org_permissions.read_only, false) as organization_identity_read_only,
	coalesce(org_permissions.permissions, ARRAY[]::text[]) as permissions,
	o.config,
	o.disabled as organization_disabled,
	o.public_sharing_disabled as public_sharing_disabled,
	o.sso_only as sso_only,
	$1 as user_id,
	$2 as ls_user_id,
	$3 as email,
	$4 as full_name,
	(select provider from existing_provider_user) = 'supabase:sso' as is_sso_user,
	'' as service_identity,
    '' as default_tenant_id
FROM organizations o
LEFT JOIN org_permissions ON o.id = $5
WHERE o.id = $5
`

func getOrgInfoForNonSupabaseUserQueryByType(authType string) string {
	// This is used for non-basic-auth queries only because
	// basic auth doesn't upsert users/provider_users
	if authType == "none" {
		return fmt.Sprintf(getOrgInfoForNonSSOUserQuery, "(ls_user_id) WHERE provider_user_id IS NULL")
	}
	return fmt.Sprintf(getOrgInfoForNonSSOUserQuery, "(ls_user_id, provider_user_id) WHERE provider <> 'supabase:sso'")
}

const ListProvidersForUserQuery = `
SELECT
	id,
	provider,
	ls_user_id,
	saml_provider_id,
	provider_user_id,
	email,
	full_name,
	hashed_password
FROM provider_users WHERE ls_user_id = $1
ORDER BY provider
`

// This query retrieves matching provider_users along with
// any linked provider_users that share the same ls_user_id.
const ListProvidersForEmailOrUserIdQuery = `
SELECT
	id,
	provider,
	ls_user_id,
	saml_provider_id,
	provider_user_id,
	email,
	full_name,
	hashed_password
FROM provider_users
WHERE email = $1
	OR provider_user_id = $2
	OR ls_user_id = (SELECT ls_user_id FROM provider_users WHERE email = $1 OR provider_user_id = $2 LIMIT 1)
ORDER BY provider
`

const ListProvidersForEmailQuery = `
SELECT
	id,
	provider,
	ls_user_id,
	saml_provider_id,
	provider_user_id,
	email,
	full_name,
	hashed_password
FROM provider_users WHERE email = $1
ORDER BY provider
`

const getSamlProviderSlimQuery = `
SELECT
	sp.id,
	sp.provider_id,
	o.sso_only
FROM saml_providers sp
INNER JOIN organizations o ON sp.organization_id = o.id
WHERE organization_id = $1
`

const getSamlProviderByProviderIdSlimQuery = `
SELECT
	sp.id,
	sp.provider_id,
	o.sso_only
FROM saml_providers sp
INNER JOIN organizations o ON sp.organization_id = o.id
WHERE provider_id = $1
`

const ListProvidersQuery = `
SELECT
	id,
	provider,
	ls_user_id,
	saml_provider_id,
	provider_user_id,
	email,
	full_name,
	hashed_password
FROM provider_users
WHERE (provider_user_id = $1 OR ($1 IS NULL AND provider_user_id IS NULL))
	AND email = $2
	AND (provider = $3 OR ($3 IS NULL AND provider IS NULL))
	AND (saml_provider_id = $4 OR ($4 IS NULL AND saml_provider_id IS NULL))
`

const getOrgIdFromTenantIdQuery = `
SELECT organization_id FROM tenants WHERE id = $1
`

// Insert both records, return existing if present
const SafeInsertUserAndProviderUserQuery = `
WITH user_upsert as (
	INSERT INTO users (id, email, full_name)
    VALUES ($1, $2, NULLIF($3, ''))
	ON CONFLICT (email) DO UPDATE
	SET email = EXCLUDED.email
	RETURNING ls_user_id
)
INSERT INTO provider_users (provider, ls_user_id, saml_provider_id, provider_user_id, email, full_name)
SELECT
	$4 as provider,
	(select ls_user_id from user_upsert),
	$5 as saml_provider_id,
	$6 as provider_user_id,
	$2 as email,
	NULLIF($3, '') as full_name
ON CONFLICT (ls_user_id, provider_user_id) WHERE provider <> 'supabase:sso' DO UPDATE
SET email = EXCLUDED.email
RETURNING
	id,
	provider,
	ls_user_id,
	saml_provider_id,
	provider_user_id,
	email,
	full_name,
	hashed_password
`

// this update must succeed even if no rows are updated
// so that a parallel successful update doesn't cause failure
const updateUserInfoQuery = `
WITH user_update as (
	UPDATE users
	SET
		email = CASE
			WHEN $2 != '' AND users.email != $2 THEN $2
			ELSE users.email
		END,
		full_name = CASE
			WHEN coalesce($3, '') != coalesce(users.full_name, '') THEN $3
			ELSE users.full_name
		END,
		updated_at = CASE
			WHEN ($2 != '' AND users.email != $2)
				OR (coalesce($3, '') != coalesce(users.full_name, '')) THEN NOW()
			ELSE users.updated_at
		END
	WHERE ls_user_id = $1
	RETURNING id, ls_user_id
)
UPDATE provider_users
SET
    email = CASE
        WHEN $2 != '' AND provider_users.email != $2 THEN $2
        ELSE provider_users.email
    END,
    full_name = CASE
        WHEN coalesce($3, '') != coalesce(provider_users.full_name, '') THEN $3
        ELSE provider_users.full_name
    END,
    updated_at = CASE
        WHEN ($2 != '' AND provider_users.email != $2)
            OR (coalesce($3, '') != coalesce(provider_users.full_name, '')) THEN NOW()
        ELSE provider_users.updated_at
    END
WHERE provider_user_id = $4
RETURNING
    id,
    provider,
    ls_user_id,
    saml_provider_id,
    provider_user_id,
    email,
    full_name,
    hashed_password
`

// This update must succeed even if no rows are updated
// so that a parallel successful update doesn't cause failure.
// This updates users and all linked provider_users.
const updateLinkedUserInfoQuery = `
WITH user_update as (
	UPDATE users
	SET
		email = CASE
			WHEN $2 != '' AND users.email != $2 THEN $2
			ELSE users.email
		END,
		full_name = CASE
			WHEN coalesce($3, '') != coalesce(users.full_name, '') THEN $3
			ELSE users.full_name
		END,
		updated_at = CASE
			WHEN ($2 != '' AND users.email != $2)
				OR (coalesce($3, '') != coalesce(users.full_name, '')) THEN NOW()
			ELSE users.updated_at
		END
	WHERE ls_user_id = $1
	RETURNING id, ls_user_id
)
UPDATE provider_users
SET
    email = CASE
        WHEN $2 != '' AND provider_users.email != $2 THEN $2
        ELSE provider_users.email
    END,
    full_name = CASE
        WHEN coalesce($3, '') != coalesce(provider_users.full_name, '') THEN $3
        ELSE provider_users.full_name
    END,
    updated_at = CASE
        WHEN ($2 != '' AND provider_users.email != $2)
            OR (coalesce($3, '') != coalesce(provider_users.full_name, '')) THEN NOW()
        ELSE provider_users.updated_at
    END
WHERE ls_user_id = $1
RETURNING
    id,
    provider,
    ls_user_id,
    saml_provider_id,
    provider_user_id,
    email,
    full_name,
    hashed_password
`

const listTenantsAndOrgsForSupabaseUserQuery = `
WITH existing_provider_user as (
	SELECT ls_user_id, provider, email_confirmed_at
	FROM provider_users
	WHERE email = $3
		AND (provider = $5 OR ($5 IS NULL AND provider IS NULL))
        AND (saml_provider_id = $6 OR ($6 IS NULL AND saml_provider_id IS NULL))
        AND (provider_user_id = $1 OR ($1 IS NULL AND provider_user_id IS NULL))
)
SELECT
	$1 as user_id,
	$2::uuid as ls_user_id,
	$3 as user_email,
	$4 as user_full_name,
	$5 = 'supabase:sso' as is_sso_user,
	array(
		SELECT t.id FROM identities i
		INNER JOIN tenants t ON i.tenant_id = t.id
		INNER JOIN organizations o ON t.organization_id = o.id
		WHERE i.ls_user_id = $2::uuid
			AND i.access_scope = 'workspace'
			AND (
				(
					$5 = 'supabase:non-sso'
					AND NOT COALESCE(o.sso_only, false)
				)
				OR (
					$5 = 'supabase:sso'
					AND t.organization_id = (SELECT organization_id FROM saml_providers WHERE provider_id = $6)
					AND (SELECT email_confirmed_at FROM existing_provider_user) IS NOT NULL
				)
			)
		ORDER BY t.id
	) AS tenant_ids,
	array(
		SELECT o.id FROM identities i
		INNER JOIN organizations o ON i.organization_id = o.id
		WHERE i.ls_user_id = $2::uuid
			AND i.access_scope = 'organization'
			AND (
				(
					$5 = 'supabase:non-sso'
					AND NOT COALESCE(o.sso_only, false)
				)
				OR (
					$5 = 'supabase:sso'
					AND o.id = (SELECT organization_id FROM saml_providers WHERE provider_id = $6)
					AND (SELECT email_confirmed_at FROM existing_provider_user) IS NOT NULL
				)
			)
		ORDER BY o.id
	) AS organization_ids;
`

const listTenantsAndOrgsForProvisionedUserQuery = `
WITH existing_provider_user as (
	SELECT ls_user_id, provider
	FROM provider_users
	WHERE email = $3
		AND (provider = $5 OR ($5 IS NULL AND provider IS NULL))
        AND (saml_provider_id = $6 OR ($6 IS NULL AND saml_provider_id IS NULL))
        AND (provider_user_id = $1 OR ($1 IS NULL AND provider_user_id IS NULL))
)
SELECT
	$1 as user_id,
	$2::uuid as ls_user_id,
	$3 as user_email,
	$4 as user_full_name,
	false as is_sso_user,
	array(
		SELECT t.id FROM identities i
		INNER JOIN tenants t ON i.tenant_id = t.id
		INNER JOIN organizations o ON t.organization_id = o.id
		WHERE i.ls_user_id = $2::uuid
			AND i.access_scope = 'workspace'
		ORDER BY t.id
	) AS tenant_ids,
	array(
		SELECT o.id FROM identities i
		INNER JOIN organizations o ON i.organization_id = o.id
		WHERE i.ls_user_id = $2::uuid
			AND i.access_scope = 'organization'
		ORDER BY o.id
	) AS organization_ids;
`

const listTenantsAndOrgsForNonSSOUserQuery = `
WITH user_upsert as (
	INSERT INTO users (id, email, full_name)
    VALUES ($1, $2, NULLIF($3, ''))
	ON CONFLICT (id) DO UPDATE
	SET email = coalesce($2, users.email),
		full_name = coalesce($3, users.full_name),
		updated_at = NOW()
	WHERE ($2 != '' and users.email != $2)
		OR coalesce(users.full_name, '') != $3
	RETURNING id, ls_user_id
),
existing_user as (
	SELECT ls_user_id
	FROM users
	WHERE id = $1
),
existing_provider_user as (
	SELECT ls_user_id
	FROM provider_users
	WHERE email = $2
		AND (provider = $4 OR ($4 IS NULL AND provider IS NULL))
        AND (saml_provider_id = $5 OR ($5 IS NULL AND saml_provider_id IS NULL))
        AND (provider_user_id = $6 OR ($6 IS NULL AND provider_user_id IS NULL))
),
provider_user_upsert as (
	INSERT INTO provider_users (provider, ls_user_id, saml_provider_id, provider_user_id, email, full_name)
	SELECT
		$4 as provider,
		coalesce(
			(SELECT ls_user_id FROM user_upsert),
			(SELECT ls_user_id FROM existing_user)
		),
		$5 as saml_provider_id,
		$6 as provider_user_id,
		$2 as email,
		NULLIF($3, '') as full_name
	ON CONFLICT %s DO UPDATE
	SET email = coalesce($2, provider_users.email),
		full_name = coalesce($3, provider_users.full_name),
		updated_at = NOW()
	WHERE ($2 != '' and provider_users.email != $2)
		OR coalesce(provider_users.full_name, '') != $3
	RETURNING id, ls_user_id
),
resolved_user as (
	SELECT coalesce(
		(select ls_user_id from user_upsert),
		(select ls_user_id from provider_user_upsert),
		(select ls_user_id from existing_provider_user)
    ) as ls_user_id
)
SELECT
	$1 as user_id,
	(select ls_user_id from resolved_user) as ls_user_id,
	$2 as user_email,
	$3 as user_full_name,
	false as is_sso_user,
	array(
		SELECT t.id FROM identities i
		INNER JOIN tenants t ON i.tenant_id = t.id
		WHERE i.user_id = $1 AND i.access_scope = 'workspace'
		ORDER BY t.id
	) as tenant_ids,
	array(
		SELECT o.id FROM identities i
		INNER JOIN organizations o ON i.organization_id = o.id
		WHERE i.user_id = $1 AND i.access_scope = 'organization'
		ORDER BY o.id
	) as organization_ids;
`

func listTenantsAndOrgsForNonSSOUserQueryByType(authType string) string {
	// This is used for non-basic-auth queries only because
	// basic auth doesn't upsert users/provider_users
	if authType == "none" {
		return fmt.Sprintf(listTenantsAndOrgsForNonSSOUserQuery, "(ls_user_id) WHERE provider_user_id IS NULL")
	}
	return fmt.Sprintf(listTenantsAndOrgsForNonSSOUserQuery, "(ls_user_id, provider_user_id) WHERE provider <> 'supabase:sso'")
}

// on conflict clause is to allow parallel inserts to not conflict with each other
const LinkProviderUsersQuery = `
WITH existing_user as (
	SELECT ls_user_id
	FROM users
	WHERE email = $1
)
INSERT INTO provider_users (provider, ls_user_id, saml_provider_id, provider_user_id, email, full_name)
SELECT
	$2 as provider,
	ls_user_id,
	$3 as saml_provider_id,
	$4 as provider_user_id,
	$1 as email,
	NULLIF($5, '') as full_name
FROM existing_user
ON CONFLICT %s DO UPDATE
SET ls_user_id = EXCLUDED.ls_user_id
RETURNING
	id,
	provider,
	ls_user_id,
	saml_provider_id,
	provider_user_id,
	email,
	full_name,
	hashed_password
`

func linkProviderUsersQueryByProvider(provider string) string {
	// This is used for non-basic-auth queries only because
	// basic auth doesn't upsert users/provider_users
	if provider == "supabase:sso" {
		return fmt.Sprintf(LinkProviderUsersQuery, "(ls_user_id, saml_provider_id)")
	}
	return fmt.Sprintf(LinkProviderUsersQuery, "(ls_user_id, provider_user_id) WHERE provider <> 'supabase:sso'")
}

type Handler interface {
	Middleware(http.Handler) http.Handler
	CacheKey(r *http.Request) uint64
	GetTenantlessAuth(w http.ResponseWriter, r *http.Request)
	OrgMiddleware(http.Handler) http.Handler
	XServiceKeyMiddleware(http.Handler) http.Handler
	FetchInternalAuth(w http.ResponseWriter, r *http.Request)
	FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request)
}

func cacheKeyWithHeaders(r *http.Request, headers ...string) uint64 {
	// Read the request payload, and then setup buffer for future reader
	var buf []byte
	if r.Body != nil {
		buf, _ = io.ReadAll(r.Body)
		r.Body = io.NopCloser(bytes.NewBuffer(buf))
	}
	var headerbuf []byte
	for _, header := range headers {
		headerbuf = append(headerbuf, []byte(r.Header.Get(header))...)
	}

	// Prepare cache key based on request URL path and the request data payload.
	return lsredis.BytesToHash(
		[]byte(strings.ToLower(r.URL.Path)),
		[]byte(strings.ToLower(r.URL.RawQuery)),
		headerbuf,
		buf,
	)
}

func FetchAuth(w http.ResponseWriter, r *http.Request) {
	auth := GetAuthInfo(r)
	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func FetchPublicAuth(w http.ResponseWriter, r *http.Request) {
	auth := GetPublicAuthInfo(r)
	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func FetchOrgAuth(w http.ResponseWriter, r *http.Request) {
	auth := GetOrgAuthInfo(r)
	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func RequirePermission(permission Permission) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// fetch the auth info
			authInfo, exists := MaybeGetAuthInfo(r)
			if !exists {
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Failed to fetch auth info"})
				return
			}

			if authInfo.ServiceIdentity != "unspecified" {
				if authInfo.IdentityPermissions == nil || !slices.Contains(authInfo.IdentityPermissions, string(permission)) {
					http.Error(w, fmt.Sprintf("missing permission %s", permission), http.StatusForbidden)
					return
				}
			}
			// Proceed to the next handler with the updated context
			next.ServeHTTP(w, r)
		})
	}
}

func VerifyAuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authInfo, ok := MaybeGetAuthInfo(r)
		if !ok || authInfo == nil {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		if authInfo.OrganizationDisabled {
			http.Error(w, "Organization is disabled", http.StatusForbidden)
			return
		}

		if authInfo.TenantIsDeleted {
			http.Error(w, "Workspace is deleted", http.StatusForbidden)
			return
		}

		if xUserID := r.Header.Get("X-User-Id"); xUserID != "" && authInfo.UserID != xUserID {
			http.Error(w, "Forbidden", http.StatusForbidden)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func AuditLogContextMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authInfo, ok := MaybeGetAuthInfo(r)
		if ok {
			ctx := SetAuthCtxFields(r.Context(), authInfo)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}
		orgAuthInfo, ok := MaybeGetOrgAuthInfo(r)
		if ok {
			ctx := SetOrgAuthCtxFields(r.Context(), orgAuthInfo)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}
		next.ServeHTTP(w, r)
	})
}
