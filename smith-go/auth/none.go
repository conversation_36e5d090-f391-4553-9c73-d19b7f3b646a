package auth

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/redis/go-redis/v9"
	lsredis "langchain.com/smith/redis"

	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
)

const authTypeNone = "none"
const DefaultEmailNone = "<EMAIL>"
const DefaultNameNone = "Default User"

func NewNone(pg *database.AuditLoggedPool, redisClient redis.UniversalClient, cacheTTLSecs int) *AuthHandlerNone {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	cache := lsredis.NewCache[uint64, *AuthInfo](redisClient, "authInfo", ttl)
	orgCache := lsredis.NewCache[uint64, *OrgAuthInfo](redisClient, "orgAuthInfo", ttl)
	return &AuthHandlerNone{
		Pg: pg,

		cache:           cache,
		orgCache:        orgCache,
		XServiceKeyAuth: NewHandlerXServiceKeyAuth(pg, cache, orgCache),
	}
}

type AuthHandlerNone struct {
	Pg *database.AuditLoggedPool

	cache           *lsredis.Cache[uint64, *AuthInfo]
	orgCache        *lsredis.Cache[uint64, *OrgAuthInfo]
	XServiceKeyAuth *HandlerXServiceKeyAuth
}

func (h *AuthHandlerNone) Middleware(next http.Handler) http.Handler {
	orgInfoQuery := getOrgInfoForNonSupabaseUserQueryByType(authTypeNone)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.Middleware(next).ServeHTTP(w, r)
			return
		}
		// verify tenant access
		auth, err := h.cache.GetFresh(
			ctx,
			lsredis.StringToHash(getTenantInfoForUserQuery, orgInfoQuery, r.Header.Get("Authorization"), config.Env.SingletonTenantId),
			func() (*AuthInfo, error) {
				// validate access by retrieving org info then tenant info in one transaction
				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
				if err != nil {
					return nil, err
				}

				defer func() {
					if err != nil {
						tx.Rollback(r.Context())
						return
					}
					err = tx.Commit(r.Context()) // commit, set return error here
				}()

				// retrieve org ID from tenant ID if not provided
				organizationId := r.Header.Get("X-Organization-Id")
				var resolvedOrgId string
				if organizationId == "" {
					orgIdRow, err := tx.Query(
						ctx,
						getOrgIdFromTenantIdQuery,
						config.Env.SingletonTenantId,
					)
					if err != nil {
						if !errors.Is(err, pgx.ErrNoRows) {
							log.Println("pgx error collecting org ID: ", err)
						}
						return nil, err
					}
					if orgId, err := pgx.CollectExactlyOneRow(orgIdRow, pgx.RowTo[string]); err == nil {
						resolvedOrgId = orgId
					} else {
						log.Printf("No organization with tenant ID %s found", config.Env.SingletonTenantId)
						return nil, err // unknown error (unable to find org), not cached
					}
				} else {
					resolvedOrgId = organizationId
				}

				// org info
				orgRows, _ := tx.Query(
					ctx,
					orgInfoQuery,
					config.Env.SingletonTenantId,
					resolvedOrgId,
					DefaultEmailNone,
					DefaultNameNone,
					nil,
					nil,
					nil,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No user with user ID %s found. organization: %s", config.Env.SingletonTenantId, resolvedOrgId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Println("pgx error collecting org auth info in workspace middleware: ", err)
					return nil, err // unknown error, not cached
				}

				providerUserRows, _ := tx.Query(
					ctx,
					ListProvidersQuery,
					nil,
					DefaultEmailNone,
					nil,
					nil,
				)
				providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No login method for email %s, org ID %s and workspace ID %s found", DefaultEmailNone, resolvedOrgId, config.Env.SingletonTenantId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Printf("pgx error collecting provider user info in workspace middleware for email %s, org ID %s and workspace ID %s: %v", DefaultEmailNone, resolvedOrgId, config.Env.SingletonTenantId, err)
					return nil, err // unknown error, not cached
				}

				// tenant info
				rows, _ := tx.Query(
					ctx,
					getTenantInfoForUserQuery,
					providerInfo.LSUserID,
					config.Env.SingletonTenantId,
				)
				tenantAuth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[TenantAuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No identity found for user %s on tenant %s", config.Env.SingletonTenantId, config.Env.SingletonTenantId)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Println("pgx error collecting tenant auth info: ", err)
					return nil, err // unknown error, not cached
				}

				auth := &AuthInfo{
					OrganizationID:                  orgAuth.OrganizationID,
					OrganizationIsPersonal:          orgAuth.OrganizationIsPersonal,
					OrganizationMetronomeCustomerId: orgAuth.OrganizationMetronomeCustomerId,
					OrganizationStripeCustomerId:    orgAuth.OrganizationStripeCustomerId,
					OrganizationIdentityID:          orgAuth.IdentityID,
					OrganizationIdentityReadOnly:    orgAuth.IdentityReadOnly,
					OrganizationPermissions:         orgAuth.OrganizationPermissions,
					OrganizationConfig:              orgAuth.OrganizationConfig,
					PublicSharingDisabled:           orgAuth.PublicSharingDisabled,
					SsoOnly:                         orgAuth.SsoOnly,
					UserID:                          orgAuth.UserID,
					LSUserID:                        orgAuth.LSUserID,
					UserEmail:                       orgAuth.UserEmail,
					UserFullName:                    orgAuth.UserFullName,
					TenantID:                        tenantAuth.TenantID,
					TenantHandle:                    tenantAuth.TenantHandle,
					TenantIsDeleted:                 tenantAuth.TenantIsDeleted,
					TenantConfig:                    tenantAuth.TenantConfig,
					TenantIdentityID:                tenantAuth.IdentityID,
					TenantIdentityReadOnly:          tenantAuth.IdentityReadOnly,
					IdentityPermissions:             tenantAuth.IdentityPermissions,
					ServiceIdentity:                 "",
				}

				InitializeAndUnmarshalConfig(auth)

				return auth, nil
			},
		)
		if err != nil {
			fmt.Printf("error: %v\n", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *AuthHandlerNone) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r)
}

func (h *AuthHandlerNone) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	rows, _ := h.Pg.Query(
		ctx,
		listTenantsAndOrgsForNonSSOUserQueryByType(authTypeNone),
		config.Env.SingletonTenantId,
		DefaultEmailNone,
		DefaultNameNone,
		nil,
		nil,
		nil,
	)
	auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AllTenantsAuthInfo])
	if err != nil {
		fmt.Printf("error: %v\n", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": fmt.Sprintf("error: %v\n", err)})
		return
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func (h *AuthHandlerNone) OrgMiddleware(next http.Handler) http.Handler {
	orgInfoQuery := getOrgInfoForNonSupabaseUserQueryByType(authTypeNone)
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}
		// verify organization access
		auth, err := h.orgCache.GetFresh(
			ctx,
			lsredis.StringToHash(orgInfoQuery, r.Header.Get("Authorization"), config.Env.SingletonTenantId),
			func() (*OrgAuthInfo, error) {
				// validate access by retrieving org info then tenant info in one transaction
				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.RepeatableRead})
				if err != nil {
					return nil, err
				}

				defer func() {
					if err != nil {
						tx.Rollback(r.Context())
						return
					}
					err = tx.Commit(r.Context()) // commit, set return error here
				}()
				resolvedOrgId := ""
				// Get organization ID from tenant
				orgIdRow, err := tx.Query(
					ctx,
					getOrgIdFromTenantIdQuery,
					config.Env.SingletonTenantId,
				)
				if err != nil {
					if !errors.Is(err, pgx.ErrNoRows) {
						log.Println("pgx error collecting org ID: ", err)
					}
					return nil, err
				}
				if orgId, err := pgx.CollectExactlyOneRow(orgIdRow, pgx.RowTo[string]); err == nil {
					resolvedOrgId = orgId
				} else {
					log.Printf("No organization with tenant ID %s found", config.Env.SingletonTenantId)
					resolvedOrgId = ""
				}
				// validate access
				orgRows, _ := h.Pg.Query(
					ctx,
					orgInfoQuery,
					config.Env.SingletonTenantId,
					resolvedOrgId,
					DefaultEmailNone,
					DefaultNameNone,
					nil,
					nil,
					nil,
				)
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err != nil {
					if !errors.Is(err, pgx.ErrNoRows) {
						log.Println("pgx error collecting org auth info in org middleware: ", err)
					}
					return nil, err
				}

				InitializeAndUnmarshalOrgConfig(orgAuth)

				return orgAuth, nil
			},
		)
		if err != nil {
			fmt.Printf("error: %v\n", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *AuthHandlerNone) XServiceKeyMiddleware(next http.Handler) http.Handler {
	return h.XServiceKeyAuth.XServiceKeyMiddleware(next)
}

func (h *AuthHandlerNone) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalAuth(w, r)
}

func (h *AuthHandlerNone) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalOrgAuth(w, r)
}
