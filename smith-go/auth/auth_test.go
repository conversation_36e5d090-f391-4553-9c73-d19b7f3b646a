package auth_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"

	"github.com/justinas/alice"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
)

func TestRequirePermission(t *testing.T) {
	// Define the permission we will test
	perm := auth.DatasetsRead

	// Define a handler to be wrapped by the middleware
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	// Create the middleware
	middleware := auth.RequirePermission(perm)

	// Wrap the test handler with the middleware
	handlerToTest := middleware(testHandler)

	// Define test cases
	tests := []struct {
		name           string
		authInfo       *auth.AuthInfo
		expectedStatus int
		expectedBody   interface{}
	}{
		{
			name:           "No AuthInfo",
			authInfo:       nil,
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   map[string]string{"error": "Failed to fetch auth info"},
		},
		{
			name: "AuthInfo without permissions",
			authInfo: &auth.AuthInfo{
				IdentityPermissions: nil,
			},
			expectedStatus: http.StatusForbidden,
			expectedBody:   fmt.Sprintf("missing permission %s\n", perm),
		},
		{
			name: "AuthInfo without required permission",
			authInfo: &auth.AuthInfo{
				IdentityPermissions: []string{"some-other-permission"},
			},
			expectedStatus: http.StatusForbidden,
			expectedBody:   fmt.Sprintf("missing permission %s\n", perm),
		},
		{
			name: "AuthInfo with required permission",
			authInfo: &auth.AuthInfo{
				IdentityPermissions: []string{string(perm)},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "OK",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a request
			req := httptest.NewRequest("GET", "/", nil)

			// If authInfo is provided, set it in the context
			if tt.authInfo != nil {
				ctx := context.WithValue(req.Context(), auth.AuthCtxKey, tt.authInfo)
				req = req.WithContext(ctx)
			}

			// Create a ResponseRecorder
			rr := httptest.NewRecorder()

			// Call the handler
			handlerToTest.ServeHTTP(rr, req)

			// Check the status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// Check the response body
			if tt.expectedStatus == http.StatusUnauthorized {
				var actualBody map[string]string
				err := json.Unmarshal(rr.Body.Bytes(), &actualBody)
				if err != nil {
					t.Errorf("failed to unmarshal response body: %v", err)
				}
				expectedBody := tt.expectedBody.(map[string]string)
				if !reflect.DeepEqual(actualBody, expectedBody) {
					t.Errorf("expected body %v, got %v", expectedBody, actualBody)
				}
			} else {
				body := strings.TrimSpace(rr.Body.String())
				expectedBody := strings.TrimSpace(tt.expectedBody.(string))
				if body != expectedBody {
					t.Errorf("expected body %q, got %q", expectedBody, body)
				}
			}
		})
	}
}

func TestVerifyAuthMiddleware(t *testing.T) {
	protectedHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("OK"))
	})

	t.Run("no auth info => 401 Unauthorized", func(t *testing.T) {
		rr := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/", nil)

		chain := alice.New(auth.VerifyAuthMiddleware).Then(protectedHandler)
		chain.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusUnauthorized, rr.Code)
		assert.Contains(t, rr.Body.String(), "Unauthorized")
	})

	t.Run("organization disabled => 403 Forbidden", func(t *testing.T) {
		rr := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/", nil)

		authInfo := &auth.AuthInfo{
			OrganizationDisabled: true,
		}
		ctx := context.WithValue(req.Context(), auth.AuthCtxKey, authInfo)
		req = req.WithContext(ctx)

		chain := alice.New(auth.VerifyAuthMiddleware).Then(protectedHandler)
		chain.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusForbidden, rr.Code)
		assert.Contains(t, rr.Body.String(), "Organization is disabled")
	})

	t.Run("workspace (tenant) deleted => 403 Forbidden", func(t *testing.T) {
		rr := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/", nil)

		authInfo := &auth.AuthInfo{
			TenantIsDeleted: true,
		}
		ctx := context.WithValue(req.Context(), auth.AuthCtxKey, authInfo)
		req = req.WithContext(ctx)

		chain := alice.New(auth.VerifyAuthMiddleware).Then(protectedHandler)
		chain.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusForbidden, rr.Code)
		assert.Contains(t, rr.Body.String(), "Workspace is deleted")
	})

	t.Run("X-User-Id mismatch => 403 Forbidden", func(t *testing.T) {
		rr := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/", nil)

		req.Header.Set("X-User-Id", "someone-else")

		authInfo := &auth.AuthInfo{
			UserID: "correct-user-id",
		}
		ctx := context.WithValue(req.Context(), auth.AuthCtxKey, authInfo)
		req = req.WithContext(ctx)

		chain := alice.New(auth.VerifyAuthMiddleware).Then(protectedHandler)
		chain.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusForbidden, rr.Code)
		assert.Contains(t, rr.Body.String(), "Forbidden")
	})

	t.Run("X-User-Id match => 200 OK", func(t *testing.T) {
		rr := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/", nil)

		req.Header.Set("X-User-Id", "my-user-id")
		authInfo := &auth.AuthInfo{
			UserID: "my-user-id",
		}
		ctx := context.WithValue(req.Context(), auth.AuthCtxKey, authInfo)
		req = req.WithContext(ctx)

		chain := alice.New(auth.VerifyAuthMiddleware).Then(protectedHandler)
		chain.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
		assert.Equal(t, "OK", rr.Body.String())
	})

	t.Run("no X-User-Id header => 200 OK (skip user check)", func(t *testing.T) {
		rr := httptest.NewRecorder()
		req := httptest.NewRequest(http.MethodGet, "/", nil)

		authInfo := &auth.AuthInfo{
			UserID: "some-user-id",
		}
		ctx := context.WithValue(req.Context(), auth.AuthCtxKey, authInfo)
		req = req.WithContext(ctx)

		chain := alice.New(auth.VerifyAuthMiddleware).Then(protectedHandler)
		chain.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)
		assert.Equal(t, "OK", rr.Body.String())
	})
}
