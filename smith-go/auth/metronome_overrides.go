package auth

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/mitchellh/mapstructure"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/metronome"
)

var GetOrgTenantConfigSource = GetOrgTenantConfig

func UpdateConfigFromOrgConfig(config TenantConfig, orgConfig OrganizationConfig) TenantConfig {
	updatedConfig := config

	// Update MaxIdentities if not set in TenantConfig and set in OrganizationConfig
	if updatedConfig.MaxIdentities == nil && orgConfig.MaxIdentities != 0 {
		temp := orgConfig.MaxIdentities
		updatedConfig.MaxIdentities = &temp
	}

	// Update CanUseRbac if not set in TenantConfig and set in OrganizationConfig
	if updatedConfig.CanUseRbac == nil && orgConfig.CanUseRbac != nil {
		updatedConfig.CanUseRbac = orgConfig.CanUseRbac
	}

	// Update CanAddSeats if not set in TenantConfig and set in OrganizationConfig
	if updatedConfig.CanAddSeats == nil && orgConfig.CanAddSeats != nil {
		updatedConfig.CanAddSeats = orgConfig.CanAddSeats
	}

	// Update StartupPlanApprovalDate if not set in TenantConfig and set in OrganizationConfig
	if updatedConfig.StartupPlanApprovalDate == nil && orgConfig.StartupPlanApprovalDate != nil {
		updatedConfig.StartupPlanApprovalDate = orgConfig.StartupPlanApprovalDate
	}

	// Update PremierPlanApprovalDate if not set in TenantConfig and set in OrganizationConfig
	if updatedConfig.PremierPlanApprovalDate == nil && orgConfig.PremierPlanApprovalDate != nil {
		updatedConfig.PremierPlanApprovalDate = orgConfig.PremierPlanApprovalDate
	}

	return updatedConfig
}

func IsPaymentEnabled() bool {
	return config.Env.AuthType != "none" && config.Env.PaymentEnabled
}

func UpdateTenantConfigFromMetronomeObj(config TenantConfig, metronomeObj map[string]interface{}) (TenantConfig, error) {
	data, ok := metronomeObj["data"].(map[string]interface{})
	if !ok {
		return config, nil
	}
	customFields, ok := data["custom_fields"].(map[string]interface{})
	if !ok {
		return config, nil
	}

	decoderConfig := &mapstructure.DecoderConfig{
		Result:           &config,
		WeaklyTypedInput: true,
		TagName:          "json",
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return config, err
	}
	err = decoder.Decode(customFields)
	if err != nil {
		return config, err
	}

	return config, nil
}

func UpdateOrganizationConfigFromMetronomeObj(config OrganizationConfig, metronomeObj map[string]interface{}) (OrganizationConfig, error) {
	data, ok := metronomeObj["data"].(map[string]interface{})
	if !ok {
		return config, nil
	}
	customFields, ok := data["custom_fields"].(map[string]interface{})
	if !ok {
		return config, nil
	}

	decoderConfig := &mapstructure.DecoderConfig{
		Result:           &config,
		WeaklyTypedInput: true,
		TagName:          "json",
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return config, err
	}
	err = decoder.Decode(customFields)
	if err != nil {
		return config, err
	}

	return config, nil
}

func GetOrgTenantConfig(
	ctx context.Context,
	db *database.AuditLoggedPool,
	queueingRedisClient redis.UniversalClient,
	orgConfig OrganizationConfig,
	tenantConfig TenantConfig,
	organizationID string,
	isPersonal bool,
	metronomeCustomerId string,
) (OrganizationConfig, TenantConfig, *bool, error) {
	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return orgConfig, tenantConfig, nil, fmt.Errorf("invalid organization ID: %w", err)
	}

	if tenantConfig.OrganizationConfig != nil {
		tenantConfig = UpdateConfigFromOrgConfig(tenantConfig, *tenantConfig.OrganizationConfig)
	}

	// if personal organization, set max_identities to 1
	if isPersonal {
		orgConfig.MaxIdentities = 1
	}

	paymentEnabled := IsPaymentEnabled()
	if paymentEnabled && metronomeCustomerId != "" {
		cachedMetInfo, err := metronome.ForceGetMetronomeCache(ctx, db, queueingRedisClient, orgUUID)
		if err != nil {
			return orgConfig, tenantConfig, nil, err
		}

		hasPlan := false
		if cachedMetInfo != nil {
			if cachedMetInfo.PlanID != nil {
				hasPlan = true
				tenantConfig, err = UpdateTenantConfigFromMetronomeObj(tenantConfig, cachedMetInfo.PlanDetails)
				if err != nil {
					return orgConfig, tenantConfig, &hasPlan, err
				}
				orgConfig, err = UpdateOrganizationConfigFromMetronomeObj(orgConfig, cachedMetInfo.PlanDetails)
				if err != nil {
					return orgConfig, tenantConfig, &hasPlan, err
				}
			}

			tenantConfig, err = UpdateTenantConfigFromMetronomeObj(tenantConfig, cachedMetInfo.CustomerDetails)
			if err != nil {
				return orgConfig, tenantConfig, &hasPlan, err
			}
			orgConfig, err = UpdateOrganizationConfigFromMetronomeObj(orgConfig, cachedMetInfo.CustomerDetails)
			if err != nil {
				return orgConfig, tenantConfig, &hasPlan, err
			}
		}
		// Return whether they have a plan if payment is enabled and there's a customer ID
		return orgConfig, tenantConfig, &hasPlan, nil
	}

	// Return nil for hasPlan if payment isn't enabled, false if it is enabled but no customer ID
	if paymentEnabled {
		hasPlan := false
		return orgConfig, tenantConfig, &hasPlan, nil
	}

	// Handle the case where payment is not enabled
	return orgConfig, tenantConfig, nil, nil
}

func GetResolvedOrgConfig(
	ctx context.Context,
	db *database.AuditLoggedPool,
	queueingRedisClient redis.UniversalClient,
	config OrganizationConfig,
	organizationID string,
	isPersonal bool,
	metronomeCustomerId string,
) (OrganizationConfig, *bool, error) {
	orgUUID, err := uuid.Parse(organizationID)
	if err != nil {
		return config, nil, fmt.Errorf("invalid organization ID: %w", err)
	}

	// if personal organization, set max_identities to 1
	if isPersonal {
		config.MaxIdentities = 1
	}

	// if billing enabled, fetch custom fields from metronome
	paymentEnabled := IsPaymentEnabled()
	if paymentEnabled && metronomeCustomerId != "" {
		cachedMetInfo, err := metronome.ForceGetMetronomeCache(ctx, db, queueingRedisClient, orgUUID)
		if err != nil {
			return config, nil, err
		}

		hasPlan := false
		if cachedMetInfo != nil {
			if cachedMetInfo.PlanID != nil {
				hasPlan = true
				// Update from plan details first
				config, err = UpdateOrganizationConfigFromMetronomeObj(config, cachedMetInfo.PlanDetails)
				if err != nil {
					return config, &hasPlan, err
				}
			}

			// Then update from customer details
			config, err = UpdateOrganizationConfigFromMetronomeObj(config, cachedMetInfo.CustomerDetails)
			if err != nil {
				return config, &hasPlan, err
			}
		}
		return config, &hasPlan, nil
	}

	// Handle the case where payment is not enabled
	if paymentEnabled {
		hasPlan := false
		return config, &hasPlan, nil
	}

	return config, nil, nil
}
