package auth

import (
	"context"
	"errors"
	"log/slog"
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/httplog/v2"
	"langchain.com/smith/config"
	"langchain.com/smith/util"

	"github.com/go-chi/render"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

// MetronomeConfigMiddleware handles overriding configs with metronome values
type MetronomeConfigMiddleware struct {
	pg                  *database.AuditLoggedPool
	cachingRedisClient  redis.UniversalClient
	queueingRedisClient redis.UniversalClient
	cacheTTL            time.Duration

	// Caches for config overrides
	orgTenantConfigCache *lsredis.Cache[uint64, *OrgTenantConfigCacheEntry]
	orgConfigCache       *lsredis.Cache[uint64, *OrgConfigCacheEntry]

	AllowNoPlans bool
}

type OrgTenantConfigCacheEntry struct {
	OrgConfig    OrganizationConfig
	TenantConfig TenantConfig
	HasPlan      *bool
	Error        string
}

type OrgConfigCacheEntry struct {
	OrgConfig OrganizationConfig
	HasPlan   *bool
	Error     string
}

func NewMetronomeConfigMiddleware(
	pg *database.AuditLoggedPool,
	cachingRedisClient redis.UniversalClient,
	queueingRedisClient redis.UniversalClient,
	cacheTTLSecs int,
	allowNoPlans bool,
) *MetronomeConfigMiddleware {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	return &MetronomeConfigMiddleware{
		pg:                  pg,
		cachingRedisClient:  cachingRedisClient,
		queueingRedisClient: queueingRedisClient,
		cacheTTL:            ttl,
		orgTenantConfigCache: lsredis.NewCache[uint64, *OrgTenantConfigCacheEntry](
			cachingRedisClient,
			"metronomeOrgTenantConfig",
			ttl,
		),
		orgConfigCache: lsredis.NewCache[uint64, *OrgConfigCacheEntry](
			cachingRedisClient,
			"metronomeOrgConfig",
			ttl,
		),
		AllowNoPlans: allowNoPlans,
	}
}

func (m *MetronomeConfigMiddleware) OverrideAuthConfig(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(ctx)

		// Get auth info from context
		auth, ok := ctx.Value(AuthCtxKey).(*AuthInfo)
		if !ok {
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized - missing auth info"})
			return
		}

		ctx = config.LogAndContextSetField(ctx, "organization_id", slog.StringValue(auth.OrganizationID))
		ctx = config.LogAndContextSetField(ctx, "tenant_id", slog.StringValue(auth.TenantID))

		// Get cached org and tenant config overrides
		cacheKey := lsredis.StringToHash(
			"metronome-config",
			auth.OrganizationID,
			strconv.FormatBool(auth.OrganizationIsPersonal),
		)

		entry, err := m.orgTenantConfigCache.GetFresh(ctx, cacheKey, func() (*OrgTenantConfigCacheEntry, error) {
			orgConfig, tenantConfig, hasPlan, err := GetOrgTenantConfigSource(
				ctx,
				m.pg,
				m.queueingRedisClient,
				*auth.OrganizationConfig,
				*auth.TenantConfig,
				auth.OrganizationID,
				auth.OrganizationIsPersonal,
				auth.OrganizationMetronomeCustomerId,
			)
			var errMsg string
			if err != nil {
				errMsg = err.Error() // Store error message as a string
			}
			return &OrgTenantConfigCacheEntry{
				OrgConfig:    orgConfig,
				TenantConfig: tenantConfig,
				HasPlan:      hasPlan,
				Error:        errMsg,
			}, nil
		})

		if err != nil {
			oplog.Error("error getting metronome config cache", "err", err)
			status, msg := http.StatusInternalServerError, "Internal Server Error"
			if util.IsRetriableError(err) {
				status, msg = http.StatusServiceUnavailable, "Service Unavailable"
			}
			render.Status(r, status)
			render.JSON(w, r, map[string]string{"error": msg})
			return
		}

		if entry.Error != "" {
			oplog.Error("error in cached metronome config", "err", entry.Error)
			status, msg := http.StatusInternalServerError, "Internal Server Error"
			if util.IsRetriableError(errors.New(entry.Error)) {
				status, msg = http.StatusServiceUnavailable, "Service Unavailable"
			}
			render.Status(r, status)
			render.JSON(w, r, map[string]string{"error": msg})
			return
		}

		if !m.AllowNoPlans && entry.HasPlan != nil && !*entry.HasPlan && !auth.OrganizationIsPersonal {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden - no plan found"})
			return
		}

		auth.OrganizationConfig = &entry.OrgConfig
		auth.TenantConfig = &entry.TenantConfig

		// **Ensure the nested OrganizationConfig within TenantConfig is also overridden**
		if auth.TenantConfig != nil {
			auth.TenantConfig.OrganizationConfig = &entry.OrgConfig
		}

		// Update context with modified auth
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// OverrideOrgAuthConfig middleware runs after org auth middleware and overrides configs
func (m *MetronomeConfigMiddleware) OverrideOrgAuthConfig(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(ctx)

		// Get org auth info from context
		orgAuth, ok := ctx.Value(orgAuthCtxKey).(*OrgAuthInfo)
		if !ok {
			next.ServeHTTP(w, r)
			return
		}

		// Get cached org config override
		cacheKey := lsredis.StringToHash(
			"metronome-org-config",
			orgAuth.OrganizationID,
			strconv.FormatBool(orgAuth.OrganizationIsPersonal),
		)

		entry, err := m.orgConfigCache.GetFresh(ctx, cacheKey, func() (*OrgConfigCacheEntry, error) {
			orgConfig, hasPlan, err := GetResolvedOrgConfig(
				ctx,
				m.pg,
				m.queueingRedisClient,
				*orgAuth.OrganizationConfig,
				orgAuth.OrganizationID,
				orgAuth.OrganizationIsPersonal,
				orgAuth.OrganizationMetronomeCustomerId,
			)
			var errMsg string
			if err != nil {
				errMsg = err.Error() // Store error message as a string
			}
			return &OrgConfigCacheEntry{
				OrgConfig: orgConfig,
				HasPlan:   hasPlan,
				Error:     errMsg,
			}, nil
		})

		if err != nil {
			oplog.Error("error getting metronome org config cache", "err", err)
			status, msg := http.StatusInternalServerError, "Internal Server Error"
			if util.IsRetriableError(err) {
				status, msg = http.StatusServiceUnavailable, "Service Unavailable"
			}
			render.Status(r, status)
			render.JSON(w, r, map[string]string{"error": msg})
			return
		}

		if entry.Error != "" {
			oplog.Error("error in cached metronome org config", "err", entry.Error)
			status, msg := http.StatusInternalServerError, "Internal Server Error"
			if util.IsRetriableError(errors.New(entry.Error)) {
				status, msg = http.StatusServiceUnavailable, "Service Unavailable"
			}
			render.Status(r, status)
			render.JSON(w, r, map[string]string{"error": msg})
			return
		}

		if !m.AllowNoPlans && entry.HasPlan != nil && !*entry.HasPlan && !orgAuth.OrganizationIsPersonal {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden - no plan found"})
			return
		}

		// Override config in org auth
		orgAuth.OrganizationConfig = &entry.OrgConfig

		// Update context with modified org auth
		ctx = context.WithValue(ctx, orgAuthCtxKey, orgAuth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (m *MetronomeConfigMiddleware) WithAllowNoPlans() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			localM := *m
			localM.AllowNoPlans = true
			localM.OverrideAuthConfig(next).ServeHTTP(w, r)
		})
	}
}

func (m *MetronomeConfigMiddleware) WithAllowNoPlansOrg() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			localM := *m
			localM.AllowNoPlans = true
			localM.OverrideOrgAuthConfig(next).ServeHTTP(w, r)
		})
	}
}
