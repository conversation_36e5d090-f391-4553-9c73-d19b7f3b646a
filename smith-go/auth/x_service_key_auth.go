package auth

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/go-chi/jwtauth/v5"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"github.com/lestrrat-go/jwx/v2/jwt"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

const listTenantsForXServiceKeyAuthQuery = `
SELECT
	-- org fields
	t.organization_id,
	o.is_personal,
	coalesce(o.metronome_customer_id, ''),
	coalesce(o.stripe_customer_id, ''),
	null,
	false,
	ARRAY[]::Text[],
	o.config as organization_config,
    o.disabled as organization_disabled,
	o.public_sharing_disabled as public_sharing_disabled,
	o.sso_only as sso_only,
	-- tenant identity fields
	'',
	false,
	-- user fields
	'',
	'',
	'',
	'',
	false as is_sso_user,
	-- tenant fields
	t.id,
	coalesce(t.tenant_handle, ''),
	t.is_deleted,
	t.config,
	-- identity permissions
	ARRAY[]::Text[],
	-- auth fields
	$2
FROM tenants t
INNER JOIN organizations o ON t.organization_id = o.id
WHERE t.id = $1;`

const listOrgsForXServiceKeyAuthQuery = `
	SELECT
		-- org fields
		o.id as organization_id,
		o.is_personal as organization_is_personal,
		coalesce(o.metronome_customer_id, '') as organization_metronome_customer_id,
		coalesce(o.stripe_customer_id, '') as organization_stripe_customer_id,
		null as identity_id,
		false as identity_read_only,
		ARRAY[]::Text[] as organization_permissions,
		o.config as organization_config,
		o.disabled as organization_disabled,
		o.public_sharing_disabled as public_sharing_disabled,
		o.sso_only as sso_only,
		-- user fields
		'' as user_id,
		'' as ls_user_id,
		'' as user_email,
		'' as user_full_name,
		false as is_sso_user,
		-- api key fields
		'' as default_tenant_id,
		-- service fields
		$2
	FROM organizations o
	WHERE o.id = $1;`

var ValidServicesList = []string{
	// Currently used for all internal services
	"unspecified",
	// Used to power our retool based support flows
	"retool",
}

type StsAuthnError struct {
	Msg string
}

type StsAuthzError struct {
	Msg string
}

type StsError interface {
	Error() string
}

func (e *StsAuthnError) Error() string {
	return e.Msg
}

func (e *StsAuthzError) Error() string {
	return e.Msg
}

type HandlerXServiceKeyAuth struct {
	Pg       *database.AuditLoggedPool
	Jwt      *jwtauth.JWTAuth
	cache    *lsredis.Cache[uint64, *AuthInfo]
	orgCache *lsredis.Cache[uint64, *OrgAuthInfo]
}

func NewHandlerXServiceKeyAuth(pg *database.AuditLoggedPool, cache *lsredis.Cache[uint64, *AuthInfo], orgCache *lsredis.Cache[uint64, *OrgAuthInfo]) *HandlerXServiceKeyAuth {
	return &HandlerXServiceKeyAuth{
		Pg:       pg,
		Jwt:      jwtauth.New("HS256", []byte(config.Env.XServiceAuthJwtSecret), nil),
		cache:    cache,
		orgCache: orgCache,
	}
}

// Validate JWT, tenant access, and permissions
func (h *HandlerXServiceKeyAuth) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		stsKey := r.Header.Get("X-Service-Key")
		if stsKey == "" {
			log.Println("Missing X Service Key")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		auth, err := h.cache.GetFresh(
			ctx,
			lsredis.StringToHash(listTenantsForXServiceKeyAuthQuery, stsKey),
			func() (*AuthInfo, error) {
				token, err := jwtauth.VerifyToken(h.Jwt, stsKey)
				if err != nil {
					log.Println("JWT verification error:", err)
					return nil, &StsAuthnError{Msg: "JWT verification error: " + err.Error()}
				}

				tenantIdFromToken, exists := token.Get("tenant_id")
				if !exists {
					log.Println("No tenant ID in x-service token")
					return nil, &StsAuthnError{Msg: "No tenant ID in sts token"}
				}

				exp, exists := token.Get("exp")
				if !exists {
					log.Println("No expiry in x-service token")
					return nil, &StsAuthnError{Msg: "No expiry in sts token"}
				}

				maxExpiryTs := time.Now().Add(time.Duration(config.Env.XServiceAuthJwtExpirationSeconds) * time.Second)
				if exp.(time.Time).Unix() > maxExpiryTs.Unix() {
					log.Println("Token expiry too far in the future")
					return nil, &StsAuthnError{Msg: "sts token expiry too far in the future"}
				}

				service := token.Subject()
				var isValidService = false
				for _, v := range ValidServicesList {
					if v == service {
						isValidService = true
						break
					}
				}
				if !isValidService {
					log.Println("Invalid service in STS token")
					return nil, &StsAuthzError{Msg: "invalid service in STS token"}
				}

				rows, _ := h.Pg.Query(
					ctx,
					listTenantsForXServiceKeyAuthQuery,
					tenantIdFromToken,
					service,
				)

				auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No tenant with ID %s found", tenantIdFromToken)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Println("pgx error: ", err)
					return nil, err // unknown error, not cached
				}

				InitializeAndUnmarshalConfig(auth)

				return auth, nil
			},
		)

		if err != nil || auth == nil {
			if _, ok := err.(*StsAuthzError); ok {
				render.Status(r, http.StatusForbidden)
				render.JSON(w, r, map[string]string{"error": "Forbidden"})
				return
			} else {
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			}
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerXServiceKeyAuth) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r, "X-Tenant-Id", "X-Service-Key")
}

func (h *HandlerXServiceKeyAuth) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	render.Status(r, http.StatusForbidden)
	render.JSON(w, r, map[string]string{"error": "Tenantless auth not supported for sts auth"})
}

func (h *HandlerXServiceKeyAuth) OrgMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		organizationId := r.Header.Get("X-Organization-Id")
		if organizationId == "" && config.Env.AuthType != "none" {
			log.Println("Missing organization ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		stsKey := r.Header.Get("X-Service-Key")
		if stsKey == "" {
			log.Println("Missing X Service Key")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		auth, err := h.orgCache.GetFresh(
			ctx,
			lsredis.StringToHash(listOrgsForXServiceKeyAuthQuery, stsKey, organizationId),
			func() (*OrgAuthInfo, error) {
				token, err := jwtauth.VerifyToken(h.Jwt, stsKey)
				if err != nil {
					log.Println("JWT verification error:", err)
					return nil, &StsAuthnError{Msg: "JWT verification error: " + err.Error()}
				}

				organizationIdFromToken, exists := token.Get("organization_id")
				if !exists {
					log.Println("No organization ID in x-service token")
					return nil, &StsAuthnError{Msg: "No organization ID in sts token"}
				}

				exp, exists := token.Get("exp")
				if !exists {
					log.Println("No expiry in x-service token")
					return nil, &StsAuthnError{Msg: "No expiry in sts token"}
				}

				maxExpiryTs := time.Now().Add(time.Duration(config.Env.XServiceAuthJwtExpirationSeconds) * time.Second)
				if exp.(time.Time).Unix() > maxExpiryTs.Unix() {
					log.Println("Token expiry too far in the future")
					return nil, &StsAuthnError{Msg: "sts token expiry too far in the future"}
				}

				if organizationIdFromToken != organizationId && config.Env.AuthType != "none" {
					log.Println("Organization ID mismatch")
					return nil, &StsAuthnError{Msg: "organization ID on token mismatches organization ID on header"}
				}

				service := token.Subject()
				var isValidService = false
				for _, v := range ValidServicesList {
					if v == service {
						isValidService = true
						break
					}
				}
				if !isValidService {
					log.Println("Invalid service in STS token")
					return nil, &StsAuthzError{Msg: "invalid service in STS token"}
				}

				rows, _ := h.Pg.Query(
					ctx,
					listOrgsForXServiceKeyAuthQuery,
					organizationIdFromToken,
					service,
				)

				auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					log.Printf("No organization with ID %s found", organizationIdFromToken)
					return nil, nil // known error, so cached
				} else if err != nil {
					log.Println("pgx error: ", err)
					return nil, err // unknown error, not cached
				}

				InitializeAndUnmarshalOrgConfig(auth)

				return auth, nil
			},
		)

		if err != nil || auth == nil {
			if _, ok := err.(*StsAuthzError); ok {
				render.Status(r, http.StatusForbidden)
				render.JSON(w, r, map[string]string{"error": "Forbidden"})
				return
			} else {
				render.Status(r, http.StatusUnauthorized)
				render.JSON(w, r, map[string]string{"error": "Unauthorized"})
				return
			}
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetXServiceToken(r *http.Request) jwt.Token {
	return r.Context().Value(xServiceTokenKey).(jwt.Token)
}

func (h *HandlerXServiceKeyAuth) XServiceKeyMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		stsKey := r.Header.Get("X-Service-Key")
		if stsKey == "" {
			log.Println("Missing X Service Key")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		token, err := jwtauth.VerifyToken(h.Jwt, stsKey)
		if err != nil {
			log.Println("JWT verification error:", err)
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		expiration := token.Expiration()
		if expiration.IsZero() {
			log.Println("No expiry in x-service token")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}

		service := token.Subject()
		var isValidService = false
		for _, v := range ValidServicesList {
			if v == service {
				isValidService = true
				break
			}
		}

		if !isValidService {
			log.Println("Invalid service in STS token")
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden: invalid service in STS token"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, xServiceTokenKey, token)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerXServiceKeyAuth) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	// Should only run after internal auth middleware
	ctx := r.Context()
	token := GetXServiceToken(r)

	tenantId, exists := token.Get("tenant_id")
	if !exists {
		log.Println("No tenant ID in x-service token")
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}
	parsedTenantId := tenantId.(string)
	auth, err := h.cache.GetFresh(
		ctx,
		lsredis.StringToHash(listTenantsForXServiceKeyAuthQuery, token.JwtID(), parsedTenantId),
		func() (*AuthInfo, error) {
			rows, _ := h.Pg.Query(
				ctx,
				listTenantsForXServiceKeyAuthQuery,
				parsedTenantId,
				token.Subject(),
			)

			auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AuthInfo])
			if err == pgx.ErrNoRows {
				log.Printf("No tenant with ID %s found", tenantId)
				return nil, nil // known error, so cached
			} else if err != nil {
				log.Println("pgx error: ", err)
				return nil, err // unknown error, not cached
			}

			InitializeAndUnmarshalConfig(auth)

			return auth, nil
		},
	)

	if err != nil || auth == nil {
		if _, ok := err.(*StsAuthzError); ok {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		} else {
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func (h *HandlerXServiceKeyAuth) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	// Should only run after internal auth middleware
	ctx := r.Context()
	token := GetXServiceToken(r)

	organizationId, exists := token.Get("organization_id")
	if !exists {
		log.Println("No tenant ID in x-service token")
		render.Status(r, http.StatusUnauthorized)
		render.JSON(w, r, map[string]string{"error": "Unauthorized"})
		return
	}
	parsedOrganization := organizationId.(string)
	auth, err := h.orgCache.GetFresh(
		ctx,
		lsredis.StringToHash(listOrgsForXServiceKeyAuthQuery, token.JwtID(), parsedOrganization),
		func() (*OrgAuthInfo, error) {
			rows, _ := h.Pg.Query(
				ctx,
				listOrgsForXServiceKeyAuthQuery,
				parsedOrganization,
				token.Subject(),
			)

			auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
			if err == pgx.ErrNoRows {
				log.Printf("No organization with ID %s found", organizationId)
				return nil, nil // known error, so cached
			} else if err != nil {
				log.Println("pgx error: ", err)
				return nil, err // unknown error, not cached
			}

			InitializeAndUnmarshalOrgConfig(auth)

			return auth, nil
		},
	)

	if err != nil || auth == nil {
		if _, ok := err.(*StsAuthzError); ok {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		} else {
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

type SignerXServiceKeyAuth struct {
	Jwt *jwtauth.JWTAuth
}

func NewSignerXServiceKeyAuth() *SignerXServiceKeyAuth {
	return &SignerXServiceKeyAuth{
		Jwt: jwtauth.New("HS256", []byte(config.Env.XServiceAuthJwtSecret), nil),
	}
}

func (s *SignerXServiceKeyAuth) SignXServiceKeyToken(tenantId string) (string, error) {
	_, tok, err := s.Jwt.Encode(map[string]interface{}{
		jwt.SubjectKey:    "unspecified",
		jwt.ExpirationKey: time.Now().Add(time.Duration(config.Env.XServiceAuthJwtExpirationSeconds) * time.Second),
		"tenant_id":       tenantId,
	})
	return tok, err
}
