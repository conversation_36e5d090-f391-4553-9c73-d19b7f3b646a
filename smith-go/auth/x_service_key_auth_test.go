package auth_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"

	"github.com/go-chi/jwtauth/v5"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

func TestAuthHandlerStsAuth_Middleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	cache := lsredis.NewCache[uint64, *auth.AuthInfo](redisPool, "authInfo", 0)
	orgCache := lsredis.NewCache[uint64, *auth.OrgAuthInfo](redisPool, "orgAuthInfo", 0)

	ah := auth.NewHandlerXServiceKeyAuth(dbpool, cache, orgCache)

	// Create a new chi router
	logger := TestLogger(t)
	r := chi.NewRouter()
	r.Use(logger)

	// Mount the middleware and handlers
	r.Group(func(r chi.Router) {
		r.Use(ah.XServiceKeyMiddleware)
		r.Get("/internal/auth", ah.FetchInternalAuth)
		r.Get("/internal/org-auth", ah.FetchInternalOrgAuth)
	})

	// Create a test server using httptest
	ts := httptest.NewServer(r)
	defer ts.Close()

	runStsAuthMiddlewareTests(t, dbpool, ah)
	runXServiceMiddlewareTests(t, dbpool, ah)
	runInternalAuthTests(t, dbpool, ts)
	runInternalOrgAuthTests(t, dbpool, ts)
}

func runStsAuthMiddlewareTests(t *testing.T, dbpool *database.AuditLoggedPool, ah auth.Handler) {
	t.Run("missing header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing header org", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing x-organization-id", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", "hello")

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid sts auth key", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", "hello")

		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid sts auth key org", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-Id", "abc123")
		r.Header.Set("X-Service-Key", "hello")

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("valid sts auth key", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`WITH org AS (
				INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
				VALUES ($4, $5, $6, true, (select gen_random_uuid()))
				RETURNING id
			)
			INSERT INTO tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
			VALUES ($1, $2, 'yo', $3, (select id from org), false)
			RETURNING id`,
			expectedTenantId,
			"test tenant",
			`{"max_identities": 4}`,
			"00000000-0000-0000-0000-000000000003",
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		called := false
		organizationConfig := testutil.GetDefaultOrgConfig()
		organizationConfig.MaxIdentities = 8
		tenantConfig := testutil.GetDefaultTenantConfig()
		tenantConfig.MaxIdentities = util.IntPtr(4)
		tenantConfig.OrganizationConfig = organizationConfig
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true

			assert.Equal(
				t,
				&auth.AuthInfo{
					// org attrs
					OrganizationID:                  "00000000-0000-0000-0000-000000000003",
					OrganizationIsPersonal:          true,
					OrganizationMetronomeCustomerId: "",
					OrganizationStripeCustomerId:    "",
					OrganizationIdentityReadOnly:    false,
					OrganizationPermissions:         []string{},
					OrganizationConfig:              organizationConfig,
					// identity attrs
					TenantIdentityID:       "",
					TenantIdentityReadOnly: false,
					// user attrs
					UserID:       "",
					LSUserID:     "",
					UserEmail:    "",
					UserFullName: "",
					// tenant attrs
					TenantID:     expectedTenantId,
					TenantHandle: "yo",
					TenantConfig: tenantConfig,

					// IdentityPermissions are workspace-level
					IdentityPermissions: []string{},
					ServiceIdentity:     "unspecified",
				},
				auth.GetAuthInfo(r),
			)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

	})

	t.Run("valid sts auth org key", func(t *testing.T) {
		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
			VALUES ($1, $2, $3, true, (select gen_random_uuid()))
			RETURNING id
			`,
			expectedOrgId,
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "unspecified",
			"organization_id": expectedOrgId,
			"exp":             time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-Id", expectedOrgId)
		r.Header.Set("X-Service-Key", tokenStr)

		called := false
		organizationConfig := testutil.GetDefaultOrgConfig()
		organizationConfig.MaxIdentities = 8
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			fetchedAuth := auth.GetOrgAuthInfo(r)

			assert.Equal(
				t,
				&auth.OrgAuthInfo{
					// org attrs
					OrganizationID:                  expectedOrgId,
					OrganizationIsPersonal:          true,
					OrganizationMetronomeCustomerId: "",
					OrganizationStripeCustomerId:    "",
					IdentityReadOnly:                false,
					OrganizationPermissions:         []string{},
					OrganizationConfig:              organizationConfig,
					OrganizationDisabled:            false,

					// user attrs
					UserID:       "",
					LSUserID:     "",
					UserEmail:    "",
					UserFullName: "",

					// service attrs
					ServiceIdentity: "unspecified",
				},
				fetchedAuth,
			)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

	})

	t.Run("invalid services are rejected", func(t *testing.T) {

		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`WITH org AS (
				INSERT INTO organizations (id, display_name, is_personal, created_by_user_id) 
				VALUES ($4, $5, true, (select gen_random_uuid())) 
				RETURNING id
				)
				INSERT INTO tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
				VALUES ($1, $2, 'yo', $3, (select id from org), false)
				RETURNING id`,
			expectedTenantId,
			"test tenant",
			`{"max_identities": 4}`,
			"00000000-0000-0000-0000-000000000003",
			"test org",
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "fake_service",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())

	})

	t.Run("invalid services are rejected at org level", func(t *testing.T) {

		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
			VALUES ($1, $2, $3, true, (select gen_random_uuid()))
			RETURNING id
			`,
			expectedOrgId,
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "notvalid",
			"organization_id": expectedOrgId,
			"exp":             time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-Id", expectedOrgId)
		r.Header.Set("X-Service-Key", tokenStr)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("expired token is rejected", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`WITH org AS (
				INSERT INTO organizations (id, display_name, is_personal, created_by_user_id) 
				VALUES ($4, $5, true, (select gen_random_uuid())) 
				RETURNING id
			)
			INSERT INTO tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
			VALUES ($1, $2, 'yo', $3, (select id from org), false)
			RETURNING id`,
			expectedTenantId,
			"test tenant",
			`{"max_identities": 4}`,
			"00000000-0000-0000-0000-000000000003",
			"test org",
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(-1 * time.Hour).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())

	})

	t.Run("expired token rejected at org level", func(t *testing.T) {
		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
			VALUES ($1, $2, $3, true, (select gen_random_uuid()))
			RETURNING id
			`,
			expectedOrgId,
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "unspecified",
			"organization_id": expectedOrgId,
			"exp":             time.Now().Add(-1 * time.Hour).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-Id", expectedOrgId)
		r.Header.Set("X-Service-Key", tokenStr)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("missing expiry is rejected", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`WITH org AS (
				INSERT INTO organizations (id, display_name, is_personal, created_by_user_id) 
				VALUES ($4, $5, true, (select gen_random_uuid())) 
				RETURNING id
			)
			INSERT INTO tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
			VALUES ($1, $2, 'yo', $3, (select id from org), false)
			RETURNING id`,
			expectedTenantId,
			"test tenant",
			`{"max_identities": 4}`,
			"00000000-0000-0000-0000-000000000003",
			"test org",
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
		}

		_, tokenStr, err := jwt.Encode(claims)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())

	})

	t.Run("missing expiry is rejected at org level", func(t *testing.T) {
		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
			VALUES ($1, $2, $3, true, (select gen_random_uuid()))
			RETURNING id
			`,
			expectedOrgId,
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "unspecified",
			"organization_id": expectedOrgId,
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-Id", expectedOrgId)
		r.Header.Set("X-Service-Key", tokenStr)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("too far in future expiry is rejected", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`WITH org AS (
				INSERT INTO organizations (id, display_name, is_personal, created_by_user_id) 
				VALUES ($4, $5, true, (select gen_random_uuid())) 
				RETURNING id
			)
			INSERT INTO tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
			VALUES ($1, $2, 'yo', $3, (select id from org), false)
			RETURNING id`,
			expectedTenantId,
			"test tenant",
			`{"max_identities": 4}`,
			"00000000-0000-0000-0000-000000000003",
			"test org",
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(25 * time.Hour).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())

	})

	t.Run("expiry too far in future is rejected at org level", func(t *testing.T) {
		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
			VALUES ($1, $2, $3, true, (select gen_random_uuid()))
			RETURNING id
			`,
			expectedOrgId,
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "unspecified",
			"organization_id": expectedOrgId,
			"exp":             time.Now().Add(25 * time.Hour).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Organization-Id", expectedOrgId)
		r.Header.Set("X-Service-Key", tokenStr)

		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})
}

func runXServiceMiddlewareTests(t *testing.T, dbpool *database.AuditLoggedPool, ah auth.Handler) {
	t.Run("missing header", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("invalid sts auth key", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", "hello")

		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("no exp fails", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})

	t.Run("valid sts auth key", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		called := false
		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			fetchedToken := auth.GetXServiceToken(r)

			fetchedTenantId, exists := fetchedToken.Get("tenant_id")
			assert.True(t, exists)
			parsedTenantId := fetchedTenantId.(string)
			assert.Equal(
				t,
				parsedTenantId,
				expectedTenantId,
			)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

	})

	t.Run("valid sts auth org key", func(t *testing.T) {
		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "unspecified",
			"organization_id": expectedOrgId,
			"exp":             time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		called := false
		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			fetchedToken := auth.GetXServiceToken(r)

			fetchedOrganizationId, exists := fetchedToken.Get("organization_id")
			assert.True(t, exists)
			parsedOrganizationId := fetchedOrganizationId.(string)
			assert.Equal(
				t,
				parsedOrganizationId,
				expectedOrgId,
			)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

	})

	t.Run("invalid services are rejected", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "fake_service",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)

		// accepts requests when tenant present
		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("expired token is rejected", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(-1 * time.Hour).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)
		r.Header.Set("X-Service-Key", tokenStr)
		ah.XServiceKeyMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
	})
}

func runInternalAuthTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	t.Run("missing header for request", func(t *testing.T) {
		// Prepare your test request
		req, err := http.NewRequest("GET", ts.URL+"/internal/auth", nil)
		if err != nil {
			t.Fatal(err)
		}

		// Perform the request
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatal(err)
		}
		defer resp.Body.Close()

		// Check the response status code
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("valid sts auth key for internal auth", func(t *testing.T) {
		expectedTenantId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`WITH org AS (
				INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
				VALUES ($4, $5, $6, true, (select gen_random_uuid()))
				RETURNING id
			)
			INSERT INTO tenants (id, display_name, tenant_handle, config, organization_id, is_enabled)
			VALUES ($1, $2, 'yo', $3, (select id from org), false)
			RETURNING id`,
			expectedTenantId,
			"test tenant",
			`{"max_identities": 4}`,
			"00000000-0000-0000-0000-000000000003",
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)
		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":       "unspecified",
			"tenant_id": expectedTenantId,
			"exp":       time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		// Prepare your test request
		req, err := http.NewRequest("GET", ts.URL+"/internal/auth", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("X-Service-Key", tokenStr)

		// Perform the request
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatal(err)
		}
		defer resp.Body.Close()

		// Check the response status code
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Check the body
		respAuthInfo := &auth.AuthInfo{}
		if err := json.NewDecoder(resp.Body).Decode(&respAuthInfo); err != nil {
			t.Fatal(err)
		}
		orgConfig := testutil.GetDefaultOrgConfig()
		orgConfig.MaxIdentities = 8
		tenantConfig := testutil.GetDefaultTenantConfig()
		tenantConfig.MaxIdentities = util.IntPtr(4)
		tenantConfig.OrganizationConfig = orgConfig
		assert.Equal(
			t,
			&auth.AuthInfo{
				// org attrs
				OrganizationID:                  "00000000-0000-0000-0000-000000000003",
				OrganizationIsPersonal:          true,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				OrganizationIdentityReadOnly:    false,
				OrganizationPermissions:         []string{},
				OrganizationConfig:              orgConfig,
				// identity attrs
				TenantIdentityID:       "",
				TenantIdentityReadOnly: false,
				// user attrs
				UserID:       "",
				LSUserID:     "",
				UserEmail:    "",
				UserFullName: "",
				// tenant attrs
				TenantID:     expectedTenantId,
				TenantHandle: "yo",
				TenantConfig: tenantConfig,

				// IdentityPermissions are workspace-level
				IdentityPermissions: []string{},
				ServiceIdentity:     "unspecified",
			},
			respAuthInfo,
		)
	})
}

func runInternalOrgAuthTests(t *testing.T, dbpool *database.AuditLoggedPool, ts *httptest.Server) {
	t.Run("missing header for org auth request", func(t *testing.T) {
		// Prepare your test request
		req, err := http.NewRequest("GET", ts.URL+"/internal/org-auth", nil)
		if err != nil {
			t.Fatal(err)
		}

		// Perform the request
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatal(err)
		}
		defer resp.Body.Close()

		// Check the response status code
		assert.Equal(t, http.StatusUnauthorized, resp.StatusCode)
	})

	t.Run("valid sts auth key for internal org auth", func(t *testing.T) {
		expectedOrgId := "c87edfaf-b89c-49ad-bcfa-4bc3ad085edb"
		_, err := dbpool.Exec(
			context.Background(),
			`INSERT INTO organizations (id, display_name, config, is_personal, created_by_user_id) 
			VALUES ($1, $2, $3, true, (select gen_random_uuid()))
			RETURNING id
			`,
			expectedOrgId,
			"test org",
			`{"max_identities": 8}`,
		)
		defer dbpool.Exec(context.Background(), "delete from organizations")
		assert.NoError(t, err)

		jwt := jwtauth.New(
			"HS256",
			[]byte(config.Env.XServiceAuthJwtSecret),
			nil,
		)

		claims := map[string]interface{}{
			"sub":             "unspecified",
			"organization_id": expectedOrgId,
			"exp":             time.Now().Add(5 * time.Minute).Unix(),
		}

		_, tokenStr, err := jwt.Encode(claims)

		assert.NoError(t, err)

		// Prepare your test request
		req, err := http.NewRequest("GET", ts.URL+"/internal/org-auth", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("X-Service-Key", tokenStr)

		// Perform the request
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			t.Fatal(err)
		}
		defer resp.Body.Close()

		// Check the response status code
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Check the body
		respAuthInfo := &auth.OrgAuthInfo{}
		if err := json.NewDecoder(resp.Body).Decode(&respAuthInfo); err != nil {
			t.Fatal(err)
		}
		orgConfig := testutil.GetDefaultOrgConfig()
		orgConfig.MaxIdentities = 8
		assert.Equal(
			t,
			&auth.OrgAuthInfo{
				// org attrs
				OrganizationID:                  expectedOrgId,
				OrganizationIsPersonal:          true,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityReadOnly:                false,
				OrganizationPermissions:         []string{},
				OrganizationConfig:              orgConfig,
				OrganizationDisabled:            false,

				// user attrs
				UserID:       "",
				LSUserID:     "",
				UserEmail:    "",
				UserFullName: "",

				// service attrs
				ServiceIdentity: "unspecified",
			},
			respAuthInfo,
		)
	})
}
