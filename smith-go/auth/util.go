package auth

import (
	"log/slog"
	"reflect"

	"github.com/Netflix/go-env"
)

func InitializeAndUnmarshalConfig(auth *AuthInfo) {
	// Initialize TenantConfig if nil
	if auth.TenantConfig == nil {
		auth.TenantConfig = &TenantConfig{}
	}
	if auth.TenantConfig.OrganizationConfig == nil {
		auth.TenantConfig.OrganizationConfig = &OrganizationConfig{}
	}

	// Unmarshal env vars into temp TenantConfig
	tempTenantConfig := &TenantConfig{
		OrganizationConfig: &OrganizationConfig{},
	}
	if _, err := env.UnmarshalFromEnviron(tempTenantConfig); err != nil {
		slog.Error("Error loading tenant config from environment", "err", err)
	}
	// Merge tempTenantConfig into auth.TenantConfig, only for zero fields
	mergeConfigs(auth.TenantConfig, tempTenantConfig)

	// Unmarshal env vars into temp OrganizationConfig
	tempOrgConfig := &OrganizationConfig{}
	if _, err := env.UnmarshalFromEnviron(tempOrgConfig); err != nil {
		slog.Error("Error loading organization config from environment", "err", err)
	}
	// Initialize OrganizationConfig if nil
	if auth.OrganizationConfig == nil {
		auth.OrganizationConfig = &OrganizationConfig{}
	}
	// Merge tempOrgConfig into auth.OrganizationConfig, only for zero fields
	mergeConfigs(auth.OrganizationConfig, tempOrgConfig)
	auth.TenantConfig.OrganizationConfig = auth.OrganizationConfig
}

func InitializeAndUnmarshalOrgConfig(auth *OrgAuthInfo) {
	// Initialize OrganizationConfig if nil
	if auth.OrganizationConfig == nil {
		auth.OrganizationConfig = &OrganizationConfig{}
	}

	// Unmarshal environment variables into a temporary OrganizationConfig
	tempOrgConfig := &OrganizationConfig{}
	if _, err := env.UnmarshalFromEnviron(tempOrgConfig); err != nil {
		slog.Error("Error loading organization config from environment", "err", err)
	}

	// Merge tempOrgConfig into auth.OrganizationConfig, only setting zero-valued fields
	mergeConfigs(auth.OrganizationConfig, tempOrgConfig)
}

// mergeConfigs merges fields from src into dest, only if dest fields are zero.
func mergeConfigs(dest, src interface{}) {
	destVal := reflect.ValueOf(dest).Elem()
	srcVal := reflect.ValueOf(src).Elem()
	for i := 0; i < destVal.NumField(); i++ {
		destField := destVal.Field(i)
		srcField := srcVal.Field(i)
		// Skip unexported fields
		if !destField.CanSet() {
			continue
		}
		if destField.Kind() == reflect.Struct {
			// Recursively merge nested structs
			mergeConfigs(destField.Addr().Interface(), srcField.Addr().Interface())
		} else if isZeroValue(destField) && !isZeroValue(srcField) {
			destField.Set(srcField)
		}
	}
}

// isZeroValue checks if a reflect.Value is the zero value for its type.
func isZeroValue(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Ptr, reflect.Interface, reflect.Map, reflect.Slice:
		return v.IsNil()
	default:
		zero := reflect.Zero(v.Type())
		return reflect.DeepEqual(v.Interface(), zero.Interface())
	}
}
