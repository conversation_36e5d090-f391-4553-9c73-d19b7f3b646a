package auth_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	. "langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
)

func TestAuthHandlerNone_Middleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewNone(dbpool, redisPool, 0)
	singletonTenantId := "00000000-0000-0000-0000-000000000000"
	config.Env.SingletonTenantId = singletonTenantId
	orgId := "00000000-0000-0000-0000-000000000001"
	tenantIdentityId := "00000000-0000-0000-0000-000000000004"
	orgIdentityId := "00000000-0000-0000-0000-000000000005"

	t.Run("missing", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		// rejects requests when tenant missing
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewNone(dbpool, redisPool, 0)

	t.Run("present", func(t *testing.T) {
		UtilsInit()
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, null, null from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			singletonTenantId, "test tenant", "{}", orgId, "test org",
			singletonTenantId,
			tenantIdentityId,
			orgIdentityId,
			auth.DefaultEmailNone,
			auth.DefaultNameNone,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                config.Env.SingletonTenantId,
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantIdentityId,
				TenantIdentityReadOnly:  false,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				UserID:                  singletonTenantId,
				UserEmail:               auth.DefaultEmailNone,
				UserFullName:            auth.DefaultNameNone,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		res, err := srv.Client().Get(srv.URL + "/auth")

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                config.Env.SingletonTenantId,
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantIdentityId,
			TenantIdentityReadOnly:  false,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			UserID:                  singletonTenantId,
			UserEmail:               auth.DefaultEmailNone,
			UserFullName:            auth.DefaultNameNone,
		}, authResponse)

		// check that subsequent request succeeds
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)

		called = false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                config.Env.SingletonTenantId,
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantIdentityId,
				TenantIdentityReadOnly:  false,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				UserID:                  singletonTenantId,
				UserEmail:               auth.DefaultEmailNone,
				UserFullName:            auth.DefaultNameNone,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr = chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv = httptest.NewServer(rtr)
		defer srv.Close()

		res, err = srv.Client().Get(srv.URL + "/auth")

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                config.Env.SingletonTenantId,
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantIdentityId,
			TenantIdentityReadOnly:  false,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			UserID:                  singletonTenantId,
			UserEmail:               auth.DefaultEmailNone,
			UserFullName:            auth.DefaultNameNone,
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewNone(dbpool, redisPool, 0)

	t.Run("present with other fields set", func(t *testing.T) {
		UtilsInit()
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, null, null from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			singletonTenantId, "test tenant", "{}", orgId, "test org",
			singletonTenantId,
			tenantIdentityId,
			orgIdentityId,
			auth.DefaultEmailNone,
			auth.DefaultNameNone,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		// accepts requests when tenant present
		called := false
		ah.Middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.AuthInfo{
				OrganizationID:          orgId,
				OrganizationIsPersonal:  false,
				OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
				TenantID:                config.Env.SingletonTenantId,
				TenantHandle:            "",
				TenantConfig:            testutil.GetDefaultTenantConfig(),
				TenantIdentityID:        tenantIdentityId,
				TenantIdentityReadOnly:  false,
				IdentityPermissions:     ADMIN_PERMISSIONS,
				OrganizationPermissions: ORG_PERMISSIONS,
				OrganizationConfig:      testutil.GetDefaultOrgConfig(),
				UserID:                  singletonTenantId,
				UserEmail:               auth.DefaultEmailNone,
				UserFullName:            auth.DefaultNameNone,
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListUsersQuery,
			auth.DefaultEmailNone,
		)
		assert.NoError(t, err)
		userInfo, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[auth.UserInfoWithHashedPassword])
		assert.NoError(t, err)
		assert.NotEmpty(t, userInfo.LSUserID, "ls_user_id should be set")
		assert.Equal(t,
			&auth.UserInfoWithHashedPassword{UserID: singletonTenantId, UserEmail: auth.DefaultEmailNone, UserFullName: auth.DefaultNameNone, UserHashedPassword: ""},
			&auth.UserInfoWithHashedPassword{UserID: userInfo.UserID, UserEmail: userInfo.UserEmail, UserFullName: userInfo.UserFullName, UserHashedPassword: userInfo.UserHashedPassword},
		)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			userInfo.LSUserID,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			LSUserID: userInfo.LSUserID,
			Email:    sql.NullString{String: auth.DefaultEmailNone, Valid: true},
			FullName: sql.NullString{String: auth.DefaultNameNone, Valid: true},
		}, providerUserInfo)

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.Middleware).Get("/auth", auth.FetchAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		res, err := srv.Client().Get(srv.URL + "/auth")

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.AuthInfo{
			OrganizationID:          orgId,
			OrganizationIsPersonal:  false,
			OrganizationIdentityID:  sql.NullString{String: orgIdentityId, Valid: true},
			TenantID:                config.Env.SingletonTenantId,
			TenantHandle:            "",
			TenantConfig:            testutil.GetDefaultTenantConfig(),
			TenantIdentityID:        tenantIdentityId,
			TenantIdentityReadOnly:  false,
			IdentityPermissions:     ADMIN_PERMISSIONS,
			OrganizationPermissions: ORG_PERMISSIONS,
			OrganizationConfig:      testutil.GetDefaultOrgConfig(),
			UserID:                  singletonTenantId,
			UserEmail:               auth.DefaultEmailNone,
			UserFullName:            auth.DefaultNameNone,
		}, authResponse)
	})
}

func TestAuthHandlerNone_OrgMiddleware(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewNone(dbpool, redisPool, 0)
	singletonTenantId := "00000000-0000-0000-0000-000000000000"
	config.Env.SingletonTenantId = singletonTenantId
	orgId := "00000000-0000-0000-0000-000000000001"
	orgIdentityId := "00000000-0000-0000-0000-000000000005"

	t.Run("missing", func(t *testing.T) {
		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		// rejects requests when tenant missing
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Fail(t, "should not be called")
		})).ServeHTTP(w, r)
		assert.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	ah = auth.NewNone(dbpool, redisPool, 0)

	t.Run("present", func(t *testing.T) {
		UtilsInit()
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, null, null from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			singletonTenantId, "test tenant", "{}", orgId, "test org",
			singletonTenantId,
			"00000000-0000-0000-0000-000000000004",
			orgIdentityId,
			auth.DefaultEmailNone,
			auth.DefaultNameNone,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		// accepts requests when tenant present
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          singletonTenantId,
				UserEmail:                       auth.DefaultEmailNone,
				UserFullName:                    auth.DefaultNameNone,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		res, err := srv.Client().Get(srv.URL + "/orgs/auth")

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          singletonTenantId,
			UserEmail:                       auth.DefaultEmailNone,
			UserFullName:                    auth.DefaultNameNone,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)

		// subsequent request should succeed
		w = httptest.NewRecorder()
		r = httptest.NewRequest("GET", "/", nil)

		called = false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          singletonTenantId,
				UserEmail:                       auth.DefaultEmailNone,
				UserFullName:                    auth.DefaultNameNone,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr = chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv = httptest.NewServer(rtr)
		defer srv.Close()

		res, err = srv.Client().Get(srv.URL + "/orgs/auth")

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          singletonTenantId,
			UserEmail:                       auth.DefaultEmailNone,
			UserFullName:                    auth.DefaultNameNone,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewNone(dbpool, redisPool, 0)

	t.Run("present with other fields set", func(t *testing.T) {
		UtilsInit()
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, null, null from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			singletonTenantId, "test tenant", "{}", orgId, "test org",
			singletonTenantId,
			"00000000-0000-0000-0000-000000000004",
			orgIdentityId,
			auth.DefaultEmailNone,
			auth.DefaultNameNone,
		)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		r := httptest.NewRequest("GET", "/", nil)

		// accepts requests when tenant present
		called := false
		ah.OrgMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			called = true
			authInfo := auth.GetOrgAuthInfo(r)
			assert.NotEmpty(t, authInfo.LSUserID)
			authInfo.LSUserID = ""
			assert.Equal(t, &auth.OrgAuthInfo{
				OrganizationID:                  orgId,
				OrganizationIsPersonal:          false,
				OrganizationMetronomeCustomerId: "",
				OrganizationStripeCustomerId:    "",
				IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
				IdentityReadOnly:                false,
				UserID:                          singletonTenantId,
				UserEmail:                       auth.DefaultEmailNone,
				UserFullName:                    auth.DefaultNameNone,
				OrganizationPermissions:         ORG_PERMISSIONS,
				OrganizationConfig:              testutil.GetDefaultOrgConfig(),
				ServiceIdentity:                 "",
			}, authInfo)
		})).ServeHTTP(w, r)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, w.Code, w.Body.String())

		// returns json auth info
		rtr := chi.NewRouter()
		rtr.With(ah.OrgMiddleware).Get("/orgs/auth", auth.FetchOrgAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		res, err := srv.Client().Get(srv.URL + "/orgs/auth")

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.OrgAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID)
		authResponse.LSUserID = ""
		assert.Equal(t, &auth.OrgAuthInfo{
			OrganizationID:                  orgId,
			OrganizationIsPersonal:          false,
			OrganizationMetronomeCustomerId: "",
			OrganizationStripeCustomerId:    "",
			IdentityID:                      sql.NullString{String: orgIdentityId, Valid: true},
			IdentityReadOnly:                false,
			UserID:                          singletonTenantId,
			UserEmail:                       auth.DefaultEmailNone,
			UserFullName:                    auth.DefaultNameNone,
			OrganizationPermissions:         ORG_PERMISSIONS,
			OrganizationConfig:              testutil.GetDefaultOrgConfig(),
			ServiceIdentity:                 "",
		}, authResponse)
	})
}

func TestAuthHandlerNone_GetTenantlessAuth(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)
	ah := auth.NewNone(dbpool, redisPool, 0)
	singletonTenantId := "00000000-0000-0000-0000-000000000000"
	config.Env.SingletonTenantId = singletonTenantId

	t.Run("tenant found", func(t *testing.T) {
		orgId := "00000000-0000-0000-0000-000000000003"
		_, err := dbpool.Exec(
			context.Background(),
			`with
org as (
	insert into organizations (id, display_name, created_by_user_id) values ($4, $5, $6) returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id) select $1, $2, $3, id from org returning id
),

usr as (
	insert into users (id, email, full_name) select $6, $9, $10 returning *
),

provider_user as (
	insert into provider_users (ls_user_id, email, full_name, provider, provider_user_id)
	select u.ls_user_id, u.email, u.full_name, null, null from usr u
),

org_identity as (
	insert into identities (id, user_id, organization_id, role_id, access_scope, ls_user_id)
	select $8, $6, org.id, (select id from roles where name = 'ORGANIZATION_USER'), 'organization', (select ls_user_id from usr) from org returning id
)

insert into identities (id, user_id, tenant_id, organization_id, role_id, access_scope, parent_identity_id, ls_user_id)
select $7, $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), 'workspace', $8, (select ls_user_id from usr)
from ten cross join org`,
			singletonTenantId, "test tenant", "{}", orgId, "test org",
			singletonTenantId,
			"00000000-0000-0000-0000-000000000004",
			"00000000-0000-0000-0000-000000000005",
			auth.DefaultEmailNone,
			auth.DefaultNameNone,
		)
		assert.NoError(t, err)

		httptest.NewRequest("GET", "/current/tenants", nil)
		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID, "ls_user_id should be set")
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          singletonTenantId,
			UserEmail:       auth.DefaultEmailNone,
			UserFullName:    auth.DefaultNameNone,
			TenantIDs:       []string{singletonTenantId},
			OrganizationIds: []string{"00000000-0000-0000-0000-000000000003"},
		}, authInfo)

		rows, err := dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			authResponse.LSUserID,
		)
		assert.NoError(t, err)
		providers, err := pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo := auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			LSUserID: authResponse.LSUserID,
			Email:    sql.NullString{String: auth.DefaultEmailNone, Valid: true},
			FullName: sql.NullString{String: auth.DefaultNameNone, Valid: true},
		}, providerUserInfo)

		// subsequent request should succeed
		httptest.NewRequest("GET", "/current/tenants", nil)
		// returns json auth info
		rtr = chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv = httptest.NewServer(rtr)
		defer srv.Close()

		req, err = http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		res, err = srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err = io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse = &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		assert.NotEmpty(t, authResponse.LSUserID, "ls_user_id should be set")
		authInfo = &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          singletonTenantId,
			UserEmail:       auth.DefaultEmailNone,
			UserFullName:    auth.DefaultNameNone,
			TenantIDs:       []string{singletonTenantId},
			OrganizationIds: []string{"00000000-0000-0000-0000-000000000003"},
		}, authInfo)

		rows, err = dbpool.Query(
			context.Background(),
			auth.ListProvidersForUserQuery,
			authResponse.LSUserID,
		)
		assert.NoError(t, err)
		providers, err = pgx.CollectRows(rows, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
		assert.NoError(t, err)
		assert.Len(t, providers, 1)
		providerUserInfo = auth.ProviderUserInfo{
			Provider:       providers[0].Provider,
			LSUserID:       providers[0].LSUserID,
			ProviderUserID: providers[0].ProviderUserID,
			SAMLProviderID: providers[0].SAMLProviderID,
			Email:          providers[0].Email,
			FullName:       providers[0].FullName,
			HashedPassword: providers[0].HashedPassword,
		}
		assert.Equal(t, auth.ProviderUserInfo{
			LSUserID: authResponse.LSUserID,
			Email:    sql.NullString{String: auth.DefaultEmailNone, Valid: true},
			FullName: sql.NullString{String: auth.DefaultNameNone, Valid: true},
		}, providerUserInfo)
	})

	dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	ah = auth.NewNone(dbpool, redisPool, 0)

	t.Run("no tenant found", func(t *testing.T) {
		httptest.NewRequest("GET", "/current/tenants", nil)
		// returns json auth info
		rtr := chi.NewRouter()
		rtr.Get("/current/tenants", ah.GetTenantlessAuth)
		srv := httptest.NewServer(rtr)
		defer srv.Close()

		req, err := http.NewRequest("GET", srv.URL+"/current/tenants", nil)
		assert.NoError(t, err)
		res, err := srv.Client().Do(req)

		assert.NoError(t, err)
		defer res.Body.Close()
		assert.Equal(t, http.StatusOK, res.StatusCode)
		assert.Equal(t, "application/json", res.Header.Get("Content-Type"))
		body, err := io.ReadAll(res.Body)
		assert.NoError(t, err)
		authResponse := &auth.AllTenantsAuthInfo{}
		assert.NoError(t, json.Unmarshal(body, &authResponse))
		authInfo := &auth.AllTenantsAuthInfo{
			UserID:          authResponse.UserID,
			UserEmail:       authResponse.UserEmail,
			UserFullName:    authResponse.UserFullName,
			TenantIDs:       authResponse.TenantIDs,
			OrganizationIds: authResponse.OrganizationIds,
		}
		assert.Equal(t, &auth.AllTenantsAuthInfo{
			UserID:          singletonTenantId,
			UserEmail:       auth.DefaultEmailNone,
			UserFullName:    auth.DefaultNameNone,
			TenantIDs:       []string{},
			OrganizationIds: []string{},
		}, authInfo)
	})
}
