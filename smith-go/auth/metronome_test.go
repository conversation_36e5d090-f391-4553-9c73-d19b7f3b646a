package auth_test

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/database"
	"langchain.com/smith/testutil"
	"langchain.com/smith/util"

	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestMetronomeConfigMiddleware_OverrideAuthConfig(t *testing.T) {
	// Set payment enabled to false to avoid calling Metronome client
	config.Env.PaymentEnabled = false

	// Create an AuthInfo with initial values
	authInfo := &auth.AuthInfo{
		OrganizationID:                  uuid.New().String(),
		OrganizationIsPersonal:          false, // Not a personal organization
		OrganizationMetronomeCustomerId: "",    // Empty to avoid Metronome client call
		OrganizationConfig: &auth.OrganizationConfig{
			MaxIdentities: 10,
		},
		TenantConfig: &auth.TenantConfig{
			OrganizationConfig: &auth.OrganizationConfig{
				MaxIdentities: 10,
			},
		},
	}

	// Put the AuthInfo into the context
	ctx := context.WithValue(context.Background(), auth.AuthCtxKey, authInfo)

	// Create a request with the context
	r := httptest.NewRequest("GET", "/", nil).WithContext(ctx)
	w := httptest.NewRecorder()

	// Create the Redis client
	cachingRedisClient := lsredis.CachingRedisConnect()
	queueingRedisClient := lsredis.SingleRedisConnect()
	defer func() {
		err := queueingRedisClient.Close()
		if err != nil {
			t.Error(err)
		}
	}()

	// Delete only the cache keys used by this test
	err := queueingRedisClient.Del(ctx,
		"metronomeOrgTenantConfig:*",
		"metronomeOrgConfig:*",
	).Err()
	if err != nil {
		t.Fatalf("Failed to clean Redis test keys: %v", err)
	}

	// Create the middleware instance with the Redis client
	m := auth.NewMetronomeConfigMiddleware(nil, cachingRedisClient, queueingRedisClient, 60, false)

	// Next handler that checks the overridden values
	nextCalled := false
	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		nextCalled = true

		// Retrieve the AuthInfo from the context
		authInfoAfter, ok := r.Context().Value(auth.AuthCtxKey).(*auth.AuthInfo)
		if !ok {
			t.Fatal("AuthInfo not found in context")
		}

		// Since OrganizationIsPersonal is false, MaxIdentities should remain unchanged
		assert.Equal(t, 10, authInfoAfter.OrganizationConfig.MaxIdentities)
	})

	// Call the middleware
	testutil.TestLogger(t)(m.OverrideAuthConfig(nextHandler)).ServeHTTP(w, r)

	// Ensure the next handler was called
	assert.True(t, nextCalled)
	// Ensure the response status is OK
	assert.Equal(t, http.StatusOK, w.Code)
}

func TestMetronomeConfigMiddleware_OverrideAuthConfigRetry(t *testing.T) {
	// Set payment enabled to false to avoid calling Metronome client
	config.Env.PaymentEnabled = false

	// Create an AuthInfo with initial values
	authInfo := &auth.AuthInfo{
		OrganizationID:                  uuid.New().String(),
		OrganizationIsPersonal:          false, // Not a personal organization
		OrganizationMetronomeCustomerId: "",    // Empty to avoid Metronome client call
		OrganizationConfig: &auth.OrganizationConfig{
			MaxIdentities: 10,
		},
		TenantConfig: &auth.TenantConfig{
			OrganizationConfig: &auth.OrganizationConfig{
				MaxIdentities: 10,
			},
		},
	}

	// Put the AuthInfo into the context
	ctx := context.WithValue(context.Background(), auth.AuthCtxKey, authInfo)

	// Create a request with the context
	r := httptest.NewRequest("GET", "/", nil).WithContext(ctx)
	w := httptest.NewRecorder()

	// Create the Redis client
	cachingRedisClient := lsredis.CachingRedisConnect()
	queueingRedisClient := lsredis.SingleRedisConnect()
	defer func() {
		err := queueingRedisClient.Close()
		if err != nil {
			t.Error(err)
		}
	}()

	// Delete only the cache keys used by this test
	err := queueingRedisClient.Del(ctx,
		"metronomeOrgTenantConfig:*",
		"metronomeOrgConfig:*",
	).Err()
	if err != nil {
		t.Fatalf("Failed to clean Redis test keys: %v", err)
	}

	// Create the middleware instance with the Redis client
	m := auth.NewMetronomeConfigMiddleware(nil, cachingRedisClient, queueingRedisClient, 60, false)

	// Next handler that checks the overridden values
	nextCalled := false
	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		nextCalled = true

		// Retrieve the AuthInfo from the context
		authInfoAfter, ok := r.Context().Value(auth.AuthCtxKey).(*auth.AuthInfo)
		if !ok {
			t.Fatal("AuthInfo not found in context")
		}

		// Since OrganizationIsPersonal is false, MaxIdentities should remain unchanged
		assert.Equal(t, 10, authInfoAfter.OrganizationConfig.MaxIdentities)
	})

	// mock to return a retrievable error
	auth.GetOrgTenantConfigSource = func(ctx context.Context, db *database.AuditLoggedPool, queueingRedisClient redis.UniversalClient, orgConfig auth.OrganizationConfig, tenantConfig auth.TenantConfig, organizationID string, isPersonal bool, metronomeCustomerId string) (auth.OrganizationConfig, auth.TenantConfig, *bool, error) {
		return orgConfig, tenantConfig, util.BoolPtr(true), errors.New("connection refused")
	}

	// Call the middleware, expect 503
	testutil.TestLogger(t)(m.OverrideAuthConfig(nextHandler)).ServeHTTP(w, r)
	assert.False(t, nextCalled)
	assert.Equal(t, http.StatusServiceUnavailable, w.Code)

	// Call the middleware again, expect same response
	w = httptest.NewRecorder()
	testutil.TestLogger(t)(m.OverrideAuthConfig(nextHandler)).ServeHTTP(w, r)
	assert.False(t, nextCalled)
	assert.Equal(t, http.StatusServiceUnavailable, w.Code)

	// reset mock
	auth.GetOrgTenantConfigSource = auth.GetOrgTenantConfig
}

func TestMetronomeConfigMiddleware_OverrideAuthConfig_PersonalOrg(t *testing.T) {
	// Set payment enabled to false to avoid calling Metronome client
	config.Env.PaymentEnabled = false

	// Create an AuthInfo with initial values for a personal organization
	authInfo := &auth.AuthInfo{
		OrganizationID:                  uuid.New().String(),
		OrganizationIsPersonal:          true, // Personal organization
		OrganizationMetronomeCustomerId: "",   // Empty to avoid Metronome client call
		OrganizationConfig: &auth.OrganizationConfig{
			MaxIdentities: 10, // Initial value
		},
		TenantConfig: &auth.TenantConfig{
			OrganizationConfig: &auth.OrganizationConfig{
				MaxIdentities: 10, // Initial value
			},
		},
	}

	// Put the AuthInfo into the context
	ctx := context.WithValue(context.Background(), auth.AuthCtxKey, authInfo)

	// Create a request with the context
	r := httptest.NewRequest("GET", "/", nil).WithContext(ctx)
	w := httptest.NewRecorder()

	// Create the Redis client
	cachingRedisClient := lsredis.CachingRedisConnect()
	queueingRedisClient := lsredis.SingleRedisConnect()
	defer func() {
		err := queueingRedisClient.Close()
		if err != nil {
			t.Error(err)
		}
	}()

	// Delete only the cache keys used by this test
	err := queueingRedisClient.Del(ctx,
		"metronomeOrgTenantConfig:*",
		"metronomeOrgConfig:*",
	).Err()
	if err != nil {
		t.Fatalf("Failed to clean Redis test keys: %v", err)
	}

	// Create the middleware instance with the Redis client
	m := auth.NewMetronomeConfigMiddleware(nil, cachingRedisClient, queueingRedisClient, 60, false)

	// Next handler that checks the overridden values
	nextCalled := false
	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		nextCalled = true

		// Retrieve the AuthInfo from the context
		authInfoAfter, ok := r.Context().Value(auth.AuthCtxKey).(*auth.AuthInfo)
		if !ok {
			t.Fatal("AuthInfo not found in context")
		}

		// Since OrganizationIsPersonal is true, MaxIdentities should be set to 1
		assert.Equal(t, 1, authInfoAfter.OrganizationConfig.MaxIdentities)
	})

	// Call the middleware
	testutil.TestLogger(t)(m.OverrideAuthConfig(nextHandler)).ServeHTTP(w, r)

	// Ensure the next handler was called
	assert.True(t, nextCalled)
	// Ensure the response status is OK
	assert.Equal(t, http.StatusOK, w.Code)
}
