package auth

import (
	"context"
	"errors"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/util"

	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/jwtauth/v5"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"github.com/lestrrat-go/jwx/v2/jwt"

	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

func NewSupabase(pg *database.AuditLoggedPool, redisClient redis.UniversalClient, cacheTTLSecs int) *HandlerSupabase {
	ttl := time.Duration(cacheTTLSecs) * time.Second
	cache := lsredis.NewCache[uint64, *AuthInfo](redisClient, "authInfo", ttl)
	orgCache := lsredis.NewCache[uint64, *OrgAuthInfo](redisClient, "orgAuthInfo", ttl)
	return &HandlerSupabase{
		Pg:              pg,
		Jwt:             jwtauth.New("HS256", []byte(config.Env.SupabaseJwtSecret), nil),
		ApiKey:          &HandlerApiKey{pg, cache, orgCache},
		XServiceKeyAuth: NewHandlerXServiceKeyAuth(pg, cache, orgCache),
		cache:           cache,
		orgCache:        orgCache,
	}
}

type HandlerSupabase struct {
	Pg              *database.AuditLoggedPool
	Jwt             *jwtauth.JWTAuth
	ApiKey          *HandlerApiKey
	XServiceKeyAuth *HandlerXServiceKeyAuth

	cache    *lsredis.Cache[uint64, *AuthInfo]
	orgCache *lsredis.Cache[uint64, *OrgAuthInfo]
}

func extractSupabaseUserInfo(token jwt.Token) LinkableUserInfo {
	var email string
	if value, ok := token.Get("email"); ok {
		if value, ok := value.(string); ok {
			email = strings.ToLower(value)
		}
	}
	var fullName string
	if value, ok := token.Get("user_metadata"); ok {
		if value, ok := value.(map[string]interface{}); ok {
			if value, ok := value["full_name"]; ok {
				if value, ok := value.(string); ok {
					fullName = value
				}
			}
		}
	}
	var supabaseProvider string
	if value, ok := token.Get("app_metadata"); ok {
		if value, ok := value.(map[string]interface{}); ok {
			if value, ok := value["provider"]; ok {
				if value, ok := value.(string); ok {
					supabaseProvider = value
				}
			}
		}
	}
	var samlProviderId interface{}
	if strings.HasPrefix(supabaseProvider, "sso:") {
		samlProviderId = supabaseProvider[4:]
	}
	var provider string
	if samlProviderId != nil {
		provider = "supabase:sso"
	} else {
		provider = "supabase:non-sso"
	}
	return LinkableUserInfo{
		Email:          email,
		FullName:       fullName,
		Provider:       provider,
		ProviderUserId: token.Subject(),
		SamlProviderId: samlProviderId,
	}
}

// Adds users and provider_users records if necessary.
// Returns resolved provider_users info, or error if provisioning failed.
func maybeProvision(tx pgx.Tx, ctx context.Context, oplog *slog.Logger, incomingUserInfo LinkableUserInfo) (*ProviderUserInfo, error) {
	providerUserRows, _ := tx.Query(
		ctx,
		ListProvidersForEmailOrUserIdQuery,
		incomingUserInfo.Email,
		incomingUserInfo.ProviderUserId,
	)
	providerInfos, err := pgx.CollectRows(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		oplog.Error("pgx error collecting provider user info by email", "err", err)
		return nil, err
	}

	var providerInfoWithMatchingUserId *ProviderUserInfo
	var providerInfoWithMatchingEmail *ProviderUserInfo
	var providerInfoExactMatch *ProviderUserInfo
	for _, v := range providerInfos {
		if v.ProviderUserID.String == incomingUserInfo.ProviderUserId {
			providerInfoWithMatchingUserId = v
		}
		if v.Email.String == incomingUserInfo.Email {
			providerInfoWithMatchingEmail = v
		}
		if v.ProviderUserID.String == incomingUserInfo.ProviderUserId && v.Email.String == incomingUserInfo.Email && v.Provider.String == incomingUserInfo.Provider && v.FullName.String == incomingUserInfo.FullName && (v.SAMLProviderID.String == incomingUserInfo.SamlProviderId || (!v.SAMLProviderID.Valid && incomingUserInfo.SamlProviderId == nil)) {
			providerInfoExactMatch = v
		}
	}

	var incomingIsNewSamlProvider bool
	if len(providerInfos) > 0 && incomingUserInfo.Provider == "supabase:sso" {
		for _, providerInfo := range providerInfos {
			// prevent same user ID, different email address different organizations, different SAML providers
			if providerInfo.ProviderUserID.String == incomingUserInfo.ProviderUserId && providerInfo.Email.String != incomingUserInfo.Email && providerInfo.SAMLProviderID.Valid && providerInfo.SAMLProviderID.String != incomingUserInfo.SamlProviderId {
				oplog.Warn("user ID is already in use with a different SAML provider ID", "existing_saml_provider_id", providerInfo.SAMLProviderID.String)
				return nil, errors.New("user ID is already in use in another organization using SAML SSO")
			}

			// allow same email address, different user ID (implies different IdP), different organizations, different SAML providers
			if providerInfo.Email.String == incomingUserInfo.Email && providerInfo.ProviderUserID.Valid && providerInfo.ProviderUserID.String != incomingUserInfo.ProviderUserId && providerInfo.SAMLProviderID.Valid && providerInfo.SAMLProviderID.String != incomingUserInfo.SamlProviderId {
				oplog.Debug("email is already in use with different user ID and different SAML provider ID", "existing_user_id", providerInfo.ProviderUserID.String, "existing_saml_provider_id", providerInfo.SAMLProviderID.String)
				incomingIsNewSamlProvider = true
			}

			// allow same email address, same user ID (implies same IdP), different organizations, different SAML providers
			if providerInfo.Email.String == incomingUserInfo.Email && providerInfo.ProviderUserID.Valid && providerInfo.ProviderUserID.String == incomingUserInfo.ProviderUserId && providerInfo.SAMLProviderID.Valid && providerInfo.SAMLProviderID.String != incomingUserInfo.SamlProviderId {
				oplog.Debug("email is already in use with same user ID and different SAML provider ID", "existing_saml_provider_id", providerInfo.SAMLProviderID.String)
				incomingIsNewSamlProvider = true
			}

		}
	}

	// fail if attempting an email change with linked login methods
	if len(providerInfos) >= 2 && providerInfoWithMatchingUserId != nil && providerInfoWithMatchingUserId.Email.String != incomingUserInfo.Email {
		oplog.Warn("cannot change email address with linked login methods", "existing_email", providerInfoWithMatchingUserId.Email.String)
		return nil, errors.New("cannot change email address for linked login methods")
	}

	newProvider := len(providerInfos) == 1 && providerInfos[0].Provider.String != incomingUserInfo.Provider && providerInfoWithMatchingEmail != nil

	if len(providerInfos) == 0 {
		// if we have no existing login methods or user, create them
		oplog.Info("creating user and provider user info")
		providerUserRow, _ := tx.Query(
			ctx,
			SafeInsertUserAndProviderUserQuery,
			incomingUserInfo.ProviderUserId,
			incomingUserInfo.Email,
			incomingUserInfo.FullName,
			incomingUserInfo.Provider,
			incomingUserInfo.SamlProviderId,
			incomingUserInfo.ProviderUserId,
		)
		providerInfo, err := pgx.CollectExactlyOneRow(providerUserRow, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
		if err != nil {
			oplog.Error("error creating provider user info", "err", err)
			return nil, err
		}
		return providerInfo, nil
	} else if providerInfoExactMatch != nil {
		// we already have a matching login method
		return providerInfoExactMatch, nil
	} else if newProvider || incomingIsNewSamlProvider {
		// we have an existing login method with the same email address but different provider
		// OR new SAML provider, so link the new login method.
		// Here we link SSO to non-SSO (and vice versa), or an additional SSO login method to any mix of previous ones
		linkProviderInfoByTypeQuery := linkProviderUsersQueryByProvider(incomingUserInfo.Provider)
		linkedProviderInfoRows, _ := tx.Query(
			ctx,
			linkProviderInfoByTypeQuery,
			incomingUserInfo.Email,
			incomingUserInfo.Provider,
			incomingUserInfo.SamlProviderId,
			incomingUserInfo.ProviderUserId,
			incomingUserInfo.FullName,
		)

		linkedProviderInfo, err := pgx.CollectExactlyOneRow(linkedProviderInfoRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
		if err != nil {
			oplog.Error("error linking provider user info", "err", err)
			return nil, err
		}
		oplog.Info("linked provider user info", "linked_user_id", linkedProviderInfo.ProviderUserID.String, "linked_email", linkedProviderInfo.Email.String, "linked_provider", linkedProviderInfo.Provider.String, "linked_saml_provider_id", linkedProviderInfo.SAMLProviderID.String)
		return linkedProviderInfo, nil
	} else if providerInfoWithMatchingUserId != nil && (providerInfoWithMatchingUserId.Email.String != incomingUserInfo.Email || providerInfoWithMatchingUserId.FullName.String != incomingUserInfo.FullName) {
		// we have an existing login method with the same user ID but different email address or full name,
		// so update those fields
		oplog.Info("updating provider user info email or name", "old_email", providerInfoWithMatchingUserId.Email.String, "old_name", providerInfoWithMatchingUserId.FullName.String, "new_email", incomingUserInfo.Email, "new_name", incomingUserInfo.FullName)
		updatedProviderInfoRows, _ := tx.Query(
			ctx,
			updateUserInfoQuery,
			providerInfoWithMatchingUserId.LSUserID,
			incomingUserInfo.Email,
			incomingUserInfo.FullName,
			incomingUserInfo.ProviderUserId,
		)
		updatedProviderInfo, err := pgx.CollectExactlyOneRow(updatedProviderInfoRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
		if err != nil {
			oplog.Error("error updating provider user info", "err", err)
			return nil, err
		}
		return updatedProviderInfo, nil
	} else if len(providerInfos) == 1 && providerInfoWithMatchingEmail != nil && providerInfoWithMatchingUserId == nil {
		// we have a single existing login method with the same email address but different user ID,
		// this should not happen, so return an error
		oplog.Error("email is already in use with different user ID", "existing_user_id", providerInfoWithMatchingEmail.ProviderUserID.String)
		return nil, errors.New("email is already in use with a different user ID")
	}

	oplog.Error("unexpected state provisioning user", "numProviderInfos", len(providerInfos))
	return nil, errors.New("unexpected state provisioning user")
}

func (h *HandlerSupabase) OrgMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(r.Context())
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.OrgMiddleware(next).ServeHTTP(w, r)
			return
		}

		// get organization id header
		organizationId := r.Header.Get("X-Organization-Id")
		if organizationId == "" {
			oplog.Warn("Missing organization ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		ctx = config.LogAndContextSetField(ctx, "organization_id", slog.StringValue(organizationId))

		// verify organization access
		auth, err := h.orgCache.GetFresh(
			ctx,
			lsredis.StringToHash(getOrgInfoForProvisionedUserQuery, r.Header.Get("Authorization"), organizationId),
			func() (*OrgAuthInfo, error) {
				// verify jwt
				token, err := jwtauth.VerifyRequest(h.Jwt, r, jwtauth.TokenFromHeader)
				if err != nil {
					oplog.Warn("JWT verification error", "err", err)
					render.Status(r, http.StatusUnauthorized)
					render.JSON(w, r, map[string]string{"error": "Unauthorized"})
					return nil, nil
				}

				userInfo := extractSupabaseUserInfo(token)
				ctx = logFieldsFromUserInfo(ctx, userInfo)

				if !(userInfo.Provider == "supabase:sso" || userInfo.Provider == "supabase:non-sso") {
					oplog.Error("unknown provider", "provider", userInfo.Provider)
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, nil
				}

				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.ReadCommitted})
				if err != nil {
					return nil, err
				}

				defer func() {
					if err != nil {
						tx.Rollback(r.Context())
						return
					}
					err = tx.Commit(r.Context()) // commit, set return error here
				}()

				// Fail if SSO user attempts to login and SAML provider does not exist,
				// or if non-SSO user attempts to login to an SSO-only organization
				samlProvider, _ := tx.Query(
					ctx,
					getSamlProviderSlimQuery,
					organizationId,
				)
				samlProviderInfo, err := pgx.CollectExactlyOneRow(samlProvider, pgx.RowToAddrOfStructByPos[SamlProviderInfoSlim])
				if err != nil && err != pgx.ErrNoRows {
					oplog.Error("pgx error collecting SAML provider info", "err", err)
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, err
				}
				if samlProviderInfo != nil && samlProviderInfo.SsoOnly && userInfo.Provider != "supabase:sso" {
					oplog.Warn("User is using a non-SSO login method but organization is configured to be SSO only")
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, nil
				}
				if samlProviderInfo == nil && userInfo.Provider == "supabase:sso" {
					oplog.Error("User is using an SSO login method but SAML provider does not exist")
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, nil
				}

				providerUserInfo, err := maybeProvision(tx, ctx, oplog, userInfo)
				if err != nil {
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, err
				}
				samlProviderId := util.StringPtrFromNullString(providerUserInfo.SAMLProviderID)
				orgRows, _ := tx.Query(
					ctx,
					getOrgInfoForProvisionedUserQuery,
					providerUserInfo.ProviderUserID.String,
					providerUserInfo.LSUserID,
					userInfo.Email,
					userInfo.FullName,
					organizationId,
					samlProviderId,
				)

				// validate access
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err != nil {
					if !errors.Is(err, pgx.ErrNoRows) {
						oplog.Error("pgx error collecting org auth info", "err", err)
					}
					return nil, err
				}

				InitializeAndUnmarshalOrgConfig(orgAuth)

				return orgAuth, nil
			},
		)
		if err != nil {
			oplog.Error("org auth error", "err", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, orgAuthCtxKey, auth)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerSupabase) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		oplog := httplog.LogEntry(r.Context())
		if r.Header.Get("X-Api-Key") != "" {
			h.ApiKey.Middleware(next).ServeHTTP(w, r)
			return
		}

		if r.Header.Get("X-Service-Key") != "" {
			h.XServiceKeyAuth.Middleware(next).ServeHTTP(w, r)
			return
		}

		// get tenant id header
		tenantId := r.Header.Get("X-Tenant-Id")
		if tenantId == "" {
			oplog.Warn("Missing tenant ID")
			render.Status(r, http.StatusUnauthorized)
			render.JSON(w, r, map[string]string{"error": "Unauthorized"})
			return
		}
		ctx = config.LogAndContextSetField(ctx, "tenant_id", slog.StringValue(tenantId))

		// verify tenant access
		auth, err := h.cache.GetFresh(
			ctx,
			lsredis.StringToHash(getTenantInfoForUserQuery, getOrgInfoForProvisionedUserQuery, r.Header.Get("Authorization"), tenantId),
			func() (*AuthInfo, error) {
				// verify jwt
				token, err := jwtauth.VerifyRequest(h.Jwt, r, jwtauth.TokenFromHeader)
				if err != nil {
					oplog.Warn("JWT verification error", "err", err)
					render.Status(r, http.StatusUnauthorized)
					render.JSON(w, r, map[string]string{"error": "Unauthorized"})
					return nil, nil
				}

				userInfo := extractSupabaseUserInfo(token)
				ctx = logFieldsFromUserInfo(ctx, userInfo)

				if !(userInfo.Provider == "supabase:sso" || userInfo.Provider == "supabase:non-sso") {
					oplog.Error("unknown provider", "provider", userInfo.Provider)
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, nil
				}

				// validate access by retrieving org info then tenant info in one transaction.
				// read committed (not repeatable read) required for insert to be visible to subsequent select
				tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.ReadCommitted})
				if err != nil {
					return nil, err
				}

				defer func() {
					if err != nil {
						tx.Rollback(r.Context())
						return
					}
					err = tx.Commit(r.Context()) // commit, set return error here
				}()

				// retrieve org ID from tenant ID if not provided
				organizationId := r.Header.Get("X-Organization-Id")
				var resolvedOrgId string
				if organizationId == "" {
					orgIdRow, _ := tx.Query(
						ctx,
						getOrgIdFromTenantIdQuery,
						tenantId,
					)
					if orgId, err := pgx.CollectExactlyOneRow(orgIdRow, pgx.RowTo[string]); err == nil {
						resolvedOrgId = orgId
					} else if err != nil || resolvedOrgId == "" {
						oplog.Error("No organization with tenant ID found")
						return nil, err // unknown error (unable to find org), not cached
					}
				} else {
					resolvedOrgId = organizationId
				}

				ctx = config.LogAndContextSetField(ctx, "organization_id", slog.StringValue(resolvedOrgId))

				// Fail if SSO user attempts to login and SAML provider does not exist,
				// or if non-SSO user attempts to login to an SSO-only organization
				samlProvider, _ := tx.Query(
					ctx,
					getSamlProviderSlimQuery,
					resolvedOrgId,
				)
				samlProviderInfo, err := pgx.CollectExactlyOneRow(samlProvider, pgx.RowToAddrOfStructByPos[SamlProviderInfoSlim])
				if err != nil && err != pgx.ErrNoRows {
					oplog.Error("pgx error collecting SAML provider info", "err", err)
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, err
				}
				if samlProviderInfo != nil && samlProviderInfo.SsoOnly && userInfo.Provider != "supabase:sso" {
					oplog.Warn("User is using a non-SSO login method but organization is configured to be SSO only")
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, nil
				}
				if samlProviderInfo == nil && userInfo.Provider == "supabase:sso" {
					oplog.Error("User is using an SSO login method but SAML provider does not exist")
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, nil
				}

				providerUserInfo, err := maybeProvision(tx, ctx, oplog, userInfo)
				if err != nil {
					render.Status(r, http.StatusForbidden)
					render.JSON(w, r, map[string]string{"error": "Forbidden"})
					return nil, err
				}
				samlProviderId := util.StringPtrFromNullString(providerUserInfo.SAMLProviderID)
				orgRows, _ := tx.Query(
					ctx,
					getOrgInfoForProvisionedUserQuery,
					providerUserInfo.ProviderUserID.String,
					providerUserInfo.LSUserID,
					userInfo.Email,
					userInfo.FullName,
					resolvedOrgId,
					samlProviderId,
				)

				// validate access
				orgAuth, err := pgx.CollectExactlyOneRow(orgRows, pgx.RowToAddrOfStructByPos[OrgAuthInfo])
				if err == pgx.ErrNoRows {
					oplog.Warn("No login method found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Error("pgx error collecting org auth info in workspace middleware", "err", err)
					return nil, err // unknown error, not cached
				}

				providerUserRows, _ := tx.Query(
					ctx,
					ListProvidersQuery,
					userInfo.ProviderUserId,
					userInfo.Email,
					userInfo.Provider,
					userInfo.SamlProviderId,
				)
				providerInfo, err := pgx.CollectExactlyOneRow(providerUserRows, pgx.RowToAddrOfStructByPos[ProviderUserInfo])
				if err == pgx.ErrNoRows {
					oplog.Warn("No login method found")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Error("pgx error collecting provider user info in workspace middleware", "err", err)
					return nil, err // unknown error, not cached
				}
				ctx = config.LogAndContextSetField(ctx, "ls_user_id", slog.StringValue(providerInfo.LSUserID))

				// tenant info
				rows, _ := tx.Query(
					ctx,
					getTenantInfoForUserQuery,
					providerInfo.LSUserID,
					tenantId,
				)
				tenantAuth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[TenantAuthInfo])
				if err == pgx.ErrNoRows {
					oplog.Warn("No identity found for user")
					return nil, nil // known error, so cached
				} else if err != nil {
					oplog.Error("pgx error collecting workspace auth info in workspace middleware", "err", err)
					return nil, err // unknown error, not cached
				}

				auth := &AuthInfo{
					OrganizationID:                  orgAuth.OrganizationID,
					OrganizationIsPersonal:          orgAuth.OrganizationIsPersonal,
					OrganizationMetronomeCustomerId: orgAuth.OrganizationMetronomeCustomerId,
					OrganizationStripeCustomerId:    orgAuth.OrganizationStripeCustomerId,
					OrganizationIdentityID:          orgAuth.IdentityID,
					OrganizationIdentityReadOnly:    orgAuth.IdentityReadOnly,
					OrganizationPermissions:         orgAuth.OrganizationPermissions,
					OrganizationConfig:              orgAuth.OrganizationConfig,
					OrganizationDisabled:            orgAuth.OrganizationDisabled,
					PublicSharingDisabled:           orgAuth.PublicSharingDisabled,
					SsoOnly:                         orgAuth.SsoOnly,
					UserID:                          orgAuth.UserID,
					LSUserID:                        orgAuth.LSUserID,
					UserEmail:                       orgAuth.UserEmail,
					UserFullName:                    orgAuth.UserFullName,
					IsSsoUser:                       orgAuth.IsSsoUser,
					TenantID:                        tenantAuth.TenantID,
					TenantHandle:                    tenantAuth.TenantHandle,
					TenantIsDeleted:                 tenantAuth.TenantIsDeleted,
					TenantConfig:                    tenantAuth.TenantConfig,
					TenantIdentityID:                tenantAuth.IdentityID,
					TenantIdentityReadOnly:          tenantAuth.IdentityReadOnly,
					IdentityPermissions:             tenantAuth.IdentityPermissions,
					ServiceIdentity:                 "",
				}

				InitializeAndUnmarshalConfig(auth)

				return auth, nil
			},
		)
		if err != nil {
			oplog.Error("workspace auth error", "err", err)
		}
		if err != nil || auth == nil {
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}

		// set auth context
		ctx = context.WithValue(ctx, AuthCtxKey, auth)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (h *HandlerSupabase) CacheKey(r *http.Request) uint64 {
	return cacheKeyWithHeaders(r, "X-Tenant-Id", "Authorization", "X-Api-Key", "X-Service-Key")
}

func (h *HandlerSupabase) GetTenantlessAuth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	if r.Header.Get("X-Api-Key") != "" {
		h.ApiKey.GetTenantlessAuth(w, r)
		return
	}

	// Get tenants
	token, err := jwtauth.VerifyRequest(h.Jwt, r, jwtauth.TokenFromHeader)
	if err != nil {
		oplog.Error("error verifying JWT in tenantless auth", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}

	userInfo := extractSupabaseUserInfo(token)
	ctx = logFieldsFromUserInfo(ctx, userInfo)

	if userInfo.Provider != "supabase:sso" && userInfo.Provider != "supabase:non-sso" {
		oplog.Error("unknown provider", "provider", userInfo.Provider)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}

	tx, err := h.Pg.BeginTx(ctx, pgx.TxOptions{IsoLevel: pgx.ReadCommitted})
	if err != nil {
		oplog.Error("error starting transaction in tenantless auth", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}

	defer func() {
		if err != nil {
			tx.Rollback(ctx)
			return
		}
		err = tx.Commit(ctx) // commit, set return error here
	}()

	// Fail if SSO user attempts to login and SAML provider does not exist
	if userInfo.Provider == "supabase:sso" && userInfo.SamlProviderId != nil {
		samlProvider, _ := tx.Query(
			ctx,
			getSamlProviderByProviderIdSlimQuery,
			userInfo.SamlProviderId,
		)
		samlProviderInfo, err := pgx.CollectExactlyOneRow(samlProvider, pgx.RowToAddrOfStructByPos[SamlProviderInfoSlim])
		if err != nil && err != pgx.ErrNoRows {
			oplog.Error("pgx error collecting SAML provider info", "err", err)
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}
		if samlProviderInfo == nil && userInfo.Provider == "supabase:sso" {
			oplog.Error("User is using an SSO login method but SAML provider does not exist")
			render.Status(r, http.StatusForbidden)
			render.JSON(w, r, map[string]string{"error": "Forbidden"})
			return
		}
	}

	providerUserInfo, err := maybeProvision(tx, ctx, oplog, userInfo)
	if err != nil {
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	rows, _ := tx.Query(
		ctx,
		listTenantsAndOrgsForSupabaseUserQuery,
		providerUserInfo.ProviderUserID.String,
		providerUserInfo.LSUserID,
		userInfo.Email,
		userInfo.FullName,
		userInfo.Provider,
		userInfo.SamlProviderId,
	)

	auth, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByPos[AllTenantsAuthInfo])
	if err != nil {
		oplog.Error("error collecting tenantless auth", "err", err)
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "Forbidden"})
		return
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, auth)
}

func (h *HandlerSupabase) XServiceKeyMiddleware(next http.Handler) http.Handler {
	return h.XServiceKeyAuth.XServiceKeyMiddleware(next)
}

func (h *HandlerSupabase) FetchInternalAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalAuth(w, r)
}

func (h *HandlerSupabase) FetchInternalOrgAuth(w http.ResponseWriter, r *http.Request) {
	h.XServiceKeyAuth.FetchInternalOrgAuth(w, r)
}
