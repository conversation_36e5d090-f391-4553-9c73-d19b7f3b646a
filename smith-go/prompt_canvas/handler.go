package prompt_canvas

import (
	"github.com/ggicci/httpin"
	"github.com/go-chi/render"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	"net/http"
)

type PromptCanvasHandler struct {
	Pg *database.AuditLoggedPool
}

func (h *PromptCanvasHandler) CreateQuickActionHandler(w http.ResponseWriter, r *http.Request) {
	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		http.Error(w, "unauthorized", http.StatusForbidden)
		return
	}

	req := r.Context().Value(httpin.Input).(*CreateQuickActionRequest)

	if req.Payload.Name == "" {
		http.Error(w, "name is required", http.StatusBadRequest)
		return
	}
	if req.Payload.Prompt == "" {
		http.Error(w, "prompt is required", http.StatusBadRequest)
		return
	}

	quickAction, err := CreateQuickAction(r.Context(), h.Pg, authInfo, req.Payload)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, quickAction)
}

func (h *PromptCanvasHandler) ListQuickActionsHandler(w http.ResponseWriter, r *http.Request) {
	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		http.Error(w, "unauthorized", http.StatusForbidden)
		return
	}

	quickActions, err := ListQuickActions(r.Context(), h.Pg, authInfo)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	render.JSON(w, r, quickActions)
}

func (h *PromptCanvasHandler) DeleteQuickActionHandler(w http.ResponseWriter, r *http.Request) {
	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		http.Error(w, "unauthorized", http.StatusForbidden)
		return
	}

	req := r.Context().Value(httpin.Input).(*DeleteQuickActionRequest)

	if req.Payload.ID == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	err := DeleteQuickAction(r.Context(), h.Pg, authInfo, req.Payload)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (h *PromptCanvasHandler) UpdateQuickActionHandler(w http.ResponseWriter, r *http.Request) {
	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		http.Error(w, "unauthorized", http.StatusForbidden)
		return
	}

	req := r.Context().Value(httpin.Input).(*UpdateQuickActionRequest)

	if req.Payload.ID == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	quickAction, err := UpdateQuickAction(r.Context(), h.Pg, authInfo, req.Payload)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	render.JSON(w, r, quickAction)
}
