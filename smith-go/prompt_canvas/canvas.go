package prompt_canvas

import (
	"context"
	"fmt"
	"time"

	"langchain.com/smith/auth"

	"errors"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/database"
)

type QuickAction struct {
	ID          uuid.UUID `json:"id"`
	TenantID    uuid.UUID `json:"tenant_id"`
	Name        string    `json:"name"`
	Prompt      string    `json:"prompt"`
	Description *string   `json:"description,omitempty"`
	Icon        *string   `json:"icon,omitempty"`
	CreatedBy   uuid.UUID `json:"created_by"`
	UpdatedBy   uuid.UUID `json:"updated_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type CreateQuickActionRequest struct {
	Payload CreateQuickActionPayload `in:"body"`
}

type CreateQuickActionPayload struct {
	Name        string `json:"name"`
	Prompt      string `json:"prompt"`
	Description string `json:"description,omitempty"`
	Icon        string `json:"icon,omitempty"`
}

type DeleteQuickActionRequest struct {
	Payload DeleteQuickActionPayload `in:"body"`
}

type DeleteQuickActionPayload struct {
	ID string `json:"id"`
}

type UpdateQuickActionRequest struct {
	Payload UpdateQuickActionPayload `in:"body"`
}

type UpdateQuickActionPayload struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Prompt      string `json:"prompt"`
	Description string `json:"description,omitempty"`
	Icon        string `json:"icon,omitempty"`
}

func CreateQuickAction(ctx context.Context, db *database.AuditLoggedPool, authInfo *auth.AuthInfo, payload CreateQuickActionPayload) (*QuickAction, error) {
	createdQuickActionRows, _ := db.Query(ctx, `
		WITH new_quick_action AS (
			INSERT INTO quick_actions (
				tenant_id, name, prompt, description, icon, created_by, updated_by, created_at, updated_at
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
			)
			ON CONFLICT (tenant_id, name) DO NOTHING
			RETURNING *
		)
		SELECT id, tenant_id, name, prompt, description, icon, created_by, updated_by, created_at, updated_at FROM new_quick_action
	`, authInfo.TenantID, payload.Name, payload.Prompt, payload.Description, payload.Icon, authInfo.LSUserID, authInfo.LSUserID)

	createdQuickAction, err := pgx.CollectExactlyOneRow(createdQuickActionRows, pgx.RowToAddrOfStructByPos[QuickAction])
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("quick action not created because a quick action with the given name already exists")
		}
		return nil, fmt.Errorf("failed to create quick action: %w", err)
	}
	return createdQuickAction, nil
}

func ListQuickActions(ctx context.Context, db *database.AuditLoggedPool, authInfo *auth.AuthInfo) ([]*QuickAction, error) {
	quickActionRows, _ := db.Query(ctx, "SELECT id, tenant_id, name, prompt, description, icon, created_by, updated_by, created_at, updated_at FROM quick_actions WHERE tenant_id = $1", authInfo.TenantID)
	quickActions, err := pgx.CollectRows(quickActionRows, pgx.RowToAddrOfStructByPos[QuickAction])
	if err != nil {
		return nil, errors.New("failed to list quick actions")
	}
	return quickActions, nil
}

func DeleteQuickAction(ctx context.Context, db *database.AuditLoggedPool, authInfo *auth.AuthInfo, payload DeleteQuickActionPayload) error {

	_, err := uuid.Parse(payload.ID)
	if err != nil {
		return fmt.Errorf("invalid UUID format: %w", err)
	}
	deletedQuickActionRows, _ := db.Query(ctx, `
		DELETE FROM quick_actions
		WHERE id = $1 AND tenant_id = $2
		RETURNING id, tenant_id, name, prompt, description, icon, created_by, updated_by, created_at, updated_at
	`, payload.ID, authInfo.TenantID)
	_, err = pgx.CollectExactlyOneRow(deletedQuickActionRows, pgx.RowToAddrOfStructByPos[QuickAction])
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New("quick action not found or does not belong to the tenant")
		}
		return fmt.Errorf("failed to delete quick action: %w", err)
	}
	return nil
}

func UpdateQuickAction(ctx context.Context, db *database.AuditLoggedPool, authInfo *auth.AuthInfo, payload UpdateQuickActionPayload) (*QuickAction, error) {

	_, err := uuid.Parse(payload.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid UUID format: %w", err)
	}

	updatedQuickActionRows, _ := db.Query(ctx, `
		UPDATE quick_actions
		SET name = $1, prompt = $2, description = $3, icon = $4, updated_by = $5, updated_at = CURRENT_TIMESTAMP
		WHERE id = $6 AND tenant_id = $7
		RETURNING id, tenant_id, name, prompt, description, icon, created_by, updated_by, created_at, updated_at
	`, payload.Name, payload.Prompt, payload.Description, payload.Icon, authInfo.LSUserID, payload.ID, authInfo.TenantID)
	updatedQuickAction, err := pgx.CollectExactlyOneRow(updatedQuickActionRows, pgx.RowToAddrOfStructByPos[QuickAction])
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, errors.New("quick action not found or does not belong to the tenant")
		}
		return nil, fmt.Errorf("failed to create quick action: %w", err)
	}
	return updatedQuickAction, nil
}
