package tracer_sessions

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/util"
)

type TraceTier string

const (
	LongLived  TraceTier = "longlived"
	ShortLived TraceTier = "shortlived"
)

var (
	ErrSessionAlreadyExists     = errors.New("session already exists")
	ErrReferenceDatasetNotFound = errors.New("reference dataset not found")
	ErrDefaultDatasetNotFound   = errors.New("default dataset not found")
	ErrSessionEndTimeBeforeRun  = errors.New("session end time is before run start time")
	ErrInvalidTimestampFormat   = errors.New("invalid timestamp format")
	ErrTracerSessionNotFound    = errors.New("tracer session not found")
)

type TracerSessionBase struct {
	ID                 uuid.UUID               `db:"id"`
	StartTime          time.Time               `db:"start_time"`
	EndTime            *time.Time              `db:"end_time"`
	Name               string                  `db:"name"`
	Description        *string                 `db:"description"`
	DefaultDatasetID   *uuid.UUID              `db:"default_dataset_id"`
	Extra              *map[string]interface{} `db:"extra"`
	ReferenceDatasetID *uuid.UUID              `db:"reference_dataset_id"`
	TraceTier          *TraceTier              `db:"trace_tier"`
}

type TracerSessionCreate struct {
	TracerSessionBase
}

type TracerSessionWithoutVirtualFields struct {
	TracerSessionBase
	TenantID uuid.UUID `db:"tenant_id"`
}

type TTLSettings struct {
	DefaultTraceTier TraceTier `db:"default_trace_tier"`
}

func (c *TracerSessionsClient) CreateTracerSession(ctx context.Context, authInfo auth.AuthInfo, tracerSession TracerSessionCreate, upsert bool) (*TracerSessionWithoutVirtualFields, error) {
	tx, err := c.db.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	tracerSessionRecord, err := c.CreateTracerSessionWithinExistingTransaction(ctx, tx, authInfo, tracerSession, upsert)
	if err != nil {
		return nil, err
	}

	if err := tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return tracerSessionRecord, nil
}

func (c *TracerSessionsClient) CreateTracerSessionWithinExistingTransaction(ctx context.Context, tx pgx.Tx, authInfo auth.AuthInfo, tracerSession TracerSessionCreate, upsert bool) (*TracerSessionWithoutVirtualFields, error) {
	if tracerSession.ID == uuid.Nil {
		tracerSession.ID = uuid.New()
	}
	tracerSessionID := tracerSession.ID
	if tracerSession.TraceTier == nil {
		tenantUUID, err := uuid.Parse(authInfo.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to parse tenant ID: %w", err)
		}
		ttlSettings, err := getTraceTierSettingsForTenant(ctx, tx, tenantUUID)
		if err != nil {
			return nil, fmt.Errorf("failed to get trace tier settings: %w", err)
		}
		if ttlSettings != nil {
			tracerSession.TraceTier = &ttlSettings.DefaultTraceTier
		} else {
			defaultTier := TraceTier(config.Env.DefaultTraceTier)
			tracerSession.TraceTier = &defaultTier
		}
	}
	if tracerSession.ReferenceDatasetID != nil {
		tenantUUID, err := uuid.Parse(authInfo.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to parse tenant ID: %w", err)
		}
		exists, err := checkDatasetExists(ctx, tx, *tracerSession.ReferenceDatasetID, tenantUUID)
		if err != nil {
			return nil, fmt.Errorf("failed to validate reference_dataset_id: %w", err)
		}
		if !exists {
			return nil, ErrReferenceDatasetNotFound
		}
	}

	if tracerSession.DefaultDatasetID != nil {
		tenantUUID, err := uuid.Parse(authInfo.TenantID)
		if err != nil {
			return nil, fmt.Errorf("failed to parse tenant ID: %w", err)
		}
		exists, err := checkDatasetExists(ctx, tx, *tracerSession.DefaultDatasetID, tenantUUID)
		if err != nil {
			return nil, fmt.Errorf("failed to validate default_dataset_id: %w", err)
		}
		if !exists {
			return nil, ErrDefaultDatasetNotFound
		}
	}

	var query string
	if upsert {
		query = `
			INSERT INTO tracer_session
			(id, start_time, name, description, tenant_id, default_dataset_id, extra, reference_dataset_id, trace_tier, end_time)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
			ON CONFLICT (name, tenant_id)
			DO UPDATE SET
				extra = EXCLUDED.extra,
				default_dataset_id = EXCLUDED.default_dataset_id,
				reference_dataset_id = EXCLUDED.reference_dataset_id,
				trace_tier = EXCLUDED.trace_tier
			RETURNING id, tenant_id, start_time, end_time, name, description, default_dataset_id, extra, reference_dataset_id, trace_tier
		`
	} else {
		query = `
			INSERT INTO tracer_session
			(id, start_time, name, description, tenant_id, default_dataset_id, extra, reference_dataset_id, trace_tier, end_time)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
			ON CONFLICT DO NOTHING
			RETURNING id, tenant_id, start_time, end_time, name, description, default_dataset_id, extra, reference_dataset_id, trace_tier
		`
	}
	rows, err := tx.Query(ctx, query,
		tracerSessionID,
		tracerSession.StartTime,
		tracerSession.Name,
		tracerSession.Description,
		authInfo.TenantID,
		tracerSession.DefaultDatasetID,
		tracerSession.Extra,
		tracerSession.ReferenceDatasetID,
		*tracerSession.TraceTier,
		tracerSession.EndTime,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute insert query: %w", err)
	}

	tracerSessionRecord, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[TracerSessionWithoutVirtualFields])
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) && !upsert {
			return nil, ErrSessionAlreadyExists
		}
		return nil, fmt.Errorf("failed to execute insert query: %w", err)
	}

	if tracerSessionRecord.ReferenceDatasetID != nil {
		taggingQuery := `
			WITH existing_tagging AS (
				SELECT tag_value_id
				FROM taggings
				WHERE resource_type = 'dataset' AND resource_id = $1
			)
			INSERT INTO taggings
			(tag_value_id, resource_type, resource_id)
			SELECT tag_value_id, 'experiment', $2
			FROM existing_tagging
		`
		_, err := tx.Exec(ctx, taggingQuery, *tracerSessionRecord.ReferenceDatasetID, tracerSessionID)
		if err != nil {
			return nil, fmt.Errorf("failed to insert taggings: %w", err)
		}
	}

	return tracerSessionRecord, nil
}

func checkDatasetExists(ctx context.Context, tx pgx.Tx, datasetID uuid.UUID, tenantID uuid.UUID) (bool, error) {
	var exists bool
	err := tx.QueryRow(ctx, `
		SELECT EXISTS (
			SELECT 1 FROM dataset WHERE id = $1 AND tenant_id = $2
		)
	`, datasetID, tenantID).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to execute dataset existence check: %w", err)
	}
	return exists, nil
}

func getTraceTierSettingsForTenant(ctx context.Context, tx pgx.Tx, tenantID uuid.UUID) (*TTLSettings, error) {
	query := `
		SELECT pctts.default_trace_tier
		FROM per_customer_trace_tier_settings pctts
		JOIN tenants t ON (
			pctts.tenant_id = t.id 
			OR (
				pctts.organization_id = t.organization_id AND pctts.tenant_id IS NULL
			)
		)
		WHERE t.id = $1
		ORDER BY tenant_id DESC
		LIMIT 1
	`
	var defaultTraceTier string
	err := tx.QueryRow(ctx, query, tenantID).Scan(&defaultTraceTier)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to query TTL settings: %w", err)
	}
	return &TTLSettings{DefaultTraceTier: TraceTier(defaultTraceTier)}, nil
}

func (c *TracerSessionsClient) StartOrFetchTracerSession(
	ctx context.Context,
	authInfo auth.AuthInfo,
	sessionID *string,
	sessionName *string,
	startTimeStr *string,
) (*TracerSessionWithoutVirtualFields, error) {
	oplog := httplog.LogEntry(ctx)

	sessionIDValue := ""
	if sessionID != nil && *sessionID != "" {
		sessionIDValue = *sessionID
	}

	sessionNameValue := ""
	if sessionName != nil && *sessionName != "" {
		sessionNameValue = *sessionName
	}

	var startTime time.Time
	if startTimeStr == nil || *startTimeStr == "" {
		startTime = time.Now().UTC()
	} else {
		parsedTime, err := util.ParseFlexibleTimestamp(*startTimeStr)
		if err != nil {
			return nil, fmt.Errorf("%w: %w", ErrInvalidTimestampFormat, err)
		}
		startTime = parsedTime
	}

	cacheKey := fmt.Sprintf("tracer_session:%s:%s:%s", authInfo.TenantID, sessionIDValue, sessionNameValue)

	tracerSession, err := c.cache.GetFresh(ctx, cacheKey, func() (*TracerSessionWithoutVirtualFields, error) {
		var dbSession TracerSessionWithoutVirtualFields

		if sessionIDValue != "" {
			// Fetch session by ID and tenant_id
			rows, err := c.db.Query(ctx, `
				SELECT id, tenant_id, start_time, end_time, 
				       name, description, default_dataset_id, extra, reference_dataset_id, trace_tier 
				FROM tracer_session 
				WHERE id = $1 AND tenant_id = $2
			`, sessionIDValue, authInfo.TenantID)
			if err != nil {
				return nil, fmt.Errorf("failed to query tracer session: %w", err)
			}

			result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[TracerSessionWithoutVirtualFields])
			if err != nil {
				if errors.Is(err, pgx.ErrNoRows) {
					return nil, fmt.Errorf("%w: %s", ErrTracerSessionNotFound, sessionIDValue)
				}
				return nil, fmt.Errorf("failed to fetch tracer session: %w", err)
			}
			dbSession = *result
		} else {
			// Fetch session by name and tenant_id
			name := "default"
			if sessionNameValue != "" {
				name = sessionNameValue
			}

			rows, err := c.db.Query(ctx, `
				SELECT id, tenant_id, start_time, end_time, 
				       name, description, default_dataset_id, extra, reference_dataset_id, trace_tier 
				FROM tracer_session 
				WHERE name = $1 AND tenant_id = $2
			`, name, authInfo.TenantID)
			if err != nil {
				return nil, fmt.Errorf("failed to query tracer session: %w", err)
			}

			result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[TracerSessionWithoutVirtualFields])
			if err != nil {
				if errors.Is(err, pgx.ErrNoRows) {
					// Create a new tracer session
					tracerSessionCreate := TracerSessionCreate{
						TracerSessionBase: TracerSessionBase{
							ID:        uuid.New(),
							Name:      name,
							StartTime: startTime,
						},
					}
					createdSession, err := c.CreateTracerSession(ctx, authInfo, tracerSessionCreate, true)
					if err != nil {
						return nil, fmt.Errorf("failed to create tracer session: %w", err)
					}
					dbSession = *createdSession
				} else {
					return nil, fmt.Errorf("failed to fetch tracer session: %w", err)
				}
			} else {
				dbSession = *result
			}
		}

		return &dbSession, nil
	})

	if err != nil {
		return nil, err
	}

	// Prevent operation if session end time is before run start time
	if tracerSession.EndTime != nil && tracerSession.EndTime.Before(startTime) {
		return nil, fmt.Errorf("%w: end_time=%v start_time=%v",
			ErrSessionEndTimeBeforeRun,
			tracerSession.EndTime,
			startTime)
	}

	// Ensure that the session start time is <= the earliest run start time
	if tracerSession.StartTime.After(startTime) {
		oplog.Info("Updating tracer session start time", "from", tracerSession.StartTime, "to", startTime)

		_, err := c.db.Exec(ctx, `
			UPDATE tracer_session
			SET start_time = $1
			WHERE id = $2
		`, startTime, tracerSession.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to update tracer session start time: %w", err)
		}

		err = c.cache.Delete(ctx, cacheKey)
		if err != nil {
			oplog.Error("Failed to delete cache key", "key", cacheKey, "err", err)
		}

		tracerSession.StartTime = startTime
	}

	return tracerSession, nil
}
