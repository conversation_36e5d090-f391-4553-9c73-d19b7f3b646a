package tracer_sessions_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/tracer_sessions"
	tracer "langchain.com/smith/tracer_sessions"
	"langchain.com/smith/util"
)

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	_, err := dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	assert.NoError(t, err)
}

var (
	testDbPool    *database.AuditLoggedPool
	testRedisPool redis.UniversalClient
)

func TestMain(m *testing.M) {
	testDbPool = database.PgConnect()
	testRedisPool = lsredis.SingleRedisConnect()

	m.Run()

	testDbPool.Close()
	testRedisPool.Close()
}

func TestCreateTracerSession(t *testing.T) {
	defer DbCleanup(t, testDbPool)

	ctx := context.Background()

	client := tracer_sessions.NewTracerSessionsClient(testDbPool, testRedisPool)
	assert.NotNil(t, client)

	orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	traceTier := tracer.LongLived
	tracerSessionCreate := tracer.TracerSessionCreate{
		TracerSessionBase: tracer.TracerSessionBase{
			ID:          uuid.New(),
			StartTime:   time.Now().UTC(),
			Name:        "Test Session",
			Description: util.StringPtr("This is a test tracer session"),
			TraceTier:   &traceTier,
		},
	}

	tracerSessionRecord, err := client.CreateTracerSession(ctx, authInfo, tracerSessionCreate, false)
	assert.NoError(t, err)
	assert.NotNil(t, tracerSessionRecord)

	assert.Equal(t, tracerSessionCreate.ID.String(), tracerSessionRecord.ID.String())
	assert.Equal(t, authInfo.TenantID, tracerSessionRecord.TenantID.String())
	assert.Equal(t, tracerSessionCreate.Name, tracerSessionRecord.Name)
	assert.Equal(t, tracerSessionCreate.Description, tracerSessionRecord.Description)
	assert.WithinDuration(t, tracerSessionCreate.StartTime, tracerSessionRecord.StartTime, time.Second)

	if tracerSessionCreate.TraceTier == nil || tracerSessionRecord.TraceTier == nil {
		t.Errorf("Expected TraceTier %v, got %v", tracerSessionCreate.TraceTier, tracerSessionRecord.TraceTier)
	} else {
		assert.Equal(t, *tracerSessionCreate.TraceTier, *tracerSessionRecord.TraceTier)
	}
}

func TestStartOrFetchTracerSession(t *testing.T) {
	defer DbCleanup(t, testDbPool)

	ctx := context.Background()

	client := tracer_sessions.NewTracerSessionsClient(testDbPool, testRedisPool)
	assert.NotNil(t, client)

	orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	// Test creating a new session without sessionID or sessionName
	tracerSession, err := client.StartOrFetchTracerSession(ctx, authInfo, nil, nil, nil)
	if err != nil {
		t.Fatalf("Failed to start or fetch tracer session: %v", err)
	}
	assert.NotNil(t, tracerSession)
	assert.Equal(t, "default", tracerSession.Name)
	assert.Equal(t, authInfo.TenantID, tracerSession.TenantID.String())

	// Test fetching the same session (should use cache)
	tracerSessionCached, err := client.StartOrFetchTracerSession(ctx, authInfo, nil, nil, nil)
	assert.NoError(t, err)
	assert.Equal(t, tracerSession.ID, tracerSessionCached.ID)

	// Test creating a session with a specific name
	sessionName := "Test Session"
	tracerSession2, err := client.StartOrFetchTracerSession(ctx, authInfo, nil, &sessionName, nil)
	assert.NoError(t, err)
	assert.NotNil(t, tracerSession2)
	assert.Equal(t, sessionName, tracerSession2.Name)

	// Test fetching the session by name
	tracerSession2Fetched, err := client.StartOrFetchTracerSession(ctx, authInfo, nil, &sessionName, nil)
	assert.NoError(t, err)
	assert.Equal(t, tracerSession2.ID, tracerSession2Fetched.ID)

	// Test fetching by sessionID
	sessionID := tracerSession2.ID.String()
	tracerSessionByID, err := client.StartOrFetchTracerSession(ctx, authInfo, &sessionID, nil, nil)
	assert.NoError(t, err)
	assert.Equal(t, tracerSession2.ID, tracerSessionByID.ID)

	// Test with startTimeStr
	startTimeStr := "2023-05-01T12:00:00Z"
	tracerSession3, err := client.StartOrFetchTracerSession(ctx, authInfo, nil, &sessionName, &startTimeStr)
	assert.NoError(t, err)
	assert.Equal(t, tracerSession2.ID, tracerSession3.ID)

	// Test error when end_time is before start_time
	endTime := time.Date(2023, 4, 30, 12, 0, 0, 0, time.UTC) // April 30, 2023
	_, err = testDbPool.Exec(ctx, `UPDATE tracer_session SET end_time = $1 WHERE id = $2`, endTime, tracerSession2.ID)
	assert.NoError(t, err)

	cacheKey := fmt.Sprintf("tracer_session:%s:%s:%s", authInfo.TenantID, "", sessionName)
	err = client.InvalidateCache(ctx, cacheKey)
	assert.NoError(t, err)

	// Test that we get an error when trying to fetch a session with an invalid end time
	_, err = client.StartOrFetchTracerSession(ctx, authInfo, nil, &sessionName, &startTimeStr)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "session end time")
}
