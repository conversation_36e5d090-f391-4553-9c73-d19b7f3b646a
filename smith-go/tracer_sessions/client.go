package tracer_sessions

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

var (
	ErrClientNotInitialized = errors.New("tracer sessions client not initialized")
)

type TracerSessionsClient struct {
	db    *database.AuditLoggedPool
	cache *lsredis.Cache[string, *TracerSessionWithoutVirtualFields]
}

var (
	tracerSessionsClientInstance *TracerSessionsClient
	tracerSessionsClientOnce     sync.Once
)

func NewTracerSessionsClient(db *database.AuditLoggedPool, cachingRedisClient redis.UniversalClient) *TracerSessionsClient {
	tracerSessionsClientOnce.Do(func() {
		tracerSessionsClientInstance = &TracerSessionsClient{
			db:    db,
			cache: lsredis.NewCache[string, *TracerSessionWithoutVirtualFields](cachingRedisClient, "tracer_session", 60*time.Second),
		}
	})
	return tracerSessionsClientInstance
}

func GetTracerSessionsClient() (*TracerSessionsClient, error) {
	if tracerSessionsClientInstance == nil {
		return nil, ErrClientNotInitialized
	}
	return tracerSessionsClientInstance, nil
}

func (c *TracerSessionsClient) InvalidateCache(ctx context.Context, key string) error {
	if c == nil {
		return ErrClientNotInitialized
	}
	return c.cache.Delete(ctx, key)
}
