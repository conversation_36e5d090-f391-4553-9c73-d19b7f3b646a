package storage_test

import (
	"bytes"
	"context"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"
	"langchain.com/smith/config"
	"langchain.com/smith/storage"

	"langchain.com/smith/testutil/leak"
)

func runStorageClientTests(t *testing.T, client storage.StorageClient, expectedStrategy storage.UploadStrategy) {
	ctx := context.Background()
	key := "test-key"

	data := []byte("test data")
	dataSize := len(data)
	reader := bytes.NewReader(data)

	uploadInput := &storage.UploadObjectInput{
		Bucket:        config.Env.S3BucketName,
		Key:           key,
		Reader:        reader,
		ContentType:   "text/plain",
		ContentLength: int64(dataSize),
	}

	// test sync
	strategy, err := client.UploadObject(ctx, uploadInput)
	assert.NoError(t, err)
	assert.Equal(t, expectedStrategy, strategy)

	// verify content
	getInput := &storage.GetObjectInput{
		Bucket: uploadInput.Bucket,
		Key:    key,
	}
	output, err := client.GetObject(ctx, getInput)
	assert.NoError(t, err)
	assert.NotNil(t, output)

	downloadedData := new(bytes.Buffer)
	_, err = io.Copy(downloadedData, output.Body)
	assert.NoError(t, err)
	assert.Equal(t, dataSize, downloadedData.Len())

	// reset the reader for the next test
	_, err = reader.Seek(0, 0)
	assert.NoError(t, err)

	// test async upload
	asyncResult, err := client.UploadObjectAsync(ctx, uploadInput)
	assert.NoError(t, err)
	assert.NotNil(t, asyncResult)
	assert.Equal(t, expectedStrategy, asyncResult.Strategy)

	err = asyncResult.Wait()
	assert.NoError(t, err)

	// verify content
	output, err = client.GetObject(ctx, getInput)
	assert.NoError(t, err)
	assert.NotNil(t, output)

	downloadedData.Reset()
	_, err = io.Copy(downloadedData, output.Body)
	assert.NoError(t, err)
	assert.Equal(t, dataSize, downloadedData.Len())

	// verify partial content
	partialInput := &storage.GetObjectInput{
		Bucket: uploadInput.Bucket,
		Key:    key,
		Range:  &storage.Range{Start: 0, End: 10},
	}
	output, err = client.GetObject(ctx, partialInput)

	if _, ok := client.(*storage.AzureStorageClient); ok {
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "batched blob storage not supported for Azure Blob Storage")
	} else {
		assert.NoError(t, err)
		assert.NotNil(t, output)

		downloadedData.Reset()
		_, err = io.Copy(downloadedData, output.Body)

		assert.NoError(t, err)
		assert.Equal(t, 9, downloadedData.Len())
	}
}

func TestStorageClient_UploadObject(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// spool to disk when size is greater than minimum and less than cumulative limit
	diskOpts := &storage.BlobStorageClientOptions{
		SpoolMinSizeBytes: 2,
		SpoolLimitBytes:   1024 * 1024 * 1024,
	}

	// spool to memory when size is less than minimum and the cumulative limit
	memOpts := &storage.BlobStorageClientOptions{
		SpoolMinSizeBytes: 1024 * 1024 * 1024,
		SpoolLimitBytes:   1024 * 1024 * 1024,
	}

	// upload directly when size is greater than the cumulative limit, even if it's greater than minimum
	directOpts := &storage.BlobStorageClientOptions{
		SpoolMinSizeBytes: 2,
		SpoolLimitBytes:   2,
	}

	tests := []struct {
		name     string
		opts     *storage.BlobStorageClientOptions
		expected storage.UploadStrategy
	}{
		{
			name:     "from disk",
			opts:     diskOpts,
			expected: storage.UploadStrategyDisk,
		},
		{
			name:     "from memory",
			opts:     memOpts,
			expected: storage.UploadStrategyMemory,
		},
		{
			name:     "direct",
			opts:     directOpts,
			expected: storage.UploadStrategyDirect,
		},
	}

	for _, test := range tests {
		t.Run("S3_"+test.name, func(t *testing.T) {
			s3Client, err := storage.NewS3StorageClient(false, test.opts)
			assert.NoError(t, err)
			assert.NotNil(t, s3Client)

			runStorageClientTests(t, s3Client, test.expected)
		})

		t.Run("Azure_"+test.name, func(t *testing.T) {
			azureClient, err := storage.NewAzureStorageClient(false, test.opts)
			assert.NoError(t, err)
			assert.NotNil(t, azureClient)

			runStorageClientTests(t, azureClient, test.expected)
		})
	}
}
