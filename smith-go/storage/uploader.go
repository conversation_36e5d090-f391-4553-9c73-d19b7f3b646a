package storage

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"sort"
	"sync"

	"github.com/DataDog/zstd"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"langchain.com/smith/config"
)

// S3API defines the minimal S3 client interface required for uploads.
type S3API interface {
	CreateMultipartUpload(ctx context.Context, params *s3.CreateMultipartUploadInput, optFns ...func(*s3.Options)) (*s3.CreateMultipartUploadOutput, error)
	UploadPart(ctx context.Context, params *s3.UploadPartInput, optFns ...func(*s3.Options)) (*s3.UploadPartOutput, error)
	CompleteMultipartUpload(ctx context.Context, params *s3.CompleteMultipartUploadInput, optFns ...func(*s3.Options)) (*s3.CompleteMultipartUploadOutput, error)
	PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
	AbortMultipartUpload(ctx context.Context, params *s3.AbortMultipartUploadInput, optFns ...func(*s3.Options)) (*s3.AbortMultipartUploadOutput, error)
}

// ContinuousUploader uses a producer/consumer model to upload data to S3.
//
// Treat ContinuousUploader as a short-lived object for one upload. Call Complete()
// at the end to free resources and finalize or abort the upload.
// Then, if you need to do another upload, create a new ContinuousUploader instance.
//
// Additionally, it is strongly recommended to defer Close() after using the uploader.
// It is not safe to use across multiple goroutines.
type ContinuousUploader struct {
	client          S3API  // AWS S3 client for API calls
	bucket          string // S3 bucket name
	key             string // S3 object key (path/name in bucket)
	partSize        int64  // multipart chunk size (bytes), min 5 MiB
	concurrency     int    // number of worker goroutines
	spoolLimitBytes int64  // maximum bytes allowed for disk spooling

	ctx    context.Context    // context for cancellation
	cancel context.CancelFunc // cancel function for worker goroutines
	tasks  chan uploadTask
	wg     sync.WaitGroup

	tempFile    *os.File // file for spooling data before upload
	uploadedOfs int64    // bytes of tempFile already scheduled for upload
	partNum     int32    // next part number to assign (1-based)

	errMu     sync.Mutex // protects uploadErr
	uploadErr error      // first error encountered (if any)

	partsMu        sync.Mutex
	completedParts []types.CompletedPart

	uploadID    string // S3 upload ID for multipart
	isMultipart bool   // whether we've initiated multipart

	state uploadState // current state of the upload
}

// uploadTask represents a chunk of data to upload as a part.
type uploadTask struct {
	partNumber int32
	reader     io.ReadSeeker // seekable reader for the chunk (Section of tempFile)
	size       int64         // size of the chunk
}

type Strategy string

const (
	UploadStrategySinglePart Strategy = "single"
	UploadStrategyMultipart  Strategy = "multipart"
)

type uploadState int

const (
	stateNotStarted uploadState = iota
	stateInProgress
	stateCompleted
)

const (
	zstdContentEncoding = "zstd"
)

type CompletionResult struct {
	NumParts       int
	UploadStrategy Strategy
}

// NewContinuousUploader creates a ContinuousUploader with the specified S3 client and bucket.
func NewContinuousUploader(ctx context.Context, client S3API, bucket string, opts ...Option) (*ContinuousUploader, error) {
	// Default configuration
	const defaultPartSize = 5 * 1024 * 1024 // 5 MiB
	const defaultConcurrency = 5

	cu := &ContinuousUploader{
		client:          client,
		bucket:          bucket,
		partSize:        defaultPartSize,
		concurrency:     defaultConcurrency,
		partNum:         1,
		state:           stateNotStarted,
		spoolLimitBytes: config.Env.SpoolLimitGB * 1024 * 1024 * 1024, // Default to config value
	}

	for _, opt := range opts {
		if err := opt(cu); err != nil {
			return nil, err
		}
	}

	// Create a temporary file for spooling data
	tmpFile, err := os.CreateTemp("", "s3-upload-*.tmp")
	if err != nil {
		return nil, fmt.Errorf("create temp file: %w", err)
	}
	cu.tempFile = tmpFile

	// context we can cancel upon fatal errors
	cu.ctx, cu.cancel = context.WithCancel(ctx)

	// Start worker goroutines
	cu.tasks = make(chan uploadTask, cu.concurrency)
	for i := 0; i < cu.concurrency; i++ {
		cu.wg.Add(1)
		go cu.worker()
	}
	return cu, nil
}

// canSpoolToDisk checks if the requested size can be accommodated within the spool limit.
func (cu *ContinuousUploader) canSpoolToDisk(ctx context.Context, size int64) bool {
	return CanSpoolToDisk(ctx, size, cu.spoolLimitBytes)
}

// Option is a functional option for configuring ContinuousUploader.
type Option func(*ContinuousUploader) error

// WithPartSize sets a custom part size (in bytes) for multipart uploads.
func WithPartSize(size int64) Option {
	return func(cu *ContinuousUploader) error {
		// S3 multipart parts must be at least 5 MiB
		if size < 5*1024*1024 {
			return fmt.Errorf("part size must be at least 5 MiB")
		}
		cu.partSize = size
		return nil
	}
}

// WithConcurrency sets a custom number of parallel upload workers.
func WithConcurrency(n int) Option {
	return func(cu *ContinuousUploader) error {
		if n < 1 {
			return fmt.Errorf("concurrency must be at least 1")
		}
		cu.concurrency = n
		return nil
	}
}

// WithSpoolLimitBytes sets a custom spool limit for disk usage.
func WithSpoolLimitBytes(limit int64) Option {
	return func(cu *ContinuousUploader) error {
		if limit < 1 {
			return fmt.Errorf("spool limit must be positive")
		}
		cu.spoolLimitBytes = limit
		return nil
	}
}

// StartUpload initializes the upload for a given S3 key (object name).
// It records the key and prepares for data intake. Actual S3 multipart initiation is deferred until needed.
func (cu *ContinuousUploader) StartUpload(key string) error {
	cu.key = key
	// No immediate S3 call; multipart upload will be created when we have data to send.

	if cu.state != stateNotStarted {
		return fmt.Errorf("cannot start a new upload; current state is %v", cu.state)
	}

	cu.key = key
	cu.state = stateInProgress
	return nil
}

// UploadReader spools data from the provided reader. When at least partSize bytes
// are buffered, it dispatches that chunk to be uploaded by a worker.
// Returns the start/end file offsets that correspond to the uploaded data.
func (cu *ContinuousUploader) UploadReader(r io.Reader) (int64, int64, error) {
	if cu.state != stateInProgress {
		return 0, 0, fmt.Errorf("cannot upload data; current state is %v", cu.state)
	}

	if err := cu.getError(); err != nil {
		// already encountered a fatal error during processing, bail
		return 0, 0, err
	}

	startOfs, _ := cu.tempFile.Seek(0, io.SeekEnd)

	zw := zstd.NewWriter(cu.tempFile)

	// Write directly from reader to compressor
	_, err := io.Copy(zw, r)
	if err != nil {
		cu.recordError(fmt.Errorf("compress/write temp file: %w", err))
		cu.cancel()
		return 0, 0, cu.getError()
	}

	// flush compressor
	if err := zw.Close(); err != nil {
		cu.recordError(fmt.Errorf("flush compressor: %w", err))
		cu.cancel()
		return 0, 0, cu.getError()
	}

	// Check if we have enough data for a part
	fileSize, statErr := cu.tempFile.Stat()
	if statErr != nil {
		cu.recordError(fmt.Errorf("stat temp file: %w", statErr))
		cu.cancel()
		return 0, 0, cu.getError()
	}

	// Queue parts for upload
	for (fileSize.Size() - cu.uploadedOfs) >= cu.partSize {
		// Initiate multipart if needed
		if !cu.isMultipart {
			if err := cu.initiateMultipart(); err != nil {
				cu.recordError(err)
				cu.cancel()
				return 0, 0, cu.getError()
			}
			cu.isMultipart = true
		}

		partOffset := cu.uploadedOfs
		partNum := cu.partNum
		cu.partNum++
		cu.uploadedOfs += cu.partSize

		// Create a SectionReader for that chunk
		partReader := io.NewSectionReader(cu.tempFile, partOffset, cu.partSize)
		task := uploadTask{
			partNumber: partNum,
			reader:     partReader,
			size:       cu.partSize,
		}
		// Dispatch to workers
		select {
		case <-cu.ctx.Done():
			return 0, 0, cu.getError()
		case cu.tasks <- task:
		}
	}

	return startOfs, fileSize.Size(), cu.getError()
}

// Complete finishes the upload process. It sends any remaining data as the final part (if multipart),
// or uses PutObject if the total data was under partSize. It waits for all parts to finish uploading,
// then completes the multipart upload transaction on S3. It aborts the upload if any error occurred.
// Returns information about the uploaded parts.
func (cu *ContinuousUploader) Complete() (*CompletionResult, error) {
	if cu.state != stateInProgress {
		return nil, fmt.Errorf("cannot complete upload; current state is %v", cu.state)
	}

	// Ensure we always do final cleanup, including aborting incomplete uploads or removing temp file.
	// cleanup also sets state to stateCompleted.
	defer cu.cleanup()

	// If there's already an error, return it now
	if err := cu.getError(); err != nil {
		return nil, err
	}

	// Check total size
	fileInfo, statErr := cu.tempFile.Stat()
	if statErr != nil {
		return nil, fmt.Errorf("stat temp file: %w", statErr)
	}
	totalSize := fileInfo.Size()

	// Case 1: No multipart upload => data is small => do single PutObject
	if !cu.isMultipart {
		// stop the workers
		close(cu.tasks)
		cu.wg.Wait()

		// Seek to start
		if _, err := cu.tempFile.Seek(0, io.SeekStart); err != nil {
			return nil, fmt.Errorf("seek temp file: %w", err)
		}
		enc := zstdContentEncoding
		_, err := cu.client.PutObject(cu.ctx, &s3.PutObjectInput{
			Bucket:          &cu.bucket,
			Key:             &cu.key,
			Body:            cu.tempFile,
			ContentEncoding: &enc,
		})
		if err != nil {
			cu.recordError(fmt.Errorf("put object: %w", err))
			cu.cancel()
			return nil, cu.getError()
		}
		// Single-part done
		cu.state = stateCompleted
		return &CompletionResult{
			NumParts:       1,
			UploadStrategy: UploadStrategySinglePart,
		}, nil
	}

	// Case 2: Multipart was used. Queue final (remaining) bytes as one last part, if any.
	remaining := totalSize - cu.uploadedOfs
	if remaining > 0 {
		partOffset := cu.uploadedOfs
		partNum := cu.partNum
		cu.partNum++
		cu.uploadedOfs += remaining

		partReader := io.NewSectionReader(cu.tempFile, partOffset, remaining)
		task := uploadTask{partNumber: partNum, reader: partReader, size: remaining}
		select {
		case <-cu.ctx.Done():
			// Some error occurred in the interim
			return nil, cu.getError()
		case cu.tasks <- task:
		}
	}

	// Signal no more parts, wait for workers
	close(cu.tasks)
	cu.wg.Wait()

	if err := cu.getError(); err != nil {
		return nil, err
	}

	// Sort parts by part number before completion
	sort.Slice(cu.completedParts, func(i, j int) bool {
		return *cu.completedParts[i].PartNumber < *cu.completedParts[j].PartNumber
	})

	// Complete the multipart upload
	_, err := cu.client.CompleteMultipartUpload(cu.ctx, &s3.CompleteMultipartUploadInput{
		Bucket:   &cu.bucket,
		Key:      &cu.key,
		UploadId: &cu.uploadID,
		MultipartUpload: &types.CompletedMultipartUpload{
			Parts: cu.completedParts,
		},
	})
	if err != nil {
		cu.recordError(fmt.Errorf("complete multipart: %w", err))
		cu.cancel()
		return nil, cu.getError()
	}

	cu.state = stateCompleted
	return &CompletionResult{
		NumParts:       len(cu.completedParts),
		UploadStrategy: UploadStrategyMultipart,
	}, nil
}

func (cu *ContinuousUploader) worker() {
	defer cu.wg.Done()
	for {
		// stop if an error has already been recorded.
		if err := cu.getError(); err != nil {
			return
		}

		select {
		case <-cu.ctx.Done():
			// context was canceled or deadline exceeded
			return

		case task, ok := <-cu.tasks:
			if !ok {
				// the tasks channel has been closed, meaning no more work
				return
			}

			// we have a valid task; process it
			partNum := task.partNumber
			resp, err := cu.client.UploadPart(cu.ctx, &s3.UploadPartInput{
				Bucket:        &cu.bucket,
				Key:           &cu.key,
				UploadId:      &cu.uploadID,
				PartNumber:    &partNum,
				ContentLength: &task.size,
				Body:          task.reader,
			})
			if err != nil {
				// The SDK has already retried if it was retryable. This is final.
				cu.recordError(fmt.Errorf("upload part %d: %w", partNum, err))
				cu.cancel() // signal other workers to stop
				return
			}

			// Record part's ETag
			etagVal := ""
			if resp.ETag != nil {
				etagVal = *resp.ETag
			}
			cp := types.CompletedPart{
				ETag:       &etagVal,
				PartNumber: &partNum,
			}
			cu.partsMu.Lock()
			cu.completedParts = append(cu.completedParts, cp)
			cu.partsMu.Unlock()
		}
	}
}

// initiateMultipart calls S3 to start a multipart upload and stores the upload ID.
func (cu *ContinuousUploader) initiateMultipart() error {
	resp, err := cu.client.CreateMultipartUpload(cu.ctx, &s3.CreateMultipartUploadInput{
		Bucket:          &cu.bucket,
		Key:             &cu.key,
		ContentEncoding: aws.String(zstdContentEncoding),
	})
	if err != nil {
		return fmt.Errorf("init multipart: %w", err)
	}
	if resp.UploadId == nil {
		return fmt.Errorf("init multipart: no upload ID received")
	}
	cu.uploadID = *resp.UploadId
	return nil
}

// recordError sets the first error encountered.
func (cu *ContinuousUploader) recordError(err error) {
	cu.errMu.Lock()
	defer cu.errMu.Unlock()
	if cu.uploadErr == nil {
		cu.uploadErr = err
	}
}

// getError retrieves any recorded error.
func (cu *ContinuousUploader) getError() error {
	cu.errMu.Lock()
	defer cu.errMu.Unlock()
	return cu.uploadErr
}

// cleanup closes and removes the temporary file, and aborts if we have an unfinished multipart upload.
func (cu *ContinuousUploader) cleanup() {
	// If we used multipart and haven't completed, attempt an abort (best effort).
	if cu.isMultipart && cu.uploadID != "" && cu.state != stateCompleted {
		_, _ = cu.client.AbortMultipartUpload(context.Background(), &s3.AbortMultipartUploadInput{
			Bucket:   &cu.bucket,
			Key:      &cu.key,
			UploadId: &cu.uploadID,
		})
	}

	if cu.tempFile != nil {
		fileInfo, err := cu.tempFile.Stat()
		var fileSize int64
		if err == nil {
			fileSize = fileInfo.Size()
			config.LogAndContextSetField(cu.ctx, "tmp_file_drained_size", slog.Int64Value(fileSize))
		}

		_ = cu.tempFile.Close()
		_ = os.Remove(cu.tempFile.Name())
		cu.tempFile = nil

		if fileSize > 0 {
			diskBytesUsed.Add(-fileSize)
		}
	}

	cu.state = stateCompleted

	// Cancel context to free resources
	if cu.cancel != nil {
		cu.cancel()
	}

	// Wait for all workers to finish
	cu.wg.Wait()
}

// Close aborts any pending multipart upload (if present)
// and cleans up the temporary file. Safe to call multiple times.
// It is highly recommended to defer this after creating a ContinuousUploader
// to ensure resources are freed.
func (cu *ContinuousUploader) Close() {
	cu.cleanup()
}
