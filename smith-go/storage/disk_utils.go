package storage

import (
	"context"
	"sync/atomic"

	"langchain.com/smith/config"
)

// diskBytesUsed is a shared atomic counter tracking the total bytes used for disk spooling
// across all storage components.
var diskBytesUsed = atomic.Int64{}

// CanSpoolToDisk checks if the requested size can be accommodated within the spool limit.
// If successful, it increments the diskBytesUsed counter.
func CanSpoolToDisk(ctx context.Context, size int64, spoolLimitBytes int64) bool {
	next := diskBytesUsed.Add(size)
	if next > spoolLimitBytes {
		diskBytesUsed.Add(-size)
		return false
	}
	return true
}

// GetDefaultSpoolLimitBytes returns the default spool limit from config.
func GetDefaultSpoolLimitBytes() int64 {
	return config.Env.SpoolLimitGB * 1024 * 1024 * 1024
}
