package storage

import (
	"context"
	"errors"
	"fmt"
	"io"

	"net/http"
	"net/url"

	"github.com/go-chi/httplog/v2"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/blockblob"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob/container"
	"langchain.com/smith/config"
)

type AzureStorageClient struct {
	ContainerClient *container.Client
	baseStorage
}

func NewAzureStorageClient(keepAlive bool, opts *BlobStorageClientOptions) (*AzureStorageClient, error) {
	containerClient, err := AzureBlobClient(keepAlive)
	if err != nil {
		return nil, err
	}
	return &AzureStorageClient{
		ContainerClient: containerClient,
		baseStorage:     *NewBaseStorage(opts),
	}, nil
}

// https://learn.microsoft.com/en-us/azure/developer/go/azure-sdk-core-concepts#custom-http-transport
type KeepAliveTransport struct {
	transport http.RoundTripper
}

func (m *KeepAliveTransport) Do(req *http.Request) (*http.Response, error) {
	req.Close = true
	return m.transport.RoundTrip(req)
}

func AzureBlobClient(keepAlive bool) (*container.Client, error) {
	// set keepAlive to false for testing to avoid goroutine leaks
	if config.Env.AzureStorageContainerName == "" {
		return nil, errors.New("azure container name is not set")
	}

	// Allow overriding the service URL for non-standard cases.
	var serviceURL = fmt.Sprintf("https://%s.blob.core.windows.net/", config.Env.AzureStorageAccountName)
	if config.Env.AzureStorageServiceUrlOverride != "" {
		serviceURL = config.Env.AzureStorageServiceUrlOverride
	}

	var containerUrl string
	containerUrl, err := url.JoinPath(serviceURL, config.Env.AzureStorageContainerName)
	if err != nil {
		return nil, err
	}

	clientOptions := &container.ClientOptions{}
	if !keepAlive {
		m := &KeepAliveTransport{
			transport: http.DefaultTransport,
		}
		clientOptions.Transport = m
	}

	switch {
	case config.Env.AzureStorageAccountName != "" && config.Env.AzureStorageAccountKey != "":
		cred, err := azblob.NewSharedKeyCredential(config.Env.AzureStorageAccountName, config.Env.AzureStorageAccountKey)
		if err != nil {
			return nil, err
		}
		return container.NewClientWithSharedKeyCredential(containerUrl, cred, clientOptions)
	case config.Env.AzureStorageSASToken != "":
		containerUrl += "?" + config.Env.AzureStorageSASToken
		return container.NewClientWithNoCredential(containerUrl, clientOptions)
	case config.Env.AzureStorageConnectionString != "":
		return container.NewClientFromConnectionString(config.Env.AzureStorageConnectionString, config.Env.AzureStorageContainerName, clientOptions)
	default:
		cred, err := azidentity.NewDefaultAzureCredential(nil)
		if err != nil {
			return nil, fmt.Errorf("failed azidentity.NewDefaultAzureCredential: %v", err)
		}
		return container.NewClient(containerUrl, cred, clientOptions)
	}
}

func (c *AzureStorageClient) HealthCheck(ctx context.Context, input *HealthCheckInput) error {
	_, err := c.ContainerClient.GetAccountInfo(ctx, nil)
	return err
}

func (c *AzureStorageClient) CopyObject(ctx context.Context, input *CopyObjectInput) error {
	c.baseStorage.failIfDisabled()
	sourceClient := c.ContainerClient.NewBlockBlobClient(input.SourceKey)
	destClient := c.ContainerClient.NewBlockBlobClient(input.DestKey)

	_, err := destClient.CopyFromURL(ctx, sourceClient.URL(), nil)
	return err
}

func (c *AzureStorageClient) GetObject(ctx context.Context, input *GetObjectInput) (*GetObjectOutput, error) {
	c.baseStorage.failIfDisabled()
	if input.Range != nil {
		return nil, errors.New("batched blob storage not supported for Azure Blob Storage")
	}
	blobClient := c.ContainerClient.NewBlockBlobClient(input.Key)

	response, err := blobClient.DownloadStream(ctx, nil)
	if err != nil {
		var azureErr *azcore.ResponseError
		if errors.As(err, &azureErr) {
			if azureErr.StatusCode == 404 {
				return nil, ErrNotFound
			}
		}
		return nil, err
	}

	props := response.DownloadResponse
	return &GetObjectOutput{
		Body:            response.Body,
		ContentType:     props.ContentType,
		ContentEncoding: props.ContentEncoding,
		ContentLength:   props.ContentLength,
		LastModified:    props.LastModified,
	}, nil
}

func (c *AzureStorageClient) HeadObject(ctx context.Context, input *HeadObjectInput) (*HeadObjectOutput, error) {
	c.baseStorage.failIfDisabled()
	blobClient := c.ContainerClient.NewBlockBlobClient(input.Key)

	props, err := blobClient.GetProperties(ctx, nil)
	if err != nil {
		var azureErr *azcore.ResponseError
		if errors.As(err, &azureErr) {
			if azureErr.StatusCode == 404 {
				return nil, ErrNotFound
			}
		}
		return nil, err
	}

	return &HeadObjectOutput{
		ContentType:     props.ContentType,
		ContentEncoding: props.ContentEncoding,
		ContentLength:   props.ContentLength,
		LastModified:    props.LastModified,
	}, nil
}

func (c *AzureStorageClient) doUpload(ctx context.Context, input *UploadObjectInput, reader io.Reader) error {
	blobClient := c.ContainerClient.NewBlockBlobClient(input.Key)
	opts := &blockblob.UploadStreamOptions{
		HTTPHeaders: &blob.HTTPHeaders{
			BlobContentType:     &input.ContentType,
			BlobContentEncoding: &input.ContentEncoding,
		},
	}

	_, err := blobClient.UploadStream(ctx, reader, opts)
	return err
}

func (c *AzureStorageClient) UploadObject(ctx context.Context, input *UploadObjectInput) (UploadStrategy, error) {
	c.baseStorage.failIfDisabled()
	oplog := httplog.LogEntry(ctx)
	reader, cleanup, strategy, err := c.baseStorage.prepareReader(ctx, input.Reader, input.ContentLength, oplog)
	defer cleanup()
	if err != nil {
		return strategy, err
	}

	err = c.doUpload(ctx, input, reader)
	return strategy, err
}

func (c *AzureStorageClient) UploadObjectAsync(ctx context.Context, input *UploadObjectInput) (*UploadAsyncResult, error) {
	c.baseStorage.failIfDisabled()
	oplog := httplog.LogEntry(ctx)
	reader, cleanup, strategy, err := c.baseStorage.prepareReader(ctx, input.Reader, input.ContentLength, oplog)
	if err != nil {
		return nil, err
	}

	if strategy == UploadStrategyDirect {
		// upload directly without offloading to a goroutine
		// this is to avoid data races when the reader is closed before the upload completes
		defer cleanup()
		err := c.doUpload(ctx, input, reader)
		errChan := make(chan error, 1)
		errChan <- err
		return &UploadAsyncResult{
			Strategy: strategy,
			ErrChan:  errChan,
		}, nil
	}

	errChan := make(chan error, 1)
	go func() {
		defer cleanup()
		errChan <- c.doUpload(ctx, input, reader)
	}()

	return &UploadAsyncResult{
		Strategy: strategy,
		ErrChan:  errChan,
	}, nil
}
