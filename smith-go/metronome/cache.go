package metronome

import (
	"context"
	"fmt"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/go-redsync/redsync/v4"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/payment"
	"langchain.com/smith/queue"
	lsredis "langchain.com/smith/redis"
)

type MetronomeCacheRow struct {
	ID              uuid.UUID              `db:"id"`
	OrganizationID  uuid.UUID              `db:"organization_id"`
	CustomerID      *uuid.UUID             `db:"customer_id"`
	PlanID          *uuid.UUID             `db:"plan_id"`
	CustomerDetails map[string]interface{} `db:"customer_details"`
	PlanDetails     map[string]interface{} `db:"plan_details"`
	CreatedAt       time.Time              `db:"created_at"`
	UpdatedAt       time.Time              `db:"updated_at"`
}

func GetMetronomeCache(ctx context.Context, db *database.AuditLoggedPool, queueuingRedisClient redis.UniversalClient, orgID uuid.UUID, skipUpdate bool) (*MetronomeCacheRow, error) {
	oplog := httplog.LogEntry(ctx)

	query := `
        SELECT id, organization_id, customer_id, plan_id, customer_details, plan_details, created_at, updated_at
        FROM org_metronome_cache
        WHERE organization_id = $1
    `

	rows, _ := db.Query(ctx, query, orgID)
	cachedRow, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[MetronomeCacheRow])
	if err != nil {
		if err == pgx.ErrNoRows {
			cachedRow = &MetronomeCacheRow{}
		} else {
			return nil, fmt.Errorf("error scanning cache row: %w", err)
		}
	}

	now := time.Now().UTC()
	lastUpdate := cachedRow.UpdatedAt
	if lastUpdate.IsZero() {
		lastUpdate = now.AddDate(0, 0, -100)
	}

	expirySec := config.Env.OrgMetronomeCacheExpirySec
	if !skipUpdate && now.Sub(lastUpdate).Seconds() > float64(expirySec) {
		if err := enqueueMetronomeCacheUpdate(ctx, queueuingRedisClient, orgID.String()); err != nil {
			oplog.Error("error enqueuing metronome cache update", "err", err)
		}
	}

	return cachedRow, nil
}

func UpdateMetronomeCache(ctx context.Context, db *database.AuditLoggedPool, queueingRedisClient redis.UniversalClient, organizationIDAsStr string, force bool) (*MetronomeCacheRow, error) {
	oplog := httplog.LogEntry(ctx)

	orgID, err := uuid.Parse(organizationIDAsStr)
	if err != nil {
		return nil, fmt.Errorf("invalid organization ID: %w", err)
	}

	org, err := payment.GetOrganization(ctx, db, orgID)
	if err != nil {
		return nil, fmt.Errorf("error getting organization: %w", err)
	}

	var mutex *redsync.Mutex
	if !force {
		lockKey := fmt.Sprintf("set_metronome_cache_for_org:%s", orgID.String())
		mutex = lsredis.LockManager.NewMutex(lockKey,
			redsync.WithExpiry(5*time.Second),
			redsync.WithTries(3),
			redsync.WithRetryDelay(100*time.Millisecond),
		)

		if err := mutex.Lock(); err != nil {
			oplog.Debug("failed to acquire lock, returning cached version", "orgID", orgID)
			cachedRow, err := GetMetronomeCache(ctx, db, queueingRedisClient, orgID, true)
			if err != nil {
				return nil, fmt.Errorf("error getting cached data after lock failure: %w", err)
			}
			return cachedRow, nil
		}
		defer func() {
			if ok, err := mutex.Unlock(); !ok || err != nil {
				oplog.Error("failed to release lock", "err", err, "orgID", orgID)
			}
		}()
	}

	var customerDict map[string]interface{}
	var planID *uuid.UUID
	var planDict map[string]interface{}
	var potentialTransientError bool
	var err2 error

	if org.MetronomeCustomerID != nil && *org.MetronomeCustomerID != "" {
		metronomeClient := GetMetronomeClient()
		if metronomeClient == nil {
			return nil, fmt.Errorf("unable to get metronome client")
		}
		relevantPlansForCustomer, err1 := GetRelevantPlansForCustomer(ctx, metronomeClient, *org.MetronomeCustomerID)
		customerDict, err2 = metronomeClient.client.Get(ctx, fmt.Sprintf("/customers/%s", *org.MetronomeCustomerID), nil)

		if err1 != nil || err2 != nil {
			potentialTransientError = true
			oplog.Error("error fetching customer data", "err1", err1, "err2", err2)
		} else {
			customerPlan := relevantPlansForCustomer.CurrentPlan
			if customerPlan != nil && customerPlan.PlanID != "" {
				parsedPlanID, err := uuid.Parse(customerPlan.PlanID)
				if err != nil {
					potentialTransientError = true
					oplog.Error("error parsing plan ID", "err", err)
				} else {
					planID = &parsedPlanID
					planDict, err = metronomeClient.GetPlanDetails(ctx, customerPlan.PlanID)
					if err != nil {
						potentialTransientError = true
						oplog.Error("error getting plan details", "err", err)
					}
				}
			}
		}
	}

	if potentialTransientError {
		if force {
			return nil, fmt.Errorf("internal server error during forced update")
		} else {
			return nil, nil
		}
	}

	query := `
        INSERT INTO org_metronome_cache
            (id, organization_id, customer_id, plan_id, customer_details, plan_details)
        VALUES
            ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (organization_id)
        DO UPDATE SET
            customer_id = $3,
            plan_id = $4,
            customer_details = $5,
            plan_details = $6,
            updated_at = now()
        RETURNING id, organization_id, customer_id, plan_id, customer_details, plan_details, created_at, updated_at
    `
	newID := uuid.New()
	rows, _ := db.Query(ctx, query, newID, orgID, org.MetronomeCustomerID, planID, customerDict, planDict)
	newRow, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[MetronomeCacheRow])
	if err != nil {
		return nil, fmt.Errorf("error updating cache row: %w", err)
	}

	return newRow, nil
}

func ForceGetMetronomeCache(ctx context.Context, db *database.AuditLoggedPool, queueingRedisClient redis.UniversalClient, organizationID uuid.UUID) (*MetronomeCacheRow, error) {
	cachedMetInfo, err := GetMetronomeCache(ctx, db, queueingRedisClient, organizationID, false)
	if err != nil {
		return nil, fmt.Errorf("error getting metronome cache: %w", err)
	}
	if cachedMetInfo == nil {
		cachedMetInfo, err = UpdateMetronomeCache(ctx, db, queueingRedisClient, organizationID.String(), true)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch initial metronome cache for org: %w", err)
		}
	}

	return cachedMetInfo, nil
}

func (mc *MetronomeClient) GetPlanDetails(ctx context.Context, planID string) (map[string]interface{}, error) {
	return mc.cache.GetFresh(ctx, planID, func() (map[string]interface{}, error) {
		planDict, err := mc.client.Get(ctx, fmt.Sprintf("/planDetails/%s", planID), nil)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch plan details from metronome: %w", err)
		}
		return planDict, nil
	})
}

func enqueueMetronomeCacheUpdate(ctx context.Context, queueuingRedisClient redis.UniversalClient, orgID string) error {
	job := queue.NewJob(queue.JobOptions{
		Function: "update_metronome_cache_for_org",
		Queue:    config.Env.AdhocQueue,
		Kwargs: map[string]interface{}{
			"org_id": orgID,
		},
		Retries: queue.DefaultMaxAttempts,
	})

	if err := queue.Enqueue(ctx, job, queueuingRedisClient); err != nil {
		return fmt.Errorf("error enqueueing metronome cache update: %w", err)
	}

	return nil
}
