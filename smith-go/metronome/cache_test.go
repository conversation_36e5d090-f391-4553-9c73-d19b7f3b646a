package metronome_test

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/metronome"
	"langchain.com/smith/queue"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM organizations;")
	assert.NoError(t, err)
}

func TestGetMetronomeCache(t *testing.T) {
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisClient := lsredis.CachingRedisConnect()
	defer func() {
		err := redisClient.Close()
		if err != nil {
			t.Error(err)
		}
	}()

	_, err := redisClient.Del(context.Background(), fmt.Sprintf("saq:%s:queued", config.Env.AdhocQueue)).Result()
	assert.NoError(t, err)

	ctx := context.Background()

	orgIDStr := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	orgID := uuid.MustParse(orgIDStr)

	cacheRow := metronome.MetronomeCacheRow{
		ID:             uuid.New(),
		OrganizationID: orgID,
		CustomerID:     nil,
		PlanID:         nil,
		CustomerDetails: map[string]interface{}{
			"customer_name": "Test Customer",
		},
		PlanDetails: map[string]interface{}{
			"plan_name": "Test Plan",
		},
		CreatedAt: time.Now().UTC().Add(-2 * time.Hour),
		UpdatedAt: time.Now().UTC().Add(-2 * time.Hour),
	}

	_, err = dbpool.Exec(ctx, `
		INSERT INTO org_metronome_cache
		(id, organization_id, customer_id, plan_id, customer_details, plan_details, created_at, updated_at)
		VALUES
		($1, $2, $3, $4, $5, $6, $7, $8)
	`, cacheRow.ID, cacheRow.OrganizationID, cacheRow.CustomerID, cacheRow.PlanID, cacheRow.CustomerDetails, cacheRow.PlanDetails, cacheRow.CreatedAt, cacheRow.UpdatedAt)
	assert.NoError(t, err)

	t.Run("successful cache retrieval", func(t *testing.T) {
		skipUpdate := true
		result, err := metronome.GetMetronomeCache(ctx, dbpool, redisClient, orgID, skipUpdate)
		assert.NoError(t, err)

		assert.Equal(t, cacheRow.ID, result.ID)
		assert.Equal(t, cacheRow.OrganizationID, result.OrganizationID)
		assert.Equal(t, cacheRow.CustomerDetails, result.CustomerDetails)
		assert.Equal(t, cacheRow.PlanDetails, result.PlanDetails)
	})

	t.Run("cache update enqueued when expired", func(t *testing.T) {
		config.Env.OrgMetronomeCacheExpirySec = 3600
		skipUpdate := false
		_, err = metronome.GetMetronomeCache(ctx, dbpool, redisClient, orgID, skipUpdate)
		assert.NoError(t, err)

		jobQueueKey := fmt.Sprintf("saq:%s:queued", config.Env.AdhocQueue)
		jobKeys, err := redisClient.LRange(ctx, jobQueueKey, 0, -1).Result()
		assert.NoError(t, err)
		assert.NotEmpty(t, jobKeys)

		jobKey := jobKeys[0]
		jobData, err := redisClient.Get(ctx, jobKey).Result()
		assert.NoError(t, err)

		var job queue.Job
		err = json.Unmarshal([]byte(jobData), &job)
		assert.NoError(t, err)
		assert.Equal(t, "update_metronome_cache_for_org", job.Function)
		assert.Equal(t, orgID.String(), job.Kwargs["org_id"])
	})

	t.Run("handles invalid organization ID", func(t *testing.T) {
		invalidOrgID := uuid.New()
		skipUpdate := true
		result, err := metronome.GetMetronomeCache(ctx, dbpool, redisClient, invalidOrgID, skipUpdate)
		assert.NoError(t, err)
		// Should return empty cache row for non-existent org
		assert.Equal(t, uuid.Nil, result.ID)
		assert.Equal(t, uuid.Nil, result.OrganizationID)
		assert.Nil(t, result.CustomerDetails)
		assert.Nil(t, result.PlanDetails)
	})
}
