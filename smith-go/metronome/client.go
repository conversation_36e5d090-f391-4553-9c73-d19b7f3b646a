package metronome

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"sync"
	"time"

	"github.com/hashicorp/go-retryablehttp"
	"github.com/redis/go-redis/v9"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"
)

type MetronomeClient struct {
	client *JsonHttpClient
	cache  *lsredis.Cache[string, map[string]interface{}]
}

var (
	metronomeClientInstance *MetronomeClient
	metronomeClientOnce     sync.Once
)

var (
	uuidRegex      = regexp.MustCompile(`[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}`)
	clientInstance *JsonHttpClient
)

type tracedRetryableTransport struct {
	base http.RoundTripper
}

type JsonHttpClient struct {
	client  *retryablehttp.Client
	baseURL string
}

func pathWithoutUUIDs(path string) string {
	return uuidRegex.ReplaceAllString(path, "<uuid>")
}

func (t *tracedRetryableTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	span, ctx := tracer.StartSpanFromContext(req.Context(), "http.request",
		tracer.SpanType("http"),
		tracer.ResourceName(req.URL.Path),
	)
	defer span.Finish()

	req = req.WithContext(ctx)
	span.SetTag("http.url", req.URL.String())
	span.SetTag("http.method", req.Method)

	resp, err := t.base.RoundTrip(req)
	if err != nil {
		span.SetTag("error", err)
		return resp, err
	}

	span.SetTag("http.status_code", resp.StatusCode)
	return resp, nil
}

func createClient() *retryablehttp.Client {
	apiKey := config.Env.MetronomeApiKey
	baseTransport := http.DefaultTransport

	baseTransport = &headerRoundTripper{
		rt: baseTransport,
		headers: http.Header{
			"Authorization": []string{fmt.Sprintf("Bearer %s", apiKey)},
		},
	}

	if config.Env.DatadogEnabled {
		baseTransport = &tracedRetryableTransport{
			base: baseTransport,
		}
	}

	httpClient := &http.Client{
		Transport: baseTransport,
		Timeout:   time.Duration(config.Env.MetronomeTimeoutSecs) * time.Second,
	}

	retryClient := retryablehttp.NewClient()
	retryClient.HTTPClient = httpClient
	retryClient.RetryMax = 3
	retryClient.Logger = nil

	return retryClient
}

type headerRoundTripper struct {
	rt      http.RoundTripper
	headers http.Header
}

func (h *headerRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	req = req.Clone(req.Context())
	for k, v := range h.headers {
		req.Header[k] = v
	}
	return h.rt.RoundTrip(req)
}

func NewMetronomeClient(redisClient redis.UniversalClient) {
	metronomeClientOnce.Do(func() {
		metronomeClientInstance = &MetronomeClient{
			client: initializeJsonHttpClient(),
			cache:  lsredis.NewCache[string, map[string]interface{}](redisClient, "plan_details", 60*time.Second),
		}
	})
}

func initializeJsonHttpClient() *JsonHttpClient {
	if config.Env.MetronomeApiKey == "" {
		return nil
	}
	clientInstance = &JsonHttpClient{
		client:  createClient(),
		baseURL: "https://api.metronome.com/v1/",
	}
	return clientInstance
}

func GetMetronomeClient() *MetronomeClient {
	return metronomeClientInstance
}

func (c *JsonHttpClient) Get(ctx context.Context, path string, params map[string]string) (map[string]interface{}, error) {
	fullURL := c.baseURL + path

	req, err := retryablehttp.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, err
	}

	if len(params) > 0 {
		q := req.URL.Query()
		for k, v := range params {
			q.Add(k, v)
		}
		req.URL.RawQuery = q.Encode()
	}

	req = req.WithContext(ctx)

	pathNoUUIDs := pathWithoutUUIDs(path)

	if config.Env.DatadogEnabled {
		span, _ := tracer.StartSpanFromContext(ctx, "metronome.request",
			tracer.ServiceName("metronome"),
			tracer.ResourceName("GET: "+pathNoUUIDs),
			tracer.SpanType("http"),
		)
		defer span.Finish()

		span.SetTag("span.kind", "client")
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	var result map[string]interface{}
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&result); err != nil {
		return nil, err
	}
	return result, nil
}

func (c *JsonHttpClient) Post(ctx context.Context, path string, data interface{}) (map[string]interface{}, error) {
	fullURL := c.baseURL + path

	// Encode data as JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	req, err := retryablehttp.NewRequest("POST", fullURL, jsonData)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req = req.WithContext(ctx)

	pathNoUUIDs := pathWithoutUUIDs(path)

	if config.Env.DatadogEnabled {
		span, _ := tracer.StartSpanFromContext(ctx, "metronome.request",
			tracer.ServiceName("metronome"),
			tracer.ResourceName("POST: "+pathNoUUIDs),
			tracer.SpanType("http"),
		)
		defer span.Finish()

		span.SetTag("span.kind", "client")
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	var result map[string]interface{}
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&result); err != nil {
		return nil, err
	}
	return result, nil
}

func (c *JsonHttpClient) Delete(ctx context.Context, path string) (map[string]interface{}, error) {
	fullURL := c.baseURL + path

	req, err := retryablehttp.NewRequest("DELETE", fullURL, nil)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)

	pathNoUUIDs := pathWithoutUUIDs(path)

	if config.Env.DatadogEnabled {
		span, _ := tracer.StartSpanFromContext(ctx, "metronome.request",
			tracer.ServiceName("metronome"),
			tracer.ResourceName("DELETE: "+pathNoUUIDs),
			tracer.SpanType("http"),
		)
		defer span.Finish()

		span.SetTag("span.kind", "client")
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	var result map[string]interface{}
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&result); err != nil {
		return nil, err
	}
	return result, nil
}
