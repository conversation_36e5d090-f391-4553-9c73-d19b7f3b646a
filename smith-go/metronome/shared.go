package metronome

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/go-chi/httplog/v2"
)

type PaymentPlanTier string

const (
	PlanTierNoPlan           PaymentPlanTier = "no_plan"
	PlanTierDeveloper        PaymentPlanTier = "developer"
	PlanTierPlus             PaymentPlanTier = "plus"
	PlanTierEnterprise       PaymentPlanTier = "enterprise"
	PlanTierDeveloperLegacy  PaymentPlanTier = "developer_legacy"
	PlanTierPlusLegacy       PaymentPlanTier = "plus_legacy"
	PlanTierFree             PaymentPlanTier = "free"
	PlanTierEnterpriseLegacy PaymentPlanTier = "enterprise_legacy"
	PlanTierStartup          PaymentPlanTier = "startup"
	PlanTierPartner          PaymentPlanTier = "partner"
	PlanTierPremier          PaymentPlanTier = "premier"
)

type CustomerVisiblePlanInfo struct {
	Tier      PaymentPlanTier `json:"tier"`
	StartedOn time.Time       `json:"started_on"`
	EndsOn    *time.Time      `json:"ends_on,omitempty"`
}

type PaymentPlanInfo struct {
	CustomerVisiblePlanInfo
	ID     string `json:"id"`
	PlanID string `json:"plan_id"`
}

type RelevantPlansForCustomer struct {
	CurrentPlan  *PaymentPlanInfo `json:"current_plan"`
	UpcomingPlan *PaymentPlanInfo `json:"upcoming_plan"`
}

func parseMetronomeTimestamp(timestamp string) (time.Time, error) {
	if timestamp == "" {
		return time.Time{}, fmt.Errorf("empty timestamp")
	}
	return time.Parse("2006-01-02T15:04:05-0700", timestamp)
}

func getPaymentPlanInfoFromCustomerPlan(ctx context.Context, oplog *slog.Logger, metronomeClient *MetronomeClient, metronomeCustomerID string, customerPlan map[string]interface{}) (*PaymentPlanInfo, error) {
	planID, ok := customerPlan["plan_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid plan_id format")
	}

	plan, err := metronomeClient.GetPlanDetails(ctx, planID)
	if err != nil {
		return nil, fmt.Errorf("failed to get plan details: %w", err)
	}

	data, ok := plan["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid plan data format")
	}

	customFields, ok := data["custom_fields"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid custom fields format")
	}

	tierFromMetadata, ok := customFields["__tier"].(string)
	if !ok || tierFromMetadata == "" {
		oplog.Error(
			"Could not determine tier from plan",
			"metronome_customer_id", metronomeCustomerID,
			"plan_id", planID,
			"customer_plan_id", customerPlan["id"],
		)
		return nil, fmt.Errorf("could not determine tier from plan")
	}

	startedOn, err := parseMetronomeTimestamp(customerPlan["starting_on"].(string))
	if err != nil {
		return nil, fmt.Errorf("invalid starting_on timestamp: %w", err)
	}

	var endsOn *time.Time
	if endingBefore, ok := customerPlan["ending_before"].(string); ok && endingBefore != "" {
		parsed, err := parseMetronomeTimestamp(endingBefore)
		if err != nil {
			return nil, fmt.Errorf("invalid ending_before timestamp: %w", err)
		}
		endsOn = &parsed
	}

	return &PaymentPlanInfo{
		CustomerVisiblePlanInfo: CustomerVisiblePlanInfo{
			Tier:      PaymentPlanTier(tierFromMetadata),
			StartedOn: startedOn,
			EndsOn:    endsOn,
		},
		ID:     customerPlan["id"].(string),
		PlanID: planID,
	}, nil
}

func listCustomerPlans(ctx context.Context, metronomeCustomerID string) ([]map[string]interface{}, error) {
	metronomeClient := GetMetronomeClient()
	if metronomeClient == nil {
		return nil, fmt.Errorf("unable to get metronome client")
	}
	params := map[string]string{"limit": "100"} // TODO: implement pagination

	result, err := metronomeClient.client.Get(ctx, fmt.Sprintf("customers/%s/plans", metronomeCustomerID), params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch customer plans: %w", err)
	}

	data, ok := result["data"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response format")
	}

	plans := make([]map[string]interface{}, len(data))
	for i, plan := range data {
		planMap, ok := plan.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("invalid plan format at index %d", i)
		}
		plans[i] = planMap
	}

	return plans, nil
}

func GetRelevantPlansForCustomer(ctx context.Context, metronomeClient *MetronomeClient, metronomeCustomerID string) (*RelevantPlansForCustomer, error) {
	oplog := httplog.LogEntry(ctx)

	relevantPlans := &RelevantPlansForCustomer{}

	if metronomeCustomerID == "" {
		return relevantPlans, nil
	}

	customerPlans, err := listCustomerPlans(ctx, metronomeCustomerID)
	if err != nil {
		return nil, fmt.Errorf("failed to list customer plans: %w", err)
	}

	now := time.Now().UTC()
	type planTiming struct {
		startTime time.Time
		endTime   *time.Time
		plan      map[string]interface{}
	}

	planTimings := make([]planTiming, 0, len(customerPlans))
	for _, plan := range customerPlans {
		startStr, ok := plan["starting_on"].(string)
		if !ok {
			continue
		}

		startTime, err := parseMetronomeTimestamp(startStr)
		if err != nil {
			continue
		}

		var endTime *time.Time
		if endStr, ok := plan["ending_before"].(string); ok && endStr != "" {
			parsed, err := parseMetronomeTimestamp(endStr)
			if err == nil {
				endTime = &parsed
			}
		}

		planTimings = append(planTimings, planTiming{
			startTime: startTime,
			endTime:   endTime,
			plan:      plan,
		})
	}

	for i := len(planTimings) - 1; i >= 0; i-- {
		timing := planTimings[i]

		if timing.startTime.Before(now) {
			if timing.endTime == nil || timing.endTime.After(now) {
				if relevantPlans.CurrentPlan != nil {
					oplog.Warn("Found multiple current plans for a customer", "metronome_customer_id", metronomeCustomerID)
					continue
				}

				planInfo, err := getPaymentPlanInfoFromCustomerPlan(ctx, oplog, metronomeClient, metronomeCustomerID, timing.plan)
				if err != nil {
					return nil, fmt.Errorf("failed to get payment plan info: %w", err)
				}
				relevantPlans.CurrentPlan = planInfo
			}
		}

		if relevantPlans.UpcomingPlan == nil && timing.startTime.After(now) {
			planInfo, err := getPaymentPlanInfoFromCustomerPlan(ctx, oplog, metronomeClient, metronomeCustomerID, timing.plan)
			if err != nil {
				return nil, fmt.Errorf("failed to get payment plan info: %w", err)
			}
			relevantPlans.UpcomingPlan = planInfo
			break
		}
	}

	return relevantPlans, nil
}
