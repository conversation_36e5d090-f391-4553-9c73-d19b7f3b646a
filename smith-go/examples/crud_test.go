package examples_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"langchain.com/smith/examples"
)

func TestTimeUTC_UnmarshalJSON(t *testing.T) {

	t.Run("EmptyString", func(t *testing.T) {
		// An empty JSON string should parse to a zero time
		var tUTC examples.TimeUTC
		err := tUTC.UnmarshalJSON([]byte(`""`))
		require.NoError(t, err, "Unmarshaling empty string should not error")
		assert.True(t, tUTC.Time.IsZero(), "Expected zero time for empty string")
	})

	t.Run("ValidRFC3339", func(t *testing.T) {
		// A properly formatted RFC3339 string with a UTC timezone
		var tUTC examples.TimeUTC
		err := tUTC.UnmarshalJSON([]byte(`"2023-10-05T14:48:00Z"`))
		require.NoError(t, err, "Valid RFC3339 should not error")
		assert.Equal(t, 2023, tUTC.Time.Year())
		assert.Equal(t, time.October, tUTC.Time.Month())
		assert.Equal(t, 5, tUTC.Time.Day())
		assert.Equal(t, 14, tUTC.Time.Hour())
		assert.Equal(t, 48, tUTC.Time.Minute())
		assert.Equal(t, 0, tUTC.Time.Second())
		assert.Equal(t, time.UTC, tUTC.Time.Location(), "Should be UTC location")
	})

	t.Run("NoTimezone", func(t *testing.T) {
		// A valid datetime with no timezone portion should default to UTC
		var tUTC examples.TimeUTC
		err := tUTC.UnmarshalJSON([]byte(`"2023-10-05T14:48:00"`))
		require.NoError(t, err, "Valid time string without timezone should parse and default to UTC")
		assert.Equal(t, time.UTC, tUTC.Time.Location(), "Missing TZ should result in UTC")
		assert.Equal(t, 2023, tUTC.Time.Year())
		assert.Equal(t, time.October, tUTC.Time.Month())
		assert.Equal(t, 5, tUTC.Time.Day())
		assert.Equal(t, 14, tUTC.Time.Hour())
		assert.Equal(t, 48, tUTC.Time.Minute())
	})

	t.Run("InvalidFormat", func(t *testing.T) {
		// An invalid time string should return an error
		var tUTC examples.TimeUTC
		err := tUTC.UnmarshalJSON([]byte(`"Invalid Time String"`))
		require.Error(t, err, "Invalid datetime string should produce an error")
		assert.Contains(t, err.Error(), "TimeUTC UnmarshalJSON error", "Should wrap underlying parse error")
	})
}
