package examples_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/textproto"
	"testing"

	"github.com/go-chi/chi/v5"

	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"github.com/neilotoole/slogt"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/auth"
	"langchain.com/smith/beacon"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/examples"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/storage"
	"langchain.com/smith/testutil/leak"
)

func testLogger(t *testing.T) func(next http.Handler) http.Handler {
	opt := httplog.Options{LogLevel: slog.Level(config.Env.SlogLevel)}
	log := &httplog.Logger{Logger: slogt.New(t), Options: opt}
	return httplog.RequestLogger(log)
}

type fixture struct {
	apiKey     string
	apiKeyHash string

	organizationName string
	organizationID   string
	tenantName       string
	tenantID         string
	tenantConfig     string

	datasetIDNoSchema   string
	datasetNameNoSchema string

	datasetIDWithSchema   string
	datasetNameWithSchema string
	schemaDefinition      string

	datasetIDWithTransformations   string
	datasetNameWithTransformations string
}

func newFixture(t *testing.T) fixture {
	t.Helper()

	apiKey, apiKeyHash, err := beacon.GenerateKeyAndHash("lsv2_sk_")
	assert.NoError(t, err)

	return fixture{
		apiKey:     apiKey,
		apiKeyHash: apiKeyHash,

		organizationName: "test org",
		organizationID:   uuid.NewString(),

		tenantName:   "test tenant",
		tenantID:     uuid.NewString(),
		tenantConfig: "{}",

		datasetIDNoSchema:   uuid.NewString(),
		datasetNameNoSchema: "test dataset no schema",

		datasetIDWithSchema:   uuid.NewString(),
		datasetNameWithSchema: "test dataset with schema",

		schemaDefinition: `{"type": "object", "properties": {"field": {"type": "string"}}, "required": ["field"]}`,

		datasetIDWithTransformations:   uuid.NewString(),
		datasetNameWithTransformations: "test dataset with transformations",
	}
}

func setUpDatabase(t *testing.T, dbpool *database.AuditLoggedPool) fixture {
	t.Helper()

	f := newFixture(t)

	_, err := dbpool.Exec(
		context.Background(),
		`with
org as (
	insert into organizations (id, display_name, created_by_user_id) select $4, $5, gen_random_uuid() returning id
),

ten as (
	insert into tenants (id, display_name, config, organization_id)
	select $1, $2, $3, id
	from org
	returning id
),

service_account as (
	insert into service_accounts (id, name, organization_id)
	select $8, 'test service account', id
    from org
    returning id
),
org_ident as (
	insert into identities (id, organization_id, role_id, service_account_id, access_scope)
	select $9, org.id, (select id from roles where name = 'ORGANIZATION_ADMIN'),  $8, 'organization'
	from ten
	cross join org
	returning id
),

ident as (
	insert into identities (id, tenant_id, organization_id, role_id, service_account_id, parent_identity_id)
	select $6, ten.id, org.id, (select id from roles where name = 'WORKSPACE_ADMIN'), $8, $9
	from ten
	cross join org
	returning id, tenant_id
),

dataset_no_schema as (
	insert into dataset (id, tenant_id, name)
	values ($10, $1, $11)
),

dataset_with_schema as (
	insert into dataset (id, tenant_id, name, inputs_schema_definition, outputs_schema_definition)
	values ($12, $1, $13, $14, $14)
),

dataset_with_transformations as (
	insert into dataset (id, tenant_id, name, inputs_schema_definition, outputs_schema_definition)
	values ($15, $1, $16, $14, $14)
),

dataset_transformations_insert AS (
    insert into dataset_transformations (id, dataset_id, transformation_type, path)
	select gen_random_uuid(), $15, 'remove_extra_fields', '["inputs"]'::jsonb
)

insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
select gen_random_uuid(), $7, tenant_id, id, 'hmm', $8, $4
from ident`,
		f.tenantID,
		f.tenantName,
		f.tenantConfig,
		f.organizationID,
		f.organizationName,
		uuid.NewString(),
		f.apiKeyHash,
		uuid.NewString(),
		uuid.NewString(),
		f.datasetIDNoSchema,
		f.datasetNameNoSchema,
		f.datasetIDWithSchema,
		f.datasetNameWithSchema,
		f.schemaDefinition,
		f.datasetIDWithTransformations,
		f.datasetNameWithTransformations,
	)

	assert.NoError(t, err)
	return f
}

type multipartPart struct {
	fieldName   string
	contentType string
	content     []byte
	headers     map[string]string
}

type testCase struct {
	name              string
	exampleIds        []string
	attachmentContent []byte
	requestParts      func(tc *testCase) []multipartPart
	expectedStatus    int
	expectedBody      string
	verify            func(t *testing.T, tc *testCase)
	forceDiskUpload   bool
	forceDirectUpload bool
	verifyOrder       bool
}

type uploadExamplesTestCase struct {
	name              string
	datasetId         string
	exampleIds        []string
	attachmentContent []byte
	requestParts      func(tc *uploadExamplesTestCase) []multipartPart
	expectedStatus    int
	expectedBody      string
	verify            func(t *testing.T, tc *uploadExamplesTestCase)
	forceDiskUpload   bool
	forceDirectUpload bool
	verifyOrder       bool
}

type updateExamplesTestCase struct {
	name               string
	datasetId          string
	exampleIds         []string
	attachmentContent  []byte
	uploadRequestParts func(tc *updateExamplesTestCase) []multipartPart
	updateRequestParts func(tc *updateExamplesTestCase) []multipartPart
	expectedStatus     int
	expectedBody       string
	verify             func(t *testing.T, tc *updateExamplesTestCase)
}

type updateExamplesMultipleTimesTestCase struct {
	name                     string
	datasetId                string
	exampleIds               []string
	attachmentContent        []byte
	uploadRequestParts       func(tc *updateExamplesMultipleTimesTestCase) []multipartPart
	firstUpdateRequestParts  func(tc *updateExamplesMultipleTimesTestCase) []multipartPart
	secondUpdateRequestParts func(tc *updateExamplesMultipleTimesTestCase) []multipartPart
	verify                   func(t *testing.T, tc *updateExamplesMultipleTimesTestCase)
}

type TestAttachmentInfo struct {
	MimeType string `json:"mime_type"`
	Content  string `json:"content"`
}
type testExample struct {
	ID             string                        `json:"id"`
	DatasetID      string                        `json:"dataset_id"`
	Inputs         map[string]interface{}        `json:"inputs"`
	Outputs        map[string]interface{}        `json:"outputs"`
	Metadata       map[string]interface{}        `json:"metadata"`
	AttachmentURLs map[string]TestAttachmentInfo `json:"attachment_urls"`
	SourceRunID    string                        `json:"source_run_id"`
}

func verifyStoredExamples(t *testing.T, dbpool *database.AuditLoggedPool, storageClient storage.StorageClient, expectedExamples []testExample, testCaseExampleIds []string, verifyOrder bool) {
	sql := `
    SELECT id, dataset_id, inputs, outputs, metadata, attachment_urls
    FROM examples_log
    WHERE id = ANY($1::uuid[])
    ORDER BY modified_at ASC
    `
	rows, err := dbpool.Query(context.Background(), sql, testCaseExampleIds)
	require.NoError(t, err)
	defer rows.Close()

	queriedExamples := make([]testExample, 0)

	for rows.Next() {
		var ex testExample
		var inputsData []byte
		var outputsData []byte
		var metadataData []byte
		var attachmentsData []byte

		err := rows.Scan(&ex.ID, &ex.DatasetID, &inputsData, &outputsData, &metadataData, &attachmentsData)
		require.NoError(t, err)

		if inputsData != nil {
			err = json.Unmarshal(inputsData, &ex.Inputs)
			require.NoError(t, err)
		}

		if outputsData != nil {
			err = json.Unmarshal(outputsData, &ex.Outputs)
			require.NoError(t, err)
		}

		if metadataData != nil {
			err = json.Unmarshal(metadataData, &ex.Metadata)
			require.NoError(t, err)
		}

		if attachmentsData != nil {
			attachments := make(map[string]map[string]string)
			err = json.Unmarshal(attachmentsData, &attachments)
			require.NoError(t, err)

			// Convert to AttachmentURLs format
			ex.AttachmentURLs = make(map[string]TestAttachmentInfo)
			for key, attachment := range attachments {
				mimeType, ok := attachment["mime_type"]
				require.True(t, ok, "mime_type not found in attachment")
				storageURL, ok := attachment["storage_url"]
				require.True(t, ok, "storage_url not found in attachment")

				// Get content from storage
				output, err := storageClient.GetObject(context.Background(), &storage.GetObjectInput{
					Bucket: config.Env.S3BucketName,
					Key:    storageURL,
				})
				require.NoError(t, err)

				buf := new(bytes.Buffer)
				_, err = buf.ReadFrom(output.Body)
				require.NoError(t, err)

				ex.AttachmentURLs[key] = TestAttachmentInfo{
					MimeType: mimeType,
					Content:  buf.String(),
				}
			}
		}

		queriedExamples = append(queriedExamples, ex)
	}
	require.NoError(t, rows.Err())
	require.Equal(t, len(expectedExamples), len(queriedExamples), fmt.Sprintf("Expected %d examples", len(expectedExamples)))

	for i, expected := range expectedExamples {
		var qe *testExample
		if verifyOrder {
			assert.Equal(t, expected.ID, queriedExamples[i].ID)
			qe = &queriedExamples[i]
		} else {
			for _, ex := range queriedExamples {
				if ex.ID == expected.ID {
					qe = &ex
					break
				}
			}
			require.NotNil(t, qe, fmt.Sprintf("Example %s not found in database", expected.ID))
		}

		assert.NotNil(t, qe)
		assert.Equal(t, expected.DatasetID, qe.DatasetID)
		assert.Equal(t, expected.Inputs, qe.Inputs)
		assert.Equal(t, expected.Outputs, qe.Outputs)
		assert.Equal(t, expected.Metadata, qe.Metadata)

		if expected.AttachmentURLs == nil {
			assert.Nil(t, qe.AttachmentURLs)
		} else {
			require.NotNil(t, qe.AttachmentURLs)
			assert.Equal(t, len(expected.AttachmentURLs), len(qe.AttachmentURLs))

			for key, s3Info := range qe.AttachmentURLs {
				attachmentContent, exists := expected.AttachmentURLs[key]
				assert.True(t, exists, fmt.Sprintf("Attachment key %s not found in expected attachment urls", key))
				assert.Equal(t, attachmentContent.MimeType, s3Info.MimeType)
				assert.Equal(t, attachmentContent.Content, s3Info.Content)
			}
		}
	}
}

func TestMultipartExamplesHandler_UpdateMultipleTimesDatasetExamples(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	f := setUpDatabase(t, dbpool)

	// TODO(agola11) add other auth types
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	if err != nil {
		t.Fatal(err)
	}

	h := examples.MultipartExamplesHandler{
		Pg:            dbpool,
		StorageClient: s3StorageClient,
		ExamplesCRUD:  examples.NewExamplesCRUD(dbpool),
	}

	headers := map[string]string{
		"X-Api-Key": f.apiKey,
	}

	tests := []updateExamplesMultipleTimesTestCase{
		{
			name:              "test updating always updates the latest example",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.NewString(), uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesMultipleTimesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			firstUpdateRequestParts: func(tc *updateExamplesMultipleTimesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"rename": {"image":"image2"}}`),
					},
				}
			},
			secondUpdateRequestParts: func(tc *updateExamplesMultipleTimesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"retain": ["image2"]}`),
					},
				}
			},
			verify: func(t *testing.T, tc *updateExamplesMultipleTimesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image2": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image2": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, []string{tc.exampleIds[0]}, true)
			},
		},
		{
			name:       "test source_run_id preservation and updates",
			datasetId:  f.datasetIDNoSchema,
			exampleIds: []string{uuid.NewString()},
			uploadRequestParts: func(tc *updateExamplesMultipleTimesTestCase) []multipartPart {
				// Use a fixed source_run_id for initial creation
				initialSourceRunID := "11111111-1111-1111-1111-111111111111"
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"metadata": {"key": "value"}, "source_run_id": "%s"}`, initialSourceRunID)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			firstUpdateRequestParts: func(tc *updateExamplesMultipleTimesTestCase) []multipartPart {
				// First update: only update metadata, don't specify source_run_id
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "updated_value"}}`),
					},
				}
			},
			secondUpdateRequestParts: func(tc *updateExamplesMultipleTimesTestCase) []multipartPart {
				// Second update: change source_run_id to a new fixed value
				newSourceRunID := "22222222-2222-2222-2222-222222222222"
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"source_run_id": "%s"}`, newSourceRunID)),
					},
				}
			},
			verify: func(t *testing.T, tc *updateExamplesMultipleTimesTestCase) {
				// verification for standard example fields
				expectedExamples := []testExample{
					{
						ID:          tc.exampleIds[0],
						DatasetID:   f.datasetIDNoSchema,
						Inputs:      map[string]interface{}{"input_field": "input_value"},
						Outputs:     map[string]interface{}{"output_field": "output_value"},
						Metadata:    map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						SourceRunID: "11111111-1111-1111-1111-111111111111",
					},
					{
						ID:          tc.exampleIds[0],
						DatasetID:   f.datasetIDNoSchema,
						Inputs:      map[string]interface{}{"input_field": "input_value"},
						Outputs:     map[string]interface{}{"output_field": "output_value"},
						Metadata:    map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "updated_value"},
						SourceRunID: "11111111-1111-1111-1111-111111111111",
					},
					{
						ID:          tc.exampleIds[0],
						DatasetID:   f.datasetIDNoSchema,
						Inputs:      map[string]interface{}{"input_field": "input_value"},
						Outputs:     map[string]interface{}{"output_field": "output_value"},
						Metadata:    map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "updated_value"},
						SourceRunID: "22222222-2222-2222-2222-222222222222",
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, []string{tc.exampleIds[0]}, true)
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Prepare the request parts
			uploadParts := tc.uploadRequestParts(&tc)
			firstUpdateParts := tc.firstUpdateRequestParts(&tc)
			secondUpdateParts := tc.secondUpdateRequestParts(&tc)

			// Create upload multipart request
			var uploadBody bytes.Buffer
			uploadWriter := multipart.NewWriter(&uploadBody)

			// Add parts to upload request
			for _, part := range uploadParts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = uploadWriter.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}
			uploadErr := uploadWriter.Close()
			require.NoError(t, uploadErr, "Failed to close writer")

			uploadReq := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/datasets/%s/examples", tc.datasetId), &uploadBody)
			uploadReq.Header.Set("Content-Type", uploadWriter.FormDataContentType())
			for k, v := range headers {
				uploadReq.Header.Set(k, v)
			}

			rr := httptest.NewRecorder()

			// wrap the handler with the testlogger and auth middleware
			r := chi.NewRouter()
			r.Use(testLogger(t))
			r.Use(ah.Middleware)
			r.Post("/datasets/{dataset_id}/examples", h.UploadDatasetExamples)
			r.Patch("/datasets/{dataset_id}/examples", h.UpdateDatasetExamples)
			r.ServeHTTP(rr, uploadReq)

			assert.Equal(t, http.StatusCreated, rr.Code, "UploadExamples returned wrong status code")

			// Create update multipart request
			var firstUpdateBody bytes.Buffer
			firstUpdateWriter := multipart.NewWriter(&firstUpdateBody)

			// Add parts to update request
			for _, part := range firstUpdateParts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = firstUpdateWriter.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}
			firstUpdateErr := firstUpdateWriter.Close()
			require.NoError(t, firstUpdateErr, "Failed to close writer")

			firstUpdateReq := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/datasets/%s/examples", tc.datasetId), &firstUpdateBody)
			firstUpdateReq.Header.Set("Content-Type", firstUpdateWriter.FormDataContentType())
			for k, v := range headers {
				firstUpdateReq.Header.Set(k, v)
			}
			rr = httptest.NewRecorder()
			r.ServeHTTP(rr, firstUpdateReq)

			assert.Equal(t, http.StatusCreated, rr.Code, "UpdateDatasetExamples returned wrong status code")

			// Create second update multipart request
			var secondUpdateBody bytes.Buffer
			secondUpdateWriter := multipart.NewWriter(&secondUpdateBody)

			// Add parts to update request
			for _, part := range secondUpdateParts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = secondUpdateWriter.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}
			secondUpdateErr := secondUpdateWriter.Close()
			require.NoError(t, secondUpdateErr, "Failed to close writer")

			secondUpdateReq := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/datasets/%s/examples", tc.datasetId), &secondUpdateBody)
			secondUpdateReq.Header.Set("Content-Type", secondUpdateWriter.FormDataContentType())
			for k, v := range headers {
				secondUpdateReq.Header.Set(k, v)
			}
			rr = httptest.NewRecorder()
			r.ServeHTTP(rr, secondUpdateReq)

			assert.Equal(t, http.StatusCreated, rr.Code, "UpdateDatasetExamples returned wrong status code")

			if tc.verify != nil {
				tc.verify(t, &tc)
			}
		})
	}
}

func TestMultipartExamplesHandler_DuplicateExampleID(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	f := setUpDatabase(t, dbpool)

	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	if err != nil {
		t.Fatal(err)
	}

	h := examples.MultipartExamplesHandler{
		Pg:            dbpool,
		StorageClient: s3StorageClient,
		ExamplesCRUD:  examples.NewExamplesCRUD(dbpool),
	}

	headers := map[string]string{
		"X-Api-Key": f.apiKey,
	}
	rr := httptest.NewRecorder()
	router := chi.NewRouter()
	router.Use(testLogger(t))
	router.Use(ah.Middleware)
	router.Post("/datasets/{dataset_id}/examples", h.UploadDatasetExamples)

	// Create upload multipart request
	exampleId := uuid.New().String()
	var uploadBody1 = []multipartPart{
		{
			fieldName:   exampleId,
			contentType: "application/json",
			content:     []byte(`{"metadata": {"key": "value"}}`),
		},
		{
			fieldName:   fmt.Sprintf("%s.inputs", exampleId),
			contentType: "application/json",
			content:     []byte(`{"input_field": "input_value"}`),
		},
	}
	var uploadBody2 = []multipartPart{
		{
			fieldName:   exampleId,
			contentType: "application/json",
			content:     []byte(`{"metadata": {"key": "value"}}`),
		},
		{
			fieldName:   fmt.Sprintf("%s.inputs", exampleId),
			contentType: "application/json",
			content:     []byte(`{"input_field": "input_value"}`),
		},
	}

	t.Run("conflict on already seen example", func(t *testing.T) {

		// Create upload multipart request
		var uploadBody bytes.Buffer
		uploadWriter := multipart.NewWriter(&uploadBody)

		// Add parts to upload request
		for _, part := range uploadBody1 {
			var partWriter io.Writer
			var err error
			headers := make(textproto.MIMEHeader)
			headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
			if part.contentType != "" {
				headers.Set("Content-Type", part.contentType)
			}
			for k, v := range part.headers {
				headers.Set(k, v)
			}
			partWriter, err = uploadWriter.CreatePart(headers)
			require.NoError(t, err, "Failed to create form part")
			_, err = partWriter.Write(part.content)
			require.NoError(t, err, "Failed to write to form part")
		}
		uploadErr := uploadWriter.Close()
		require.NoError(t, uploadErr, "Failed to close writer")

		uploadReq := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/datasets/%s/examples", f.datasetIDNoSchema), &uploadBody)
		uploadReq.Header.Set("Content-Type", uploadWriter.FormDataContentType())
		for k, v := range headers {
			uploadReq.Header.Set(k, v)
		}

		router.ServeHTTP(rr, uploadReq)
		assert.Equal(t, http.StatusCreated, rr.Code, "UploadExamples returned wrong status code")

		// Create second upload multipart request
		var secondUploadBody bytes.Buffer
		secondUploadWriter := multipart.NewWriter(&secondUploadBody)

		// Add parts to upload request
		for _, part := range uploadBody2 {
			var partWriter io.Writer
			var err error
			headers := make(textproto.MIMEHeader)
			headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
			if part.contentType != "" {
				headers.Set("Content-Type", part.contentType)
			}
			for k, v := range part.headers {
				headers.Set(k, v)
			}
			partWriter, err = secondUploadWriter.CreatePart(headers)
			require.NoError(t, err, "Failed to create form part")
			_, err = partWriter.Write(part.content)
			require.NoError(t, err, "Failed to write to form part")
		}
		secondUploadErr := secondUploadWriter.Close()
		require.NoError(t, secondUploadErr, "Failed to close writer")

		secondUploadReq := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/datasets/%s/examples", f.datasetIDNoSchema), &secondUploadBody)
		secondUploadReq.Header.Set("Content-Type", secondUploadWriter.FormDataContentType())
		for k, v := range headers {
			secondUploadReq.Header.Set(k, v)
		}
		rr = httptest.NewRecorder()
		router.ServeHTTP(rr, secondUploadReq)
		assert.Equal(t, http.StatusConflict, rr.Code, "UploadExamples returned wrong status code")
	})
}

func TestMultipartExamplesHandler_UpdateDatasetExamples(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	f := setUpDatabase(t, dbpool)

	// TODO(agola11) add other auth types
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	if err != nil {
		t.Fatal(err)
	}

	h := examples.MultipartExamplesHandler{
		Pg:            dbpool,
		StorageClient: s3StorageClient,
		ExamplesCRUD:  examples.NewExamplesCRUD(dbpool),
	}

	headers := map[string]string{
		"X-Api-Key": f.apiKey,
	}

	// --- 1) Spin up a mock server for smith-backend /examples/validate/bulk ---
	mockSmithBackend := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodPost && r.URL.Path == "/examples/validate/bulk" {
			w.Header().Set("Content-Type", "application/json")

			// We'll decode the request body into a slice of generic maps
			// (each entry should match your CREATE_BULK_SCHEMA.json).
			var input []map[string]any
			if err := json.NewDecoder(r.Body).Decode(&input); err != nil {
				w.WriteHeader(http.StatusBadRequest)
				_, _ = w.Write([]byte(`{"error":"failed to decode JSON"}`))
				return
			}

			var output []map[string]any
			for _, ex := range input {
				datasetID, _ := ex["dataset_id"].(string)
				id, ok := ex["id"].(string)
				if !ok || id == "" {
					id = uuid.New().String()
				}

				newEx := map[string]any{
					"dataset_id":        datasetID,
					"inputs":            map[string]any{"field": ex["inputs"].(map[string]any)["field"]},
					"outputs":           map[string]any{"field": ex["outputs"].(map[string]any)["field"]},
					"created_at":        ex["created_at"],
					"metadata":          ex["metadata"],
					"source_run_id":     ex["source_run_id"],
					"split":             ex["split"],
					"id":                id,
					"use_source_run_io": ex["use_source_run_io"],
					"overwrite":         ex["overwrite"],
				}

				if newEx["split"] == nil {
					newEx["split"] = "base"
				}
				if newEx["use_source_run_io"] == nil {
					newEx["use_source_run_io"] = false
				}
				if newEx["overwrite"] == nil {
					newEx["overwrite"] = false
				}

				output = append(output, newEx)
			}

			w.WriteHeader(http.StatusOK)
			if err := json.NewEncoder(w).Encode(output); err != nil {
				t.Errorf("failed to encode mock JSON response: %v", err)
			}
			return
		}

		// Return 404 for anything we didn't mock
		http.NotFound(w, r)
	}))
	defer mockSmithBackend.Close()

	oldSmithBackendEndpoint := config.Env.SmithBackendEndpoint
	defer func() {
		// Revert after the test
		config.Env.SmithBackendEndpoint = oldSmithBackendEndpoint
	}()
	// Point your code to the mock server
	config.Env.SmithBackendEndpoint = mockSmithBackend.URL

	tests := []updateExamplesTestCase{
		{
			name:              "update example preserves existing data",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "update example with invalid input",
			datasetId:         f.datasetIDWithSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": 1}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "schema validation errors",
		},
		{
			name:              "update example with invalid output",
			datasetId:         f.datasetIDWithSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": 1}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "schema validation errors",
		},
		{
			name:              "update example retain attachments",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_0", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_1", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"retain": ["attachment_0"]}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "update example with new attachments",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_0", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "update example rename attachment",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_0", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"rename": {"attachment_0":"attachment_1"}}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "cannot rename key and retain attachment",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_0", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"rename": {"attachment_0":"attachment_1"}, "retain": ["attachment_0"]}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "appears in both retain and rename",
		},
		{
			name:              "cannot rename value and retain attachment",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_0", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"rename": {"foo":"attachment_0"}, "retain": ["attachment_0"]}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "appears in both retain and rename",
		},
		{
			name:              "new attachments overwrite old attachments",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_0", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"rename": {"attachment_0":"attachment_1"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_1", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "updating metadata alone works",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "new_value"}}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Outputs:        map[string]interface{}{"output_field": "output_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Outputs:        map[string]interface{}{"output_field": "output_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "new_value"},
						AttachmentURLs: nil,
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "updating split does not update metadata",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"split": "new_split"}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"key": "value", "dataset_split": []interface{}{"new_split"}},
						AttachmentURLs: nil,
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "updating metadata does not update split",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}, "split": "train"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "new_value"}}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"key": "value", "dataset_split": []interface{}{"train"}},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"key": "new_value", "dataset_split": []interface{}{"train"}},
						AttachmentURLs: nil,
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "can update splits with list",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"split": ["train", "test"]}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"key": "value", "dataset_split": []interface{}{"train", "test"}},
						AttachmentURLs: nil,
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "can update attachments with periods in name",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.foo.foo", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.foo.foo": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "can rename and retain attachments with periods in name",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.foo.foo", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.bar.bar", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"rename": {"foo.foo": "baz.baz"}, "retain": ["bar.bar"]}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.foo.foo": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.bar.bar": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.baz.baz": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.bar.bar": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		{
			name:              "empty attachments operations deletes everything",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.New().String()},
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.foo.foo", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.bar.bar", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachments_operations", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.foo.foo": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.bar.bar": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
				}
				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
		// try to update an example against a dataset with transformations
		{
			name:              "Multipart request dataset with transformations",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDWithTransformations,
			attachmentContent: []byte("attachment content"),
			uploadRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "output_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			updateRequestParts: func(tc *updateExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"new_key": "new_value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "new_input_value", "extra_field": "extra_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "new_output_value"}`),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *updateExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDWithTransformations,
						Inputs:    map[string]interface{}{"field": "input_value"},
						Outputs:   map[string]interface{}{"field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  "attachment content",
							},
						},
					},
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDWithTransformations,
						Inputs:    map[string]interface{}{"field": "new_input_value"},
						Outputs:   map[string]interface{}{"field": "new_output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "new_key": "new_value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  "attachment content",
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, true)
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {

			// Prepare the request parts
			uploadParts := tc.uploadRequestParts(&tc)
			updateParts := tc.updateRequestParts(&tc)

			// Create upload multipart request
			var uploadBody bytes.Buffer
			uploadWriter := multipart.NewWriter(&uploadBody)

			// Add parts to upload request
			for _, part := range uploadParts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = uploadWriter.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}
			uploadErr := uploadWriter.Close()
			require.NoError(t, uploadErr, "Failed to close writer")

			uploadReq := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/datasets/%s/examples", tc.datasetId), &uploadBody)
			uploadReq.Header.Set("Content-Type", uploadWriter.FormDataContentType())
			for k, v := range headers {
				uploadReq.Header.Set(k, v)
			}

			rr := httptest.NewRecorder()

			// wrap the handler with the testlogger and auth middleware
			r := chi.NewRouter()
			r.Use(testLogger(t))
			r.Use(ah.Middleware)
			r.Post("/datasets/{dataset_id}/examples", h.UploadDatasetExamples)
			r.Patch("/datasets/{dataset_id}/examples", h.UpdateDatasetExamples)
			r.ServeHTTP(rr, uploadReq)

			assert.Equal(t, http.StatusCreated, rr.Code, "UploadExamples returned wrong status code")

			// Create update multipart request
			var updateBody bytes.Buffer
			updateWriter := multipart.NewWriter(&updateBody)

			// Add parts to update request
			for _, part := range updateParts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = updateWriter.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}
			updateErr := updateWriter.Close()
			require.NoError(t, updateErr, "Failed to close writer")

			updateReq := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/datasets/%s/examples", tc.datasetId), &updateBody)
			updateReq.Header.Set("Content-Type", updateWriter.FormDataContentType())
			for k, v := range headers {
				updateReq.Header.Set(k, v)
			}
			rr = httptest.NewRecorder()
			r.ServeHTTP(rr, updateReq)

			assert.Equal(t, tc.expectedStatus, rr.Code, "UpdateDatasetExamples returned wrong status code")

			if tc.expectedBody != "" {
				assert.Contains(t, rr.Body.String(), tc.expectedBody, "UpdateDatasetExamples returned unexpected body")
			}

			if tc.verify != nil {
				tc.verify(t, &tc)
			}
		})
	}
}

func TestMultipartExamplesHandler_UploadDatasetExamples(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	f := setUpDatabase(t, dbpool)

	// TODO(agola11) add other auth types
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	if err != nil {
		t.Fatal(err)
	}

	h := examples.MultipartExamplesHandler{
		Pg:            dbpool,
		StorageClient: s3StorageClient,
		ExamplesCRUD:  examples.NewExamplesCRUD(dbpool),
	}

	headers := map[string]string{
		"X-Api-Key": f.apiKey,
	}

	// --- 1) Spin up a mock server for smith-backend /examples/validate/bulk ---
	mockSmithBackend := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodPost && r.URL.Path == "/examples/validate/bulk" {
			w.Header().Set("Content-Type", "application/json")

			// We'll decode the request body into a slice of generic maps
			// (each entry should match your CREATE_BULK_SCHEMA.json).
			var input []map[string]any
			if err := json.NewDecoder(r.Body).Decode(&input); err != nil {
				w.WriteHeader(http.StatusBadRequest)
				_, _ = w.Write([]byte(`{"error":"failed to decode JSON"}`))
				return
			}

			var output []map[string]any
			for _, ex := range input {
				// If "dataset_id" is omitted or invalid, you can error or skip as needed.
				datasetID, _ := ex["dataset_id"].(string)

				// Try to get "id" from the request; if missing, you could generate a UUID.
				// Example:
				id, ok := ex["id"].(string)
				if !ok || id == "" {
					id = uuid.New().String()
				}

				newEx := map[string]any{
					"dataset_id":        datasetID,
					"inputs":            map[string]any{"field": ex["inputs"].(map[string]any)["field"]},
					"outputs":           map[string]any{"field": ex["outputs"].(map[string]any)["field"]},
					"created_at":        ex["created_at"],
					"metadata":          ex["metadata"],
					"source_run_id":     ex["source_run_id"],
					"split":             ex["split"],
					"id":                id,
					"use_source_run_io": ex["use_source_run_io"],
					"overwrite":         ex["overwrite"],
				}

				// If "split" was completely missing, set it to "base" (of course, adapt as needed).
				if newEx["split"] == nil {
					newEx["split"] = "base"
				}

				// If "use_source_run_io" was missing, set it false
				if newEx["use_source_run_io"] == nil {
					newEx["use_source_run_io"] = false
				}

				// If "overwrite" was missing, set it false
				if newEx["overwrite"] == nil {
					newEx["overwrite"] = false
				}

				output = append(output, newEx)
			}

			w.WriteHeader(http.StatusOK)
			if err := json.NewEncoder(w).Encode(output); err != nil {
				t.Errorf("failed to encode mock JSON response: %v", err)
			}
			return
		}

		// Return 404 for anything we didn't mock
		http.NotFound(w, r)
	}))
	defer mockSmithBackend.Close()

	oldSmithBackendEndpoint := config.Env.SmithBackendEndpoint
	defer func() {
		// Revert after the test
		config.Env.SmithBackendEndpoint = oldSmithBackendEndpoint
	}()
	// Point your code to the mock server
	config.Env.SmithBackendEndpoint = mockSmithBackend.URL

	testCases := []uploadExamplesTestCase{
		{
			name:              "Valid multipart request",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.NewString(), uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_1", tc.exampleIds[1]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_2", tc.exampleIds[1]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[1],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"field": "value"},
						Outputs:   map[string]interface{}{"field": "value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_2": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Valid multipart request several attachments",
			datasetId:         f.datasetIDNoSchema,
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				multipart := make([]multipartPart, 0)
				// append the example part
				multipart = append(multipart, multipartPart{
					fieldName:   tc.exampleIds[0],
					contentType: "application/json",
					content:     []byte(`{"metadata": {"key": "value"}}`),
				})

				for i := 0; i < 7; i++ {
					attachmentName := fmt.Sprintf("%s.attachment.attachment_%d", tc.exampleIds[0], i)
					multipart = append(multipart, multipartPart{
						fieldName:   attachmentName,
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					})
				}

				return multipart
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    nil,
						Outputs:   nil,
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_2": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_3": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_4": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_5": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_6": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Valid multipart request with schema",
			datasetId:         f.datasetIDWithSchema,
			exampleIds:        []string{uuid.NewString(), uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDWithSchema,
						Inputs:    map[string]interface{}{"field": "value"},
						Outputs:   map[string]interface{}{"field": "value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:             tc.exampleIds[1],
						DatasetID:      f.datasetIDWithSchema,
						Inputs:         map[string]interface{}{"field": "value"},
						Outputs:        map[string]interface{}{"field": "value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request no inputs or outputs",
			datasetId:  f.datasetIDNoSchema,
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key2": "value2"}}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[1],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key2": "value2"},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request nil inputs or outputs",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[1],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request empty example body",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Outputs:        map[string]interface{}{"output_field": "output_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request null example body",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Outputs:        map[string]interface{}{"output_field": "output_value"},
						Metadata:       map[string]interface{}{"dataset_split": []interface{}{"base"}},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request with example_id in body",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"id": "%s", "metadata": {"key": "value"}}`, tc.exampleIds[0])),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "unknown field",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request with dataset_id in body",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "unknown field",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart wrong dataset id",
			exampleIds: []string{uuid.NewString()},
			datasetId:  "*************-0000-0000-000000000000",
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			expectedBody:   "dataset not found",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart invalid json",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid JSON part",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart invalid json in inputs",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid JSON part",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart invalid json in outputs",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid JSON part",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart schema validation error",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDWithSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": 1}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "schema validation errors",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart schema validation error nil inputs or outputs",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDWithSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "schema validation errors",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request empty",
			exampleIds: []string{},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
			},
		},
		{
			name:              "Invalid multipart request inputs outputs or attachmentUrls without example body",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "example part not seen",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request bad ordering",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "example part not seen",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request bad ordering second example",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "example part not seen",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request invalid name",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   "invalid",
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid part name",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request unsupported content encoding",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "gzip",
						},
					},
				}
			},
			expectedStatus: http.StatusUnsupportedMediaType,
			expectedBody:   "unsupported Content-Encoding",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request missing content type",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName: tc.exampleIds[0],
						content:   []byte(`{metadata": {"key": "value"}}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "missing Content-Type",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request invalid content type",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{metadata": {"key": "value"}}`),
						contentType: "invalid",
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid Content-Type",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request missing length param in attachment part",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "missing length parameter",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request invalid length param in attachment part",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=invalid",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid length parameter",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request extra field in JSON part",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "unknown field",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request large attachment disk upload",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				// 120 KB attachment
				attachmentContent := make([]byte, 120*1024)
				for i := range attachmentContent {
					attachmentContent[i] = byte(i % 256)
				}
				tc.attachmentContent = attachmentContent

				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=122880",
						content:     attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    nil,
						Outputs:   nil,
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
			forceDiskUpload: true,
		},
		{
			name:       "Valid multipart request large attachment direct upload",
			exampleIds: []string{uuid.NewString()},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				// 120 KB attachment
				attachmentContent := make([]byte, 120*1024)
				for i := range attachmentContent {
					attachmentContent[i] = byte(i % 256)
				}
				tc.attachmentContent = attachmentContent

				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=122880",
						content:     attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    nil,
						Outputs:   nil,
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
			forceDirectUpload: true,
		},
		{
			name:       "Invalid multipart request use numerical id",
			exampleIds: []string{},
			datasetId:  f.datasetIDNoSchema,
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   "1",
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   "1.inputs",
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   "1.outputs",
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid part name",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Valid multipart requests upsert pt1",
			exampleIds:        []string{"918d8122-95dc-4627-8209-ea9a477bc754"},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		// try to add an example to a dataset with transformations
		{
			name:              "Multipart request dataset with transformations",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDWithTransformations,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "input_value", "extra_field": "extra_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "output_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDWithTransformations,
						Inputs:    map[string]interface{}{"field": "input_value"},
						Outputs:   map[string]interface{}{"field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  "attachment content",
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		// add an example with a split
		{
			name:              "valid multipart request with split as list",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}, "split": ["train","test"]}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"key": "value", "dataset_split": []interface{}{"train", "test"}},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "valid multipart request with split as string",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}, "split": "train"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"key": "value", "dataset_split": []interface{}{"train"}},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "valid multipart request attachment name with periods",
			exampleIds:        []string{uuid.NewString()},
			datasetId:         f.datasetIDNoSchema,
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *uploadExamplesTestCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(`{"metadata": {"key": "value"}}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *uploadExamplesTestCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"dataset_split": []interface{}{"base"}, "key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Prepare the request parts
			parts := tc.requestParts(&tc)

			// Create the multipart request
			var body bytes.Buffer
			writer := multipart.NewWriter(&body)

			if tc.forceDirectUpload {
				// override the storage client to force direct upload
				s3, err := storage.NewS3StorageClient(false, &storage.BlobStorageClientOptions{
					SpoolMinSizeBytes: 0,
					SpoolLimitBytes:   0,
				})
				if err != nil {
					t.Fatalf("Failed to create S3 storage client: %v", err)
				}
				h.StorageClient = s3
			}

			if tc.forceDiskUpload {
				// override the storage client to force disk upload
				s3, err := storage.NewS3StorageClient(false, &storage.BlobStorageClientOptions{
					SpoolMinSizeBytes: 0,
					SpoolLimitBytes:   config.Env.SpoolLimitGB * 1024 * 1024 * 1024,
				})
				if err != nil {
					t.Fatalf("Failed to create S3 storage client: %v", err)
				}
				h.StorageClient = s3
			}

			for _, part := range parts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = writer.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}

			err := writer.Close()
			require.NoError(t, err, "Failed to close writer")

			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/datasets/%s/examples", tc.datasetId), &body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			for k, v := range headers {
				req.Header.Set(k, v)
			}

			rr := httptest.NewRecorder()

			// wrap the handler with the testlogger and auth middleware
			r := chi.NewRouter()
			r.Use(testLogger(t))
			r.Use(ah.Middleware)
			r.Post("/datasets/{dataset_id}/examples", h.UploadDatasetExamples)
			r.ServeHTTP(rr, req)

			assert.Equal(t, tc.expectedStatus, rr.Code, "UploadDatasetExamples returned wrong status code")

			if tc.expectedBody != "" {
				assert.Contains(t, rr.Body.String(), tc.expectedBody, "UploadDatasetExamples returned unexpected body")
			}

			if tc.verify != nil {
				tc.verify(t, &tc)
			}
		})
	}
}

func TestMultipartExamplesHandler_UploadExamples(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	f := setUpDatabase(t, dbpool)

	// TODO(agola11) add other auth types
	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	s3StorageClient, err := storage.NewS3StorageClient(false, nil)
	if err != nil {
		t.Fatal(err)
	}

	h := examples.MultipartExamplesHandler{
		Pg:            dbpool,
		StorageClient: s3StorageClient,
		ExamplesCRUD:  examples.NewExamplesCRUD(dbpool),
	}

	headers := map[string]string{
		"X-Api-Key": f.apiKey,
	}

	testCases := []testCase{
		{
			name:              "Valid multipart request",
			exampleIds:        []string{uuid.NewString(), uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_1", tc.exampleIds[1]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.attachment_2", tc.exampleIds[1]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "",
						},
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:        tc.exampleIds[1],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"field": "value"},
						Outputs:   map[string]interface{}{"field": "value"},
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_2": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Valid multipart request several attachments",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				multipart := make([]multipartPart, 0)
				// append the example part
				multipart = append(multipart, multipartPart{
					fieldName:   tc.exampleIds[0],
					contentType: "application/json",
					content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
				})

				for i := 0; i < 7; i++ {
					attachmentName := fmt.Sprintf("%s.attachment.attachment_%d", tc.exampleIds[0], i)
					multipart = append(multipart, multipartPart{
						fieldName:   attachmentName,
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					})
				}

				return multipart
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    nil,
						Outputs:   nil,
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.attachment_0": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_1": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_2": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_3": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_4": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_5": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
							"attachment.attachment_6": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Valid multipart request with schema",
			exampleIds:        []string{uuid.NewString(), uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDWithSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDWithSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDWithSchema,
						Inputs:    map[string]interface{}{"field": "value"},
						Outputs:   map[string]interface{}{"field": "value"},
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
					{
						ID:             tc.exampleIds[1],
						DatasetID:      f.datasetIDWithSchema,
						Inputs:         map[string]interface{}{"field": "value"},
						Outputs:        map[string]interface{}{"field": "value"},
						Metadata:       map[string]interface{}{"key": "value"},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request no inputs or outputs",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key2": "value2"}}`, f.datasetIDNoSchema)),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[1],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"key2": "value2"},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request nil inputs or outputs",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"key": "value"},
						AttachmentURLs: nil,
					},
					{
						ID:             tc.exampleIds[1],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         nil,
						Outputs:        nil,
						Metadata:       map[string]interface{}{"key": "value"},
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request array inputs",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`[{"foo": "bar"}]`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "arrays are not allowed in inputs or outputs",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request array outputs",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`[{"foo": "bar"}]`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "arrays are not allowed in inputs or outputs",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request without metadata",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s"}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:             tc.exampleIds[0],
						DatasetID:      f.datasetIDNoSchema,
						Inputs:         map[string]interface{}{"input_field": "input_value"},
						Outputs:        map[string]interface{}{"output_field": "output_value"},
						Metadata:       nil,
						AttachmentURLs: nil,
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request with example_id in body",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"id": "%s", "dataset_id": "%s", "metadata": {"key": "value"}}`, tc.exampleIds[0], f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "unknown field",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request missing dataset id",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{"metadata": {"key": "value"}}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "missing dataset_id",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart wrong dataset id",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "*************-0000-0000-000000000000", "metadata": {"key": "value"}}`)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			expectedBody:   "dataset not found",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart invalid json",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(`{`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid JSON part",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart invalid json in inputs",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid JSON part",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart invalid json in outputs",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid JSON part",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart schema validation error",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDWithSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": 1}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "schema validation errors",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart schema validation error nil inputs or outputs",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDWithSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`null`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "schema validation errors",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart inconsistent dataset id",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDWithSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "inconsistent dataset_ids in parts",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request empty",
			exampleIds: []string{},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
			},
		},
		{
			name:              "Invalid multipart request inputs outputs or attachmentUrls without example body",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "example part not seen",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request bad ordering",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "example part not seen",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request bad ordering second example",
			exampleIds: []string{uuid.NewString(), uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
					{
						fieldName:   tc.exampleIds[1],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[1]),
						contentType: "application/json",
						content:     []byte(`{"field": "value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "example part not seen",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request invalid name",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   "invalid",
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid part name",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request unsupported content encoding",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						contentType: "application/json",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"input_field": "input_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						contentType: "application/json",
						content:     []byte(`{"output_field": "output_value"}`),
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
						headers: map[string]string{
							"Content-Encoding": "gzip",
						},
					},
				}
			},
			expectedStatus: http.StatusUnsupportedMediaType,
			expectedBody:   "unsupported Content-Encoding",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request missing content type",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName: tc.exampleIds[0],
						content:   []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "missing Content-Type",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request invalid content type",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "invalid",
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid Content-Type",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request missing length param in attachment part",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "missing length parameter",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Invalid multipart request invalid length param in attachment part",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=invalid",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid length parameter",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Invalid multipart request extra field in JSON part",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}, "unknown_field": "123"}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "unknown field",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:       "Valid multipart request large attachment disk upload",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				// 120 KB attachment
				attachmentContent := make([]byte, 120*1024)
				for i := range attachmentContent {
					attachmentContent[i] = byte(i % 256)
				}
				tc.attachmentContent = attachmentContent

				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=122880",
						content:     attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    nil,
						Outputs:   nil,
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
			forceDiskUpload: true,
		},
		{
			name:       "Valid multipart request large attachment direct upload",
			exampleIds: []string{uuid.NewString()},
			requestParts: func(tc *testCase) []multipartPart {
				// 120 KB attachment
				attachmentContent := make([]byte, 120*1024)
				for i := range attachmentContent {
					attachmentContent[i] = byte(i % 256)
				}
				tc.attachmentContent = attachmentContent

				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=122880",
						content:     attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    nil,
						Outputs:   nil,
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
			forceDirectUpload: true,
		},
		{
			name:       "Invalid multipart request use numerical id",
			exampleIds: []string{},
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   "1",
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
					{
						fieldName:   "1.inputs",
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   "1.outputs",
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "invalid part name",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
		{
			name:              "Valid multipart requests upsert pt1",
			exampleIds:        []string{"918d8122-95dc-4627-8209-ea9a477bc754"},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDNoSchema)),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"input_field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"output_field": "output_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusCreated,
			expectedBody:   "",
			verify: func(t *testing.T, tc *testCase) {
				expectedExamples := []testExample{
					{
						ID:        tc.exampleIds[0],
						DatasetID: f.datasetIDNoSchema,
						Inputs:    map[string]interface{}{"input_field": "input_value"},
						Outputs:   map[string]interface{}{"output_field": "output_value"},
						Metadata:  map[string]interface{}{"key": "value"},
						AttachmentURLs: map[string]TestAttachmentInfo{
							"attachment.image": TestAttachmentInfo{
								MimeType: "application/octet-stream",
								Content:  string(tc.attachmentContent),
							},
						},
					},
				}

				verifyStoredExamples(t, dbpool, s3StorageClient, expectedExamples, tc.exampleIds, tc.verifyOrder)
			},
		},
		// try to add an example to a dataset with transformations
		{
			name:              "Invalid multipart request dataset with transformations",
			exampleIds:        []string{uuid.NewString()},
			attachmentContent: []byte("attachment content"),
			requestParts: func(tc *testCase) []multipartPart {
				return []multipartPart{
					{
						fieldName:   tc.exampleIds[0],
						content:     []byte(fmt.Sprintf(`{"dataset_id": "%s", "metadata": {"key": "value"}}`, f.datasetIDWithTransformations)),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.inputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "input_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.outputs", tc.exampleIds[0]),
						content:     []byte(`{"field": "output_value"}`),
						contentType: "application/json",
					},
					{
						fieldName:   fmt.Sprintf("%s.attachment.image", tc.exampleIds[0]),
						contentType: "application/octet-stream; length=18",
						content:     tc.attachmentContent,
					},
				}
			},
			expectedStatus: http.StatusUnprocessableEntity,
			expectedBody:   "dataset contains transformations",
			verify: func(t *testing.T, tc *testCase) {
				verifyStoredExamples(t, dbpool, s3StorageClient, []testExample{}, tc.exampleIds, tc.verifyOrder)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Prepare the request parts
			parts := tc.requestParts(&tc)

			// Create the multipart request
			var body bytes.Buffer
			writer := multipart.NewWriter(&body)

			if tc.forceDirectUpload {
				// override the storage client to force direct upload
				s3, err := storage.NewS3StorageClient(false, &storage.BlobStorageClientOptions{
					SpoolMinSizeBytes: 0,
					SpoolLimitBytes:   0,
				})
				if err != nil {
					t.Fatalf("Failed to create S3 storage client: %v", err)
				}
				h.StorageClient = s3
			}

			if tc.forceDiskUpload {
				// override the storage client to force disk upload
				s3, err := storage.NewS3StorageClient(false, &storage.BlobStorageClientOptions{
					SpoolMinSizeBytes: 0,
					SpoolLimitBytes:   config.Env.SpoolLimitGB * 1024 * 1024 * 1024,
				})
				if err != nil {
					t.Fatalf("Failed to create S3 storage client: %v", err)
				}
				h.StorageClient = s3
			}

			for _, part := range parts {
				var partWriter io.Writer
				var err error
				headers := make(textproto.MIMEHeader)
				headers.Set("Content-Disposition", fmt.Sprintf(`form-data; name="%s"`, part.fieldName))
				if part.contentType != "" {
					headers.Set("Content-Type", part.contentType)
				}
				for k, v := range part.headers {
					headers.Set(k, v)
				}
				partWriter, err = writer.CreatePart(headers)
				require.NoError(t, err, "Failed to create form part")
				_, err = partWriter.Write(part.content)
				require.NoError(t, err, "Failed to write to form part")
			}

			err := writer.Close()
			require.NoError(t, err, "Failed to close writer")

			req := httptest.NewRequest(http.MethodPost, "/", &body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			for k, v := range headers {
				req.Header.Set(k, v)
			}

			rr := httptest.NewRecorder()

			// wrap the handler with the testlogger and auth middleware
			handlerWithAuth := ah.Middleware(http.HandlerFunc(h.UploadExamples))
			handlerWithLogging := testLogger(t)(handlerWithAuth)
			handlerWithLogging.ServeHTTP(rr, req)

			assert.Equal(t, tc.expectedStatus, rr.Code, "UploadExamples returned wrong status code")

			if tc.expectedBody != "" {
				assert.Contains(t, rr.Body.String(), tc.expectedBody, "UploadExamples returned unexpected body")
			}

			if tc.verify != nil {
				tc.verify(t, &tc)
			}
		})
	}
}

// TestGetChatOutputs tests the getChatOutputs helper by simulating a runData
// that includes a "generations" array with a valid chat message (and one without).
func TestGetChatOutputs(t *testing.T) {
	t.Run("Valid chat message in generations", func(t *testing.T) {
		run := examples.RunData{
			Outputs: map[string]interface{}{
				"generations": []interface{}{
					map[string]interface{}{
						"message": map[string]interface{}{
							"type": "human",
							"data": map[string]interface{}{
								"content": "Hello, world!",
							},
						},
					},
				},
			},
		}

		outputs, err := examples.GetChatOutputs(run)
		require.NoError(t, err)
		require.NotNil(t, outputs)
		require.Contains(t, outputs, "output")
		outputMsg, ok := outputs["output"].(map[string]interface{})
		require.True(t, ok, "Expected output to be a map")
		require.Equal(t, "human", outputMsg["type"])
	})

	t.Run("No chat message in generations", func(t *testing.T) {
		run := examples.RunData{
			Outputs: map[string]interface{}{
				"generations": []interface{}{},
			},
		}
		outputs, err := examples.GetChatOutputs(run)
		require.NoError(t, err)
		expected := map[string]interface{}{
			"output": map[string]interface{}{
				"type": "",
				"data": map[string]interface{}{
					"content": "",
				},
			},
		}
		require.Equal(t, expected, outputs, "Outputs should match the expected empty content structure")

	})
}

// TestGetLLMOutputs tests the getLLMOutputs helper using different scenarios:
// a generation that has text, and another that doesn't.
func TestGetLLMOutputs(t *testing.T) {
	t.Run("Generation contains text", func(t *testing.T) {
		run := examples.RunData{
			Outputs: map[string]interface{}{
				"generations": []interface{}{
					map[string]interface{}{
						"text": "The capital of France is Paris",
					},
				},
			},
		}
		outputs, err := examples.GetLLMOutputs(run)
		require.NoError(t, err)
		require.NotNil(t, outputs)
		outputVal, ok := outputs["output"].(string)
		require.True(t, ok)
		require.Equal(t, "The capital of France is Paris", outputVal)
	})

	t.Run("Generation has no text", func(t *testing.T) {
		run := examples.RunData{
			Outputs: map[string]interface{}{
				"generations": []interface{}{
					map[string]interface{}{
						// "text" key missing
						"other": "field",
					},
				},
			},
		}
		outputs, err := examples.GetLLMOutputs(run)
		require.NoError(t, err)
		expected := map[string]interface{}{
			"output": "",
		}
		require.Equal(t, expected, outputs, "Outputs should match the expected empty string output")

	})
}

// TestGetChatInputs exercises getChatInputs by varying the runData. We test whether
// invocation_params or run.Inputs are used, and confirm messages get prepared correctly.
func TestGetChatInputs(t *testing.T) {
	t.Run("Invocation params with tools and messages", func(t *testing.T) {
		run := examples.RunData{
			Extras: map[string]interface{}{
				"invocation_params": map[string]interface{}{
					"tools": []interface{}{
						map[string]interface{}{
							"type": "function",
							"function": map[string]interface{}{
								"name":       "testFunc",
								"parameters": "some params",
							},
						},
					},
				},
			},
			Inputs: map[string]interface{}{
				"messages": []interface{}{
					map[string]interface{}{
						"role":    "system",
						"content": "System prompt",
					},
				},
			},
		}

		inputs, err := examples.GetChatInputs(run)
		require.NoError(t, err)
		require.NotNil(t, inputs)

		// Confirm we have "input" (the messages) and "functions" (extracted tools)
		msgVal, hasMessages := inputs["input"]
		require.True(t, hasMessages, "Expected key 'input' in the returned map")
		_, ok := msgVal.([]interface{})
		require.True(t, ok, "Expected 'input' to be a slice")

		funcsVal, hasFuncs := inputs["functions"]
		require.True(t, hasFuncs, "Expected 'functions' in the returned map")
		funcsSlice, ok2 := funcsVal.([]interface{})
		require.True(t, ok2, "Expected 'functions' to be a slice")
		require.Len(t, funcsSlice, 1)
	})

	t.Run("No invocation params, using run.Inputs for messages", func(t *testing.T) {
		run := examples.RunData{
			Inputs: map[string]interface{}{
				"messages": []interface{}{
					map[string]interface{}{
						"role":    "user",
						"content": "Hello, system!",
					},
				},
			},
		}

		inputs, err := examples.GetChatInputs(run)
		require.NoError(t, err)
		require.NotNil(t, inputs)
		msgVal, hasInput := inputs["input"]
		require.True(t, hasInput, "Expected 'input' in the map")
		msgSlice, ok := msgVal.([]interface{})
		require.True(t, ok)
		require.Len(t, msgSlice, 1)

		// Functions/tools should not be defined here
		_, hasTools := inputs["functions"]
		require.False(t, hasTools)
	})
}

// TestGetLLMInputs checks that getLLMInputs picks up a prompt from inputs["prompt"]
// or, if missing, from the first element of inputs["prompts"].
func TestGetLLMInputs(t *testing.T) {
	t.Run("Prompt key exists", func(t *testing.T) {
		run := examples.RunData{
			Inputs: map[string]interface{}{
				"prompt": "What is the capital of France?",
			},
		}
		inputs, err := examples.GetLLMInputs(run)
		require.NoError(t, err)
		require.NotNil(t, inputs)
		require.Equal(t, "What is the capital of France?", inputs["input"])
	})

	t.Run("Prompt missing, check prompts array", func(t *testing.T) {
		run := examples.RunData{
			Inputs: map[string]interface{}{
				"prompts": []interface{}{
					"What's the weather?",
					"Second entry ignored",
				},
			},
		}
		inputs, err := examples.GetLLMInputs(run)
		require.NoError(t, err)
		require.NotNil(t, inputs)
		require.Equal(t, "What's the weather?", inputs["input"])
	})

	t.Run("Neither prompt nor prompts present", func(t *testing.T) {
		run := examples.RunData{
			Inputs: map[string]interface{}{},
		}
		inputs, err := examples.GetLLMInputs(run)
		require.NoError(t, err)
		require.NotNil(t, inputs)
		// The "input" field should be nil/empty because nothing is found.
		require.Nil(t, inputs["input"])
	})
}

func TestSafeGet(t *testing.T) {
	tests := []struct {
		name       string
		start      interface{}
		defaultVal interface{}
		path       []interface{}
		want       interface{}
	}{
		{
			name:       "nil start returns default",
			start:      nil,
			defaultVal: "default",
			path:       []interface{}{"foo"},
			want:       "default",
		},
		{
			name:       "map with one level, key exists",
			start:      map[string]interface{}{"foo": "bar"},
			defaultVal: "default",
			path:       []interface{}{"foo"},
			want:       "bar",
		},
		{
			name:       "map with one level, key not exists",
			start:      map[string]interface{}{"foo": "bar"},
			defaultVal: "default",
			path:       []interface{}{"baz"},
			want:       "default",
		},
		{
			name: "map with nested map, key path exists all levels",
			start: map[string]interface{}{
				"outer": map[string]interface{}{
					"inner": "value",
				},
			},
			defaultVal: "default",
			path:       []interface{}{"outer", "inner"},
			want:       "value",
		},
		{
			name: "map with nested map, missing key in path",
			start: map[string]interface{}{
				"outer": map[string]interface{}{
					"inner": "value",
				},
			},
			defaultVal: "default",
			path:       []interface{}{"outer", "nope"},
			want:       "default",
		},
		{
			name:       "map where second key is not string",
			start:      map[string]interface{}{"foo": "bar"},
			defaultVal: "default",
			path:       []interface{}{123},
			want:       "default",
		},
		{
			name:       "slice index within bounds",
			start:      []interface{}{"one", "two", "three"},
			defaultVal: "default",
			path:       []interface{}{1},
			want:       "two",
		},
		{
			name:       "slice index out of bounds",
			start:      []interface{}{"one", "two", "three"},
			defaultVal: "default",
			path:       []interface{}{5},
			want:       "default",
		},
		{
			name:       "slice nested in map",
			start:      map[string]interface{}{"numbers": []interface{}{10, 20, 30}},
			defaultVal: 999,
			path:       []interface{}{"numbers", 2},
			want:       30,
		},
		{
			name: "cannot continue if intermediate is not map or array",
			start: map[string]interface{}{
				"numbers": 42,
			},
			defaultVal: "default",
			path:       []interface{}{"numbers", 0},
			want:       "default",
		},
		{
			name:       "empty path returns the start",
			start:      map[string]interface{}{"foo": "bar"},
			defaultVal: "default",
			path:       []interface{}{},
			want:       map[string]interface{}{"foo": "bar"},
		},
	}
	for _, tt := range tests {
		tt := tt // capture range var
		t.Run(tt.name, func(t *testing.T) {
			got := examples.SafeGet(tt.start, tt.defaultVal, tt.path...)
			assert.Equal(t, tt.want, got, "SafeGet() mismatch")
		})
	}
}

func TestIsOpenAITool(t *testing.T) {
	tests := []struct {
		name string
		x    interface{}
		want bool
	}{
		{
			name: "valid openai tool",
			x: map[string]interface{}{
				"type":     "function",
				"function": map[string]interface{}{"name": "myTool", "parameters": "someSchema"},
			},
			want: true,
		},
		{
			name: "missing type",
			x: map[string]interface{}{
				"function": map[string]interface{}{"name": "myTool", "parameters": "someSchema"},
			},
			want: false,
		},
		{
			name: "type not function",
			x: map[string]interface{}{
				"type":     "notAFunction",
				"function": map[string]interface{}{"name": "myTool", "parameters": "someSchema"},
			},
			want: false,
		},
		{
			name: "missing 'function' key",
			x: map[string]interface{}{
				"type": "function",
			},
			want: false,
		},
		{
			name: "not a map",
			x:    []string{"function", "someSchema"},
			want: false,
		},
		{
			name: "function is invalid type",
			x: map[string]interface{}{
				"type":     "function",
				"function": "stringInsteadOfMap",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.IsOpenAITool(tt.x)
			assert.Equal(t, tt.want, got, "IsOpenAITool() mismatch")
		})
	}
}

func TestIsOpenAIFunctions(t *testing.T) {
	tests := []struct {
		name string
		x    interface{}
		want bool
	}{
		{
			name: "valid list of openai functions",
			x: []interface{}{
				map[string]interface{}{"name": "f1", "parameters": "p1"},
				map[string]interface{}{"name": "f2", "parameters": "p2"},
			},
			want: true,
		},
		{
			name: "empty slice is valid (all zero items pass)",
			x:    []interface{}{},
			want: true,
		},
		{
			name: "not a slice",
			x:    map[string]interface{}{"name": "f1", "parameters": "p1"},
			want: false,
		},
		{
			name: "one invalid function in slice",
			x: []interface{}{
				map[string]interface{}{"name": "f1", "parameters": "p1"},
				map[string]interface{}{"foo": "bar"},
			},
			want: false,
		},
		{
			name: "items not all maps",
			x:    []interface{}{"someString", 123},
			want: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.IsOpenAIFunctions(tt.x)
			assert.Equal(t, tt.want, got, "IsOpenAIFunctions() mismatch")
		})
	}
}

func TestIsOpenAITools(t *testing.T) {
	tests := []struct {
		name string
		x    interface{}
		want bool
	}{
		{
			name: "valid list of openai tools",
			x: []interface{}{
				map[string]interface{}{
					"type": "function",
					"function": map[string]interface{}{
						"name":       "tool1",
						"parameters": "schema1",
					},
				},
				map[string]interface{}{
					"type": "function",
					"function": map[string]interface{}{
						"name":       "tool2",
						"parameters": "schema2",
					},
				},
			},
			want: true,
		},
		{
			name: "empty slice is valid (all zero items pass)",
			x:    []interface{}{},
			want: true,
		},
		{
			name: "not a slice",
			x:    map[string]interface{}{"type": "function"},
			want: false,
		},
		{
			name: "one invalid tool in slice",
			x: []interface{}{
				map[string]interface{}{
					"type": "function",
					"function": map[string]interface{}{
						"name":       "tool1",
						"parameters": "schema1",
					},
				},
				map[string]interface{}{"foo": "bar"},
			},
			want: false,
		},
		{
			name: "items not all maps",
			x:    []interface{}{"someString", 123},
			want: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.IsOpenAITools(tt.x)
			assert.Equal(t, tt.want, got, "IsOpenAITools() mismatch")
		})
	}
}

func TestIsOpenAIFunction(t *testing.T) {
	tests := []struct {
		name string
		x    interface{}
		want bool
	}{
		{
			name: "valid openai function",
			x: map[string]interface{}{
				"name":       "testFunc",
				"parameters": map[string]interface{}{"some": "schema"},
			},
			want: true,
		},
		{
			name: "missing name",
			x: map[string]interface{}{
				"parameters": map[string]interface{}{"some": "schema"},
			},
			want: false,
		},
		{
			name: "missing parameters",
			x: map[string]interface{}{
				"name": "testFunc",
			},
			want: false,
		},
		{
			name: "name is not string",
			x: map[string]interface{}{
				"name":       123,
				"parameters": "schema",
			},
			want: false,
		},
		{
			name: "not a map",
			x:    []interface{}{"testFunc", "schema"},
			want: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.IsOpenAIFunction(tt.x)
			assert.Equal(t, tt.want, got, "IsOpenAIFunction() mismatch")
		})
	}
}

func TestGetMessageType(t *testing.T) {
	tests := []struct {
		name    string
		message interface{}
		want    string
	}{
		{
			name:    "nil message returns empty",
			message: nil,
			want:    "",
		},
		{
			name: "map with 'type' field used as message type",
			message: map[string]interface{}{
				"type": "system",
			},
			want: "system",
		},
		{
			name: "map with 'role' = user, plus content => returns 'human'",
			message: map[string]interface{}{
				"role":    "user",
				"content": "some content",
			},
			want: "human",
		},
		{
			name: "map with 'role' = assistant, plus content => returns 'ai'",
			message: map[string]interface{}{
				"role":    "assistant",
				"content": "some content",
			},
			want: "ai",
		},
		{
			name: "map with unknown role => returns same role but lowercased",
			message: map[string]interface{}{
				"role": "unknownRole",
			},
			want: "unknownrole",
		},
		{
			name:    "slice with two elements, recognized role (human)",
			message: []interface{}{"human", "Hello!"},
			want:    "human",
		},
		{
			name:    "slice with two elements, unrecognized role => returns first element lowercased",
			message: []interface{}{"somethingElse", "Hello!"},
			want:    "somethingelse",
		},
		{
			name:    "slice with non-string first element => empty result",
			message: []interface{}{123, "Hello!"},
			want:    "",
		},
		{
			name: "map with both 'type' and 'role' => 'type' takes precedence",
			message: map[string]interface{}{
				"type": "function",
				"role": "user",
			},
			want: "function",
		},
		{
			name: "object with 'lc' and id array => last item 'FooMessage' => becomes 'foo'",
			message: map[string]interface{}{
				"lc": true,
				"id": []interface{}{"BarMessage", "FooMessage"},
			},
			want: "foo",
		},
		{
			name: "object with 'lc' and partial leftover => ends with 'messagechunk' => becomes 'some'",
			message: map[string]interface{}{
				"lc": true,
				"id": []interface{}{"SomeMessagechunk"},
			},
			want: "some",
		},
		{
			name: "map with role and function_call => openai->LS => 'ai'",
			message: map[string]interface{}{
				"role":          "assistant",
				"function_call": "someFunc",
			},
			want: "ai",
		},
		{
			name: "map with role=user but no content => fallback => 'user'",
			message: map[string]interface{}{
				"role": "user",
			},
			want: "user",
		},
		{
			name: "object with 'type' that ends with 'messagechunk' => strip => lowercased",
			message: map[string]interface{}{
				"type": "FunctionMessagechunk",
			},
			want: "function",
		},
		{
			name:    "tuple with one item => not a valid (str, str) => returns empty",
			message: []interface{}{"human"},
			want:    "",
		},
		{
			name: "explicit check for nil 'type' => fallback => empty",
			message: map[string]interface{}{
				"type": nil,
			},
			want: "",
		},
		{
			name: "object with 'lc' and empty id => no last item => empty",
			message: map[string]interface{}{
				"lc": true,
				"id": []interface{}{},
			},
			want: "",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.GetMessageType(tt.message)
			assert.Equal(t, tt.want, got, "getMessageType() mismatch")
		})
	}
}

func TestGetMessageFields(t *testing.T) {
	tests := []struct {
		name    string
		message interface{}
		want    map[string]interface{}
	}{
		{
			name:    "nil message => nil",
			message: nil,
			want:    nil,
		},
		{
			name: "map with role and content => normal extraction",
			message: map[string]interface{}{
				"role":    "user",
				"content": "Hello World",
				"extra":   "stuff",
			},
			want: map[string]interface{}{
				"content": "Hello World",
				"additional_kwargs": map[string]interface{}{
					"extra": "stuff",
				},
			},
		},
		{
			name: "map without role => same map returned",
			message: map[string]interface{}{
				"something": "else",
			},
			want: map[string]interface{}{
				"something": "else",
			},
		},
		{
			name:    "slice with two elements => role, content style",
			message: []interface{}{"human", "Greetings"},
			want: map[string]interface{}{
				"content": "Greetings",
			},
		},
		{
			name: "slice with 1 element => nil",
			message: []interface{}{
				"human",
			},
			want: nil,
		},
		{
			name:    "slice with more than 2 elements => nil",
			message: []interface{}{"human", "content", "extra"},
			want:    nil,
		},
		{
			name: "map with lc => returns kwargs",
			message: map[string]interface{}{
				"lc": true,
				"kwargs": map[string]interface{}{
					"foo": "bar",
				},
			},
			want: map[string]interface{}{
				"foo": "bar",
			},
		},
		{
			name: "map with data => returns data",
			message: map[string]interface{}{
				"data": map[string]interface{}{
					"foo": "bar",
				},
			},
			want: map[string]interface{}{
				"foo": "bar",
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.GetMessageFields(tt.message)
			assert.Equal(t, tt.want, got, "getMessageFields() mismatch")
		})
	}
}

func TestGetMessageAsStoredMessage(t *testing.T) {
	tests := []struct {
		name    string
		message interface{}
		want    map[string]interface{}
	}{
		{
			name:    "nil => nil",
			message: nil,
			want:    nil,
		},
		{
			name: "valid role + content map => stored message",
			message: map[string]interface{}{
				"role":    "assistant",
				"content": "Hello from assistant",
			},
			want: map[string]interface{}{
				"type": "ai",
				"data": map[string]interface{}{
					"content":           "Hello from assistant",
					"additional_kwargs": map[string]interface{}{},
				},
			},
		},
		{
			name: "valid type + content => stored message uses type over role",
			message: map[string]interface{}{
				"type":    "function",
				"role":    "assistant",
				"content": "some function content",
			},
			want: map[string]interface{}{
				"type": "function",
				"data": map[string]interface{}{
					"content":           "some function content",
					"additional_kwargs": map[string]interface{}{},
				},
			},
		},
		{
			name:    "invalid => no recognized type => nil",
			message: "just a string",
			want:    nil,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.GetMessageAsStoredMessage(tt.message)
			assert.Equal(t, tt.want, got, "getMessageAsStoredMessage() mismatch")
		})
	}
}

func TestGetLangSmithRoleFromOpenAIRole(t *testing.T) {
	tests := []struct {
		name string
		role string
		want string
	}{
		{
			name: "user => human",
			role: "user",
			want: "human",
		},
		{
			name: "assistant => ai",
			role: "assistant",
			want: "ai",
		},
		{
			name: "other => same string",
			role: "system",
			want: "system",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.GetLangSmithRoleFromOpenAIRole(tt.role)
			assert.Equal(t, tt.want, got, "getLangSmithRoleFromOpenAIRole() mismatch")
		})
	}
}

func TestIsAcceptedRole(t *testing.T) {
	tests := []struct {
		name string
		role string
		want bool
	}{
		{
			name: "human is accepted",
			role: "human",
			want: true,
		},
		{
			name: "assistant is accepted",
			role: "assistant",
			want: true,
		},
		{
			name: "system is accepted",
			role: "system",
			want: true,
		},
		{
			name: "unknown role => false",
			role: "someRandomRole",
			want: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			got := examples.IsAcceptedRole(tt.role)
			assert.Equal(t, tt.want, got, "isAcceptedRole() mismatch")
		})
	}
}
