package examples

import (
	"encoding/json"
	"net/http"

	"github.com/ggicci/httpin"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/database"

	"langchain.com/smith/auth"
)

type FetchExamplesHandler struct {
	Pg *database.AuditLoggedPool
}

const listExamplesQuery = `
SELECT examples_tagged.inputs, examples_tagged.outputs
FROM examples_tagged
INNER JOIN dataset ON examples_tagged.dataset_id = dataset.id
WHERE examples_tagged.dataset_id = $1 and examples_tagged.tag = $2 and dataset.tenant_id = $3
ORDER BY examples_tagged.modified_at desc
LIMIT $4 OFFSET $5`

type Example struct {
	Inputs  json.RawMessage `json:"inputs"`
	Outputs json.RawMessage `json:"outputs"`
}

type ListExamplesReq struct {
	Dataset string `in:"query=dataset;nonzero"`
	AsOf    string `in:"query=as_of;default=latest"`

	Limit  int `in:"query=limit;default=10"`
	Offset int `in:"query=offset;default=0"`
}

func (h *FetchExamplesHandler) ListExamples(w http.ResponseWriter, r *http.Request) {
	req := r.Context().Value(httpin.Input).(*ListExamplesReq)
	auth := auth.GetAuthInfo(r)

	// validate input
	if req.Limit > 100 || req.Limit < 0 {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]string{"error": "limit must be <= 100 and >= 0"})
		return
	} else if req.Offset < 0 {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]string{"error": "offset must be >= 0"})
		return
	}

	// fetch examples
	rows, _ := h.Pg.Query(r.Context(), listExamplesQuery, req.Dataset, req.AsOf, auth.TenantID, req.Limit, req.Offset)
	examples, err := pgx.CollectRows(rows, pgx.RowToStructByPos[Example])
	if err == pgx.ErrNoRows {
		render.Status(r, http.StatusNotFound)
		render.JSON(w, r, map[string]string{"error": "Not found"})
		return
	} else if err != nil {
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]string{"error": err.Error()})
		return
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, examples)
}
