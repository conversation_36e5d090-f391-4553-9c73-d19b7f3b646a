package examples

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/xeipuuv/gojsonschema"
	"langchain.com/smith/database"
)

type Split []string

// UnmarshalJSON implements json.Unmarshaler interface
func (s *Split) UnmarshalJSON(data []byte) error {
	// Try as string first
	var str string
	if err := json.Unmarshal(data, &str); err == nil {
		*s = []string{str}
		return nil
	}

	// If not string, try as string slice
	var slice []string
	if err := json.Unmarshal(data, &slice); err != nil {
		return fmt.Errorf("split must be either a string or array of strings")
	}
	*s = slice
	return nil
}

type AttachmentInfo struct {
	MimeType   string `json:"mime_type"`
	StorageURL string `json:"storage_url"`
}

type exampleInfoBase struct {
	inputs         map[string]interface{}
	outputs        map[string]interface{}
	inputsBytes    []byte
	outputsBytes   []byte
	attachmentUrls map[string]AttachmentInfo
	sourceRunID    uuid.UUID
}

type ExampleInfoCommon interface {
	SetInputs(map[string]interface{})
	GetInputs() map[string]interface{}
	GetInputsBytes() []byte
	SetInputsBytes([]byte)

	SetOutputs(map[string]interface{})
	GetOutputs() map[string]interface{}
	GetOutputsBytes() []byte
	SetOutputsBytes([]byte)

	GetAttachmentUrls() map[string]AttachmentInfo
	SetAttachmentUrls(map[string]AttachmentInfo)

	GetID() string
	GetDatasetID() string
}

type TimeUTC struct {
	time.Time
}

func (t *TimeUTC) UnmarshalJSON(data []byte) error {
	// Trim quotes from JSON string
	timeStr := strings.Trim(string(data), `"`)
	if timeStr == "" {
		// If the field is empty, just return
		return nil
	}

	// Attempt to parse in RFC3339 first (requires timezone)
	parsedTime, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		// If parse failed, try a layout without the timezone
		layoutNoTZ := "2006-01-02T15:04:05"
		parsedTime, err = time.Parse(layoutNoTZ, timeStr)
		if err != nil {
			return fmt.Errorf("TimeUTC UnmarshalJSON error: %w", err)
		}
		// Set the location to UTC when missing
		parsedTime = time.Date(
			parsedTime.Year(),
			parsedTime.Month(),
			parsedTime.Day(),
			parsedTime.Hour(),
			parsedTime.Minute(),
			parsedTime.Second(),
			parsedTime.Nanosecond(),
			time.UTC,
		)
	}
	t.Time = parsedTime
	return nil
}

var (
	// 404 Not Found
	errDatasetNotFound  = errors.New("dataset not found")
	errExamplesNotFound = errors.New("examples not found")

	// 409 Conflict
	errExampleAlreadyExists   = errors.New("example already exists")
	errDatasetExampleMismatch = errors.New("dataset id does not match example's dataset id")

	// 422 Unprocessable Entity
	errInvalidDataset   = errors.New("invalid dataset") // throw this if dataset contains transformations
	errArraysNotAllowed = errors.New("arrays are not allowed in inputs or outputs")

	// 500 Internal Server Error
	errInsertPostgresFailure = errors.New("failed to insert examples into Postgres")
	errInvalidJsonPart       = errors.New("invalid JSON part")
)

func validateInputsAndOutputs(mapOrBytes interface{}) error {
	// Check if mapOrBytes itself is an array
	if _, isArray := mapOrBytes.([]interface{}); isArray {
		return errArraysNotAllowed
	}

	if bytes, ok := mapOrBytes.([]byte); ok {
		for i := 0; i < len(bytes); i++ {
			if !isWhitespace(bytes[i]) {
				if bytes[i] == '[' {
					return errArraysNotAllowed
				}
				break
			}
		}
	}
	return nil
}

// Function to validate updateExampleInfo
func validateInputsAndOutputsForUpdate(data map[string]*updateExampleInfo) error {
	for _, info := range data {
		// Check both inputs and outputs
		if info.inputsBytes != nil {
			if err := validateInputsAndOutputs(info.inputsBytes); err != nil {
				return err
			}
		}
		if info.inputs != nil {
			if err := validateInputsAndOutputs(info.inputs); err != nil {
				return err
			}
		}
		if info.outputsBytes != nil {
			if err := validateInputsAndOutputs(info.outputsBytes); err != nil {
				return err
			}
		}
		if info.outputs != nil {
			if err := validateInputsAndOutputs(info.outputs); err != nil {
				return err
			}
		}
	}
	return nil
}

// Function to validate exampleInfo
func validateInputsAndOutputsForCreate(data map[string]*exampleInfo) error {
	for _, info := range data {
		// Check both inputs and outputs
		if info.inputsBytes != nil {
			if err := validateInputsAndOutputs(info.inputsBytes); err != nil {
				return err
			}
		}
		if info.inputs != nil {
			if err := validateInputsAndOutputs(info.inputs); err != nil {
				return err
			}
		}
		if info.outputsBytes != nil {
			if err := validateInputsAndOutputs(info.outputsBytes); err != nil {
				return err
			}
		}
		if info.outputs != nil {
			if err := validateInputsAndOutputs(info.outputs); err != nil {
				return err
			}
		}
	}
	return nil
}

// isWhitespace returns true if the byte is a JSON whitespace character
func isWhitespace(b byte) bool {
	return b == ' ' || b == '\t' || b == '\n' || b == '\r'
}

type datasetExampleCreate struct {
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	Split          *Split                 `json:"split,omitempty"`
	CreatedAt      *TimeUTC               `json:"created_at,omitempty"`
	ModifiedAt     *TimeUTC               `json:"modified_at,omitempty"`
	SourceRunID    *string                `json:"source_run_id,omitempty"`
	UseSourceRunIO bool                   `json:"use_source_run_io"`
}

type exampleCreate struct {
	DatasetID      string                 `json:"dataset_id"` // required
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	Split          *Split                 `json:"split,omitempty"`
	CreatedAt      *TimeUTC               `json:"created_at,omitempty"`
	ModifiedAt     *TimeUTC               `json:"modified_at,omitempty"`
	SourceRunID    *string                `json:"source_run_id,omitempty"`
	UseSourceRunIO bool                   `json:"use_source_run_io"`
}

type datasetExampleUpdate struct {
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Split       *Split                 `json:"split,omitempty"`
	SourceRunID string                 `json:"source_run_id,omitempty"`
}

type exampleUpdate struct {
	DatasetID   string
	Metadata    map[string]interface{}
	Split       *Split
	SourceRunID string
}

type exampleCreateWithID struct {
	id            string
	exampleCreate *exampleCreate
}

type exampleUpdateWithID struct {
	id            string
	exampleUpdate *exampleUpdate
}

type exampleInfo struct {
	exampleInfoBase
	exampleCreateWithID *exampleCreateWithID
}

func (info *exampleInfo) SetInputs(inputs map[string]interface{}) {
	info.inputs = inputs
}

func (info *exampleInfo) GetInputs() map[string]interface{} {
	return info.inputs
}

func (info *exampleInfo) GetInputsBytes() []byte {
	return info.inputsBytes
}

func (info *exampleInfo) SetInputsBytes(inputsBytes []byte) {
	info.inputsBytes = inputsBytes
}

func (info *exampleInfo) SetOutputs(outputs map[string]interface{}) {
	info.outputs = outputs
}

func (info *exampleInfo) GetOutputs() map[string]interface{} {
	return info.outputs
}

func (info *exampleInfo) GetOutputsBytes() []byte {
	return info.outputsBytes
}

func (info *exampleInfo) SetOutputsBytes(outputsBytes []byte) {
	info.outputsBytes = outputsBytes
}

func (info *exampleInfo) GetAttachmentUrls() map[string]AttachmentInfo {
	return info.attachmentUrls
}

func (info *exampleInfo) SetAttachmentUrls(attachmentUrls map[string]AttachmentInfo) {
	info.attachmentUrls = attachmentUrls
}

func (info *exampleInfo) GetID() string {
	return info.exampleCreateWithID.id
}

func (info *exampleInfo) GetDatasetID() string {
	return info.exampleCreateWithID.exampleCreate.DatasetID
}

type attachmentsOperations struct {
	Retain []string          `json:"retain"`
	Rename map[string]string `json:"rename"`
	// anything not renamed or retained will be deleted
}

type updateExampleInfo struct {
	exampleInfoBase
	exampleUpdateWithID   *exampleUpdateWithID
	attachmentsOperations *attachmentsOperations
}

func (info *updateExampleInfo) SetInputs(inputs map[string]interface{}) {
	info.exampleInfoBase.inputs = inputs
}

func (info *updateExampleInfo) GetInputs() map[string]interface{} {
	return info.inputs
}

func (info *updateExampleInfo) GetInputsBytes() []byte {
	return info.inputsBytes
}

func (info *updateExampleInfo) SetInputsBytes(inputsBytes []byte) {
	info.inputsBytes = inputsBytes
}

func (info *updateExampleInfo) SetOutputs(outputs map[string]interface{}) {
	info.outputs = outputs
}

func (info *updateExampleInfo) GetOutputs() map[string]interface{} {
	return info.outputs
}

func (info *updateExampleInfo) GetOutputsBytes() []byte {
	return info.outputsBytes
}

func (info *updateExampleInfo) SetOutputsBytes(outputsBytes []byte) {
	info.outputsBytes = outputsBytes
}

func (info *updateExampleInfo) GetAttachmentUrls() map[string]AttachmentInfo {
	return info.attachmentUrls
}

func (info *updateExampleInfo) SetAttachmentUrls(attachmentUrls map[string]AttachmentInfo) {
	info.attachmentUrls = attachmentUrls
}

func (info *updateExampleInfo) GetID() string {
	return info.exampleUpdateWithID.id
}

func (info *updateExampleInfo) GetDatasetID() string {
	return info.exampleUpdateWithID.exampleUpdate.DatasetID
}

type ExamplesCreatedResponse struct {
	ExampleIDs []uuid.UUID `json:"example_ids" example:"[\"123e4567-e89b-12d3-a456-************\"]"`
	Count      int         `json:"count" example:"1"`
}

// ExampleUploadResponse represents the response for a successful upload

type ExamplesUpdatedResponse struct {
	ExampleIDs []uuid.UUID `json:"example_ids" example:"[\"123e4567-e89b-12d3-a456-************\"]"`
	Count      int         `json:"count" example:"1"`
}

type ExamplesCRUD struct {
	Pg *database.AuditLoggedPool
}

type datasetInfo struct {
	id                      string
	dataType                string
	inputsSchemaDefinition  gojsonschema.JSONLoader
	outputsSchemaDefinition gojsonschema.JSONLoader
	hasTransformations      bool
}

func NewExamplesCRUD(pg *database.AuditLoggedPool) *ExamplesCRUD {
	return &ExamplesCRUD{
		Pg: pg,
	}
}

func parseExampleUUIDs(examplesIds []string) ([]uuid.UUID, map[string]uuid.UUID, error) {
	exampleUUIDs := make([]uuid.UUID, 0, len(examplesIds))
	parsedUUIDs := make(map[string]uuid.UUID)
	for _, id := range examplesIds {
		uid, err := uuid.Parse(id)
		if err != nil {
			return nil, nil, fmt.Errorf("%w: invalid UUID %s: %s", errInvalidJsonPart, id, err)
		}
		exampleUUIDs = append(exampleUUIDs, uid)
		parsedUUIDs[id] = uid
	}
	return exampleUUIDs, parsedUUIDs, nil
}

func (crud *ExamplesCRUD) bulkUpdateExamples(ctx context.Context, examplesData map[string]*updateExampleInfo, tenantID string) ([]uuid.UUID, error) {
	// select existing examples and insert new examples in a single transaction
	tx, err := crud.Pg.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
	}

	// this will rollback unless committed later
	defer tx.Rollback(ctx)

	exampleIds := make([]string, len(examplesData))
	i := 0
	for k := range examplesData {
		exampleIds[i] = k
		i++
	}
	exampleUUIDs, parsedUUIDs, err := parseExampleUUIDs(exampleIds)
	if err != nil {
		return nil, err
	}

	err = validateInputsAndOutputsForUpdate(examplesData)
	if err != nil {
		return nil, err
	}

	var rows pgx.Rows
	const exampleSQL = `
		SELECT DISTINCT ON (el.id) 
       	el.id, el.inputs, el.outputs, el.attachment_urls, el.metadata, el.created_at, el.source_run_id
		FROM examples_log el
		JOIN dataset d ON el.dataset_id = d.id
		WHERE el.id = ANY($1) 
		AND d.tenant_id = $2
		ORDER BY el.id, el.modified_at DESC;
	`
	rows, err = tx.Query(ctx, exampleSQL, exampleUUIDs, tenantID)

	if err != nil {
		return nil, fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
	}
	defer rows.Close()

	// Keys are the ids of the existing examples
	existingData := make(map[string]struct {
		inputs         interface{}
		outputs        interface{}
		attachmentUrls map[string]interface{} // Need to be backwards compatible
		metadata       interface{}
		createdAt      time.Time
		sourceRunID    uuid.UUID
	})

	for rows.Next() {
		var (
			id             uuid.UUID
			inputs         interface{}
			outputs        interface{}
			attachmentUrls map[string]interface{}
			metadata       interface{}
			createdAt      time.Time
			sourceRunID    uuid.UUID
		)
		if err := rows.Scan(&id, &inputs, &outputs, &attachmentUrls, &metadata, &createdAt, &sourceRunID); err != nil {
			return nil, fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
		}
		existingData[id.String()] = struct {
			inputs         interface{}
			outputs        interface{}
			attachmentUrls map[string]interface{}
			metadata       interface{}
			createdAt      time.Time
			sourceRunID    uuid.UUID
		}{inputs, outputs, attachmentUrls, metadata, createdAt, sourceRunID}
	}

	var missingIDs []string
	for id := range examplesData {
		if _, exists := existingData[id]; !exists {
			missingIDs = append(missingIDs, id)
		}
	}
	if len(missingIDs) > 0 {
		return nil, fmt.Errorf("%w: %v", errExamplesNotFound, missingIDs)
	}

	columns := []string{
		"id",
		"dataset_id",
		"inputs",
		"outputs",
		"metadata",
		"attachment_urls",
		"created_at",
		"source_run_id",
	}

	insertRows := make([][]interface{}, 0, len(examplesData))
	insertedIds := make([]uuid.UUID, 0, len(examplesData))

	// prepare insert rows
	for id, updateInfo := range examplesData {
		if updateInfo.exampleUpdateWithID == nil || updateInfo.exampleUpdateWithID.exampleUpdate == nil {
			return nil, fmt.Errorf("%w: encountered unexpected nil value", errInvalidJsonPart)
		}
		if updateInfo.exampleUpdateWithID.id == "" || updateInfo.exampleUpdateWithID.exampleUpdate.DatasetID == "" {
			return nil, fmt.Errorf("%w: example_id and dataset_id are required for all example parts", errInvalidJsonPart)
		}

		uid := parsedUUIDs[id]

		datasetID, err := uuid.Parse(updateInfo.exampleUpdateWithID.exampleUpdate.DatasetID)
		if err != nil {
			return nil, fmt.Errorf("%w: invalid dataset UUID %s: %s", errInsertPostgresFailure, updateInfo.exampleUpdateWithID.exampleUpdate.DatasetID, err)
		}
		// For patch operations, preserve existing data if not provided
		existingInfo := existingData[id]

		var inputs, outputs interface{}

		if updateInfo.inputs != nil {
			inputs = updateInfo.inputs
		} else if updateInfo.inputsBytes != nil {
			inputs = updateInfo.inputsBytes
		} else {
			inputs = existingInfo.inputs
		}

		if updateInfo.outputs != nil {
			outputs = updateInfo.outputs
		} else if updateInfo.outputsBytes != nil {
			outputs = updateInfo.outputsBytes
		} else {
			outputs = existingInfo.outputs
		}

		var metadata map[string]interface{}

		if updateInfo.exampleUpdateWithID.exampleUpdate.Metadata != nil {
			metadata = updateInfo.exampleUpdateWithID.exampleUpdate.Metadata
		} else if existingInfo.metadata != nil {
			metadata = existingInfo.metadata.(map[string]interface{})
		} else {
			metadata = make(map[string]interface{})
		}

		if updateInfo.exampleUpdateWithID.exampleUpdate.Split != nil {
			metadata["dataset_split"] = *updateInfo.exampleUpdateWithID.exampleUpdate.Split
		} else if existingInfo.metadata != nil {
			if existingSplit, ok := existingInfo.metadata.(map[string]interface{})["dataset_split"]; ok {
				metadata["dataset_split"] = existingSplit
			}
		}

		var sourceRunID *string

		if updateInfo.exampleUpdateWithID.exampleUpdate.SourceRunID != "" {
			sourceRunID = &updateInfo.exampleUpdateWithID.exampleUpdate.SourceRunID
		} else if existingInfo.sourceRunID != uuid.Nil {
			sourceRunIDStr := existingInfo.sourceRunID.String()
			sourceRunID = &sourceRunIDStr
		}

		attachmentUrls := make(map[string]interface{})
		if updateInfo.attachmentUrls == nil && updateInfo.attachmentsOperations.Rename == nil && updateInfo.attachmentsOperations.Retain == nil {
			attachmentUrls = existingInfo.attachmentUrls
		} else {
			// Validate that all Retain and Rename elements exist in the current attachments
			if updateInfo.attachmentsOperations.Retain != nil || updateInfo.attachmentsOperations.Rename != nil {
				existingAttachments := make(map[string]bool)
				for rawName := range existingInfo.attachmentUrls {
					name := strings.TrimPrefix(rawName, "attachment.")
					existingAttachments[name] = true
				}

				// Check Retain operations
				for _, name := range updateInfo.attachmentsOperations.Retain {
					if !existingAttachments[name] {
						return nil, fmt.Errorf("%w for %s: cannot retain non-existent attachment %s", errInvalidAttachmentsOperations, updateInfo.exampleUpdateWithID.id, name)
					}
				}

				// Check Rename operations
				for oldName := range updateInfo.attachmentsOperations.Rename {
					if !existingAttachments[oldName] {
						return nil, fmt.Errorf("%w for %s: cannot rename non-existent attachment %s", errInvalidAttachmentsOperations, updateInfo.exampleUpdateWithID.id, oldName)
					}
				}
			}
			for rawName, attachmentInfo := range existingInfo.attachmentUrls {
				name := strings.TrimPrefix(rawName, "attachment.")
				if updateInfo.attachmentsOperations.Retain != nil && slices.Contains(updateInfo.attachmentsOperations.Retain, name) {
					attachmentUrls[fmt.Sprintf("attachment.%s", name)] = attachmentInfo
				} else if newName, exists := updateInfo.attachmentsOperations.Rename[name]; exists {
					attachmentUrls[fmt.Sprintf("attachment.%s", newName)] = attachmentInfo
				}
			}
			for name, attachmentInfo := range updateInfo.attachmentUrls {
				attachmentUrls[name] = attachmentInfo
			}
			// Don't send empty attachmentUrls
			if len(attachmentUrls) == 0 {
				attachmentUrls = nil
			}
		}

		row := []interface{}{
			uid,
			datasetID,
			inputs,
			outputs,
			metadata,
			attachmentUrls,
			existingData[id].createdAt,
			sourceRunID,
		}

		insertRows = append(insertRows, row)
		insertedIds = append(insertedIds, uid)
	}

	if err := crud.bulkInsertExamplesPostgres(ctx, tx, insertRows, columns); err != nil {
		return nil, err
	}

	// return the list of example UUIDs that were successfully updated
	return insertedIds, nil
}

func (crud *ExamplesCRUD) bulkInsertExamplesPostgres(ctx context.Context, tx pgx.Tx, insertRows [][]interface{}, columns []string) error {
	// use CopyFrom to bulk insert
	copyCount, err := tx.CopyFrom(
		ctx,
		pgx.Identifier{"examples_log"},
		columns,
		pgx.CopyFromRows(insertRows),
	)

	if err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) {
			switch pgErr.Code {
			case "22P02":
				return fmt.Errorf("%w: %s", errInvalidJsonPart, pgErr.Message)
			case "23505":
				return fmt.Errorf("%w: %s", errExampleAlreadyExists, pgErr.Message)
			default:
				// Postgres error code is P0001 so need to check message
				if pgErr.Message == "Dataset ID mismatch" {
					return fmt.Errorf("%w", errDatasetExampleMismatch)
				}
				return fmt.Errorf("%w: %s", errInsertPostgresFailure, pgErr.Message)
			}
		}
		return fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
	}

	if int(copyCount) != len(insertRows) {
		return fmt.Errorf("%w: expected %d insertRows, got %d", errInsertPostgresFailure, len(insertRows), copyCount)
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
	}

	return nil
}

func (crud *ExamplesCRUD) bulkCreateExamples(
	ctx context.Context,
	examplesData map[string]*exampleInfo,
	tenantID string,
) ([]uuid.UUID, error) {
	// select existing examples and insert new examples in a single transaction
	tx, err := crud.Pg.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
	}

	// this will rollback unless committed later
	defer tx.Rollback(ctx)

	exampleIds := make([]string, len(examplesData))
	i := 0
	for k := range examplesData {
		exampleIds[i] = k
		i++
	}
	exampleUUIDs, parsedUUIDs, err := parseExampleUUIDs(exampleIds)
	if err != nil {
		return nil, err
	}

	// Validate inputs and outputs for array types
	if err := validateInputsAndOutputsForCreate(examplesData); err != nil {
		return nil, err
	}

	// query for existing examples by id
	var rows pgx.Rows
	const exampleSQL = `
		SELECT examples_log.id 
		FROM examples_log 
		JOIN dataset ON examples_log.dataset_id = dataset.id 
		WHERE examples_log.id = ANY($1) AND dataset.tenant_id = $2`
	rows, err = tx.Query(ctx, exampleSQL, exampleUUIDs, tenantID)

	if err != nil {
		return nil, fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
	}
	defer rows.Close()

	// prefer empty struct over bool for set membership
	// https://dave.cheney.net/2014/03/25/the-empty-struct
	existingExamples := make(map[string]struct{})

	for rows.Next() {
		var id uuid.UUID
		if err := rows.Scan(&id); err != nil {
			return nil, fmt.Errorf("%w: %s", errInsertPostgresFailure, err)
		}
		existingExamples[id.String()] = struct{}{}
	}

	// For insert operations, we throw errors on duplicates
	var duplicateExamples []string
	for id := range existingExamples {
		duplicateExamples = append(duplicateExamples, id)
	}

	// If any duplicates are found, return an error
	if len(duplicateExamples) > 0 {
		return nil, fmt.Errorf("%w: %s", errExampleAlreadyExists, duplicateExamples)
	}

	columns := []string{
		"id",
		"dataset_id",
		"inputs",
		"outputs",
		"metadata",
		"attachment_urls",
		"source_run_id",
	}

	insertRows := make([][]interface{}, 0, len(examplesData))
	insertedIds := make([]uuid.UUID, 0, len(examplesData))

	// prepare insert rows
	for id, createInfo := range examplesData {
		if createInfo.exampleCreateWithID == nil || createInfo.exampleCreateWithID.exampleCreate == nil {
			return nil, fmt.Errorf("%w: encountered unexpected nil value", errInvalidJsonPart)
		}
		if createInfo.exampleCreateWithID.id == "" || createInfo.exampleCreateWithID.exampleCreate.DatasetID == "" {
			return nil, fmt.Errorf("%w: example_id and dataset_id are required for all example parts", errInvalidJsonPart)
		}

		uid := parsedUUIDs[id]

		datasetID, err := uuid.Parse(createInfo.exampleCreateWithID.exampleCreate.DatasetID)
		if err != nil {
			return nil, fmt.Errorf("%w: invalid dataset UUID %s: %s", errInsertPostgresFailure, createInfo.exampleCreateWithID.exampleCreate.DatasetID, err)
		}

		var metadata map[string]interface{}

		if createInfo.exampleCreateWithID.exampleCreate.Metadata != nil {
			metadata = createInfo.exampleCreateWithID.exampleCreate.Metadata
		}

		if createInfo.exampleCreateWithID.exampleCreate.Split != nil {
			if metadata == nil {
				metadata = make(map[string]interface{})
			}
			metadata["dataset_split"] = *createInfo.exampleCreateWithID.exampleCreate.Split
		}

		attachmentUrls := createInfo.attachmentUrls
		sourceRunID := createInfo.exampleCreateWithID.exampleCreate.SourceRunID

		var inputs, outputs interface{}

		if createInfo.inputs != nil {
			inputs = createInfo.inputs
		} else if createInfo.inputsBytes != nil {
			inputs = createInfo.inputsBytes
		}

		if createInfo.outputs != nil {
			outputs = createInfo.outputs
		} else if createInfo.outputsBytes != nil {
			outputs = createInfo.outputsBytes
		}

		row := []interface{}{
			uid,
			datasetID,
			inputs,
			outputs,
			metadata,
			attachmentUrls,
			sourceRunID,
		}

		insertRows = append(insertRows, row)
		insertedIds = append(insertedIds, uid)
	}

	if err := crud.bulkInsertExamplesPostgres(ctx, tx, insertRows, columns); err != nil {
		return nil, err
	}
	// return the list of example UUIDs that were successfully inserted
	return insertedIds, nil
}

func (crud *ExamplesCRUD) readDatasetInfoForUpsert(ctx context.Context, datasetID string, tenantID string) (*datasetInfo, error) {
	datasetSQL := `
	SELECT
		d.id,
		d.data_type,
		d.inputs_schema_definition,
		d.outputs_schema_definition,
		EXISTS (SELECT 1 FROM dataset_transformations dt WHERE dt.dataset_id = d.id) AS has_transformations
	FROM dataset d
	WHERE d.id = $1 AND d.tenant_id = $2
	`

	datasetInfo := &datasetInfo{}

	row := crud.Pg.QueryRow(ctx, datasetSQL, datasetID, tenantID)

	var (
		dataType                string
		inputsSchemaDefinition  sql.NullString
		outputsSchemaDefinition sql.NullString
		hasTransformations      bool
	)

	if err := row.Scan(&datasetID, &dataType, &inputsSchemaDefinition, &outputsSchemaDefinition, &hasTransformations); err != nil {
		return nil, fmt.Errorf("%w: %s", errDatasetNotFound, err)
	}

	datasetInfo.id = datasetID
	datasetInfo.dataType = dataType
	datasetInfo.hasTransformations = hasTransformations

	if inputsSchemaDefinition.Valid {
		datasetInfo.inputsSchemaDefinition = gojsonschema.NewStringLoader(inputsSchemaDefinition.String)
	}
	if outputsSchemaDefinition.Valid {
		datasetInfo.outputsSchemaDefinition = gojsonschema.NewStringLoader(outputsSchemaDefinition.String)
	}
	return datasetInfo, nil
}

func (crud *ExamplesCRUD) validateAgainstSchema(data []byte, schema gojsonschema.JSONLoader) (map[string]interface{}, error) {
	var jsonObj map[string]interface{}
	if err := json.Unmarshal(data, &jsonObj); err != nil {
		return nil, fmt.Errorf("error unmarshalling JSON: %w", err)
	}

	if schema == nil {
		return jsonObj, nil
	}

	loader := gojsonschema.NewGoLoader(jsonObj)
	result, err := gojsonschema.Validate(schema, loader)
	if err != nil {
		return nil, err
	}

	if !result.Valid() {
		var valErrors []string
		for _, desc := range result.Errors() {
			valErrors = append(valErrors, desc.String())
		}
		return nil, fmt.Errorf("schema validation errors: %v", valErrors)
	}

	return jsonObj, nil
}
