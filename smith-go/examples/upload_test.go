package examples_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"sort"
	"strings"
	"testing"

	"github.com/go-chi/chi/v5"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/examples"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
)

var (
	basicExamplesFileData = arbitraryJsonlData{
		examples: []string{
			`{"inputs": {"hello": "there"}, "outputs": {"goodbye": "now"}, "metadata": {"key": "value"}}`,
			`{"inputs": {"hello": "again"}, "outputs": {"goodbye": "again"}, "metadata": {"key": "othervalue"}}`,
		},
	}

	expectedBasicExamples = []jsonlTestExample{
		{
			Inputs:   map[string]interface{}{"hello": "there"},
			Outputs:  map[string]interface{}{"goodbye": "now"},
			Metadata: map[string]interface{}{"key": "value"},
		},
		{
			Inputs:   map[string]interface{}{"hello": "again"},
			Outputs:  map[string]interface{}{"goodbye": "again"},
			Metadata: map[string]interface{}{"key": "othervalue"},
		},
	}
)

func wipeDatasets(t *testing.T, dbpool *database.AuditLoggedPool) {
	_, err := dbpool.Exec(context.Background(), "DELETE FROM dataset")
	require.NoError(t, err)
}

func setupDatasetBasic(
	t *testing.T,
	dbpool *database.AuditLoggedPool,
	tenantId string,
) (string, error) {
	var datasetId string
	err := dbpool.QueryRow(
		context.Background(),
		`INSERT INTO dataset (id, name, description, tenant_id, created_at) VALUES ($1, $2, $3, $4, now()) RETURNING id`,
		uuid.New().String(),
		"dataset name",
		"dataset description",
		tenantId,
	).Scan(
		&datasetId,
	)
	require.NoError(t, err)
	require.NotEmpty(t, datasetId)
	return datasetId, nil
}

func setupDatasetWithSchema(
	t *testing.T,
	dbpool *database.AuditLoggedPool,
	tenantId string,
) (string, error) {
	var datasetId string
	err := dbpool.QueryRow(
		context.Background(),
		`INSERT INTO dataset (id, name, description, tenant_id, inputs_schema_definition, outputs_schema_definition) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
		uuid.New().String(),
		"dataset with schema",
		"dataset description",
		tenantId,
		`{"type":"object","properties":{"hello":{"type":"string"}}, "required":["hello"]}`,
		`{"type":"object","properties":{"goodbye":{"type":"string"}}, "required":["goodbye"]}`,
	).Scan(
		&datasetId,
	)
	require.NoError(t, err)
	require.NotEmpty(t, datasetId)
	return datasetId, nil
}

func setupDatasetWithTransformations(
	t *testing.T,
	dbpool *database.AuditLoggedPool,
	tenantId string,
) (string, error) {
	datasetId, err := setupDatasetBasic(t, dbpool, tenantId)
	require.NoError(t, err)

	var transformation_id string
	err = dbpool.QueryRow(
		context.Background(),
		`INSERT INTO dataset_transformations (id, path, transformation_type, dataset_id) VALUES ($1, $2, $3, $4) RETURNING id`,
		uuid.New().String(),
		`["inputs"]`,
		"remove_extra_fields",
		datasetId,
	).Scan(
		&transformation_id,
	)
	require.NoError(t, err)

	return datasetId, nil
}

func TestUploadExamplesHandler_UploadExamplesFromJsonlCases(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	config.Env.AuditLogsEnabled = true
	dbpool := database.PgConnect()

	defer DbCleanup(t, dbpool)
	tracer, ok := dbpool.Tracer.(*database.TestsCaptureTracer)
	if !ok {
		t.Fatalf("dbpool.Tracer is not of type *database.TestsCaptureTracer, %T", dbpool.Tracer)
	}
	defer tracer.Reset()
	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	h := examples.UploadExamplesHandler{
		Pg:           dbpool,
		ExamplesCRUD: examples.NewExamplesCRUD(dbpool),
	}

	testCases := []jsonlUploadTestCase{
		// BASIC TEST CASES
		{
			name:             "Valid jsonl request",
			setupDataset:     setupDatasetBasic,
			fileData:         basicExamplesFileData,
			expectedExamples: expectedBasicExamples,
			expectedStatus:   http.StatusCreated,
		},
		{
			name:             "No example file provided",
			setupDataset:     setupDatasetBasic,
			fileData:         basicExamplesFileData,
			expectedExamples: expectedBasicExamples,
			expectedStatus:   http.StatusCreated,
		},
		// INPUT KEY SPECIFICATION TEST CASES
		{
			name:         "Specify input keys",
			setupDataset: setupDatasetBasic,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			inputKeys:        &[]string{"hello"},
			outputKeys:       &[]string{"goodbye"},
			metadataKeys:     &[]string{"key"},
			expectedExamples: expectedBasicExamples,
			expectedStatus:   http.StatusCreated,
		},
		{
			name:         "Specify only input and metadata keys",
			setupDataset: setupDatasetBasic,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			inputKeys:    &[]string{"hello"},
			metadataKeys: &[]string{"key"},
			expectedExamples: []jsonlTestExample{
				{
					Inputs:   map[string]interface{}{"hello": "there"},
					Metadata: map[string]interface{}{"key": "value"},
				},
				{
					Inputs:   map[string]interface{}{"hello": "again"},
					Metadata: map[string]interface{}{"key": "othervalue"},
				},
			},
			expectedStatus: http.StatusCreated,
		},
		{
			name:         "Cannot specify only output keys",
			setupDataset: setupDatasetBasic,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			outputKeys:     &[]string{"goodbye"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "Cannot specify only metadata keys",
			setupDataset: setupDatasetBasic,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			metadataKeys:   &[]string{"key"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "Can specify only input keys",
			setupDataset: setupDatasetBasic,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			inputKeys:      &[]string{"hello"},
			expectedStatus: http.StatusCreated,
			expectedExamples: []jsonlTestExample{
				{Inputs: map[string]interface{}{"hello": "there"}},
				{Inputs: map[string]interface{}{"hello": "again"}},
			},
		},
		// SCHEMA VALIDATION TEST CASES
		{
			name:             "Schema validation succeeds as expected",
			setupDataset:     setupDatasetWithSchema,
			fileData:         basicExamplesFileData,
			expectedExamples: expectedBasicExamples,
			expectedStatus:   http.StatusCreated,
		},
		{
			name:         "Inputs schema validation fails as expected",
			setupDataset: setupDatasetWithSchema,
			fileData: arbitraryJsonlData{
				examples: []string{
					// good example first
					`{"inputs": {"hello": "there"}, "outputs": {"goodbye": "now"}, "metadata": {"key": "value"}}`,
					// bad example second
					`{"inputs": {"otherkey": 1}, "outputs": {"goodbye": "now"}, "metadata": {"key": "value"}}`,
				},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "Outputs schema validation fails as expected",
			setupDataset: setupDatasetWithSchema,
			fileData: arbitraryJsonlData{
				examples: []string{
					// good example first
					`{"inputs": {"hello": "there"}, "outputs": {"goodbye": "now"}, "metadata": {"key": "value"}}`,
					// bad example second
					`{"inputs": {"hello": "now"}, "outputs": {"blaaahhhh": "now"}, "metadata": {"key": "value"}}`,
				},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "Schema validation passes with input keys",
			setupDataset: setupDatasetWithSchema,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			inputKeys:        &[]string{"hello"},
			outputKeys:       &[]string{"goodbye"},
			metadataKeys:     &[]string{"key"},
			expectedExamples: expectedBasicExamples,
			expectedStatus:   http.StatusCreated,
		},
		{
			name:         "Input schema validation fails expectedly with input keys",
			setupDataset: setupDatasetWithSchema,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": 1, "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": "again", "key": "othervalue"}`,
				},
			},
			inputKeys:      &[]string{"hello"},
			outputKeys:     &[]string{"goodbye"},
			metadataKeys:   &[]string{"key"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:         "Output schema validation fails expectedly with input keys",
			setupDataset: setupDatasetWithSchema,
			fileData: arbitraryJsonlData{
				examples: []string{
					`{"hello": "there", "goodbye": "now", "key": "value"}`,
					`{"hello": "again", "goodbye": 1, "key": "othervalue"}`,
				},
			},
			inputKeys:      &[]string{"hello"},
			outputKeys:     &[]string{"goodbye"},
			metadataKeys:   &[]string{"key"},
			expectedStatus: http.StatusBadRequest,
		},
		// Transformations Test cases
		{
			name:             "Upload examples to dataset with transformations",
			setupDataset:     setupDatasetWithTransformations,
			fileData:         basicExamplesFileData,
			expectedExamples: expectedBasicExamples,
			expectedStatus:   http.StatusCreated,
		},
	}

	apiKey := "***************************************************"
	userID := uuid.New()
	userIdStr := userID.String()
	user := testutil.UserSetup(t, dbpool, userIdStr, uuid.New().String()+"@gmail.com", "test", "email", nil, nil)
	orgId := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "Test Tenant", uuid.New().String()+"-handle", &auth.TenantConfig{}, true)
	parentIdentityId := testutil.IdentitySetup(t, dbpool, userIdStr, orgId, nil, "ORGANIZATION_ADMIN", "organization", nil, user.LSUserID)
	workspaceIdentityId := testutil.IdentitySetup(t, dbpool, userIdStr, orgId, &tenantId, "WORKSPACE_ADMIN", "workspace", &parentIdentityId, user.LSUserID)
	apiKeyStr := "e8fc86029b5f84ee6341317e5f1544ba0a8eb8d36eaf57300bd31856828504f7394e3b2a7cc71686ea4c48ef8930f495799e33f401a19097fec5fa5338a1036f"
	shortApiKey := auth.ShortenApiKey(apiKey)
	testutil.ApiKeySetup(t, dbpool, apiKeyStr, tenantId, workspaceIdentityId, shortApiKey, orgId, userIdStr, user.LSUserID)

	headers := map[string]string{
		"X-Api-Key": apiKey,
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var b bytes.Buffer
			w := multipart.NewWriter(&b)

			// create the dataset
			datasetId, err := tc.setupDataset(t, dbpool, tenantId)
			require.NoError(t, err)
			defer wipeDatasets(t, dbpool)
			defer tracer.Reset()

			if tc.inputKeys != nil {
				for _, key := range *tc.inputKeys {
					err = w.WriteField("input_keys", key)
					require.NoError(t, err)
				}
			}
			if tc.outputKeys != nil {
				for _, key := range *tc.outputKeys {
					err = w.WriteField("output_keys", key)
					require.NoError(t, err)
				}
			}
			if tc.metadataKeys != nil {
				for _, key := range *tc.metadataKeys {
					err = w.WriteField("metadata_keys", key)
					require.NoError(t, err)
				}
			}

			if len(tc.fileData.examples) > 0 {
				fw, err := w.CreateFormFile("file", "example.jsonl")
				require.NoError(t, err)

				for _, example := range tc.fileData.examples {
					_, err = fw.Write(append([]byte(example), '\n'))
					require.NoError(t, err)
				}

				w.Close()

				req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/datasets/%s/examples/upload", datasetId), &b)
				req.Header.Set("Content-Type", w.FormDataContentType())
				for k, v := range headers {
					req.Header.Set(k, v)
				}
				ah := auth.NewBasicAuth(dbpool, redisPool, 0)

				rr := httptest.NewRecorder()

				// wrap the handler with the testlogger and auth middleware
				r := chi.NewRouter()
				r.Use(testLogger(t))
				r.Use(ah.Middleware)
				r.Use(auth.AuditLogContextMiddleware)
				r.Post("/datasets/{dataset_id}/examples/upload", h.UploadExamplesFromJSONL)
				r.ServeHTTP(rr, req)

				assert.Equal(t, tc.expectedStatus, rr.Code, "UploadExamplesFromJsonl returned wrong status code")

				// if the status is 201, verify the examples are present in the database
				if tc.expectedStatus == http.StatusCreated {
					// unmarshall the examplesCreatedResponse
					var examplesCreatedResponse = examples.ExamplesCreatedResponse{}
					err := json.Unmarshal(rr.Body.Bytes(), &examplesCreatedResponse)
					if err != nil {
						t.Fatal(err)
					}

					rows, err := dbpool.Query(context.Background(), `SELECT id, dataset_id, inputs, outputs, metadata, attachment_urls FROM examples_log WHERE id = ANY($1)`, examplesCreatedResponse.ExampleIDs)
					require.NoError(t, err)
					defer rows.Close()

					var examples []testExample
					for rows.Next() {
						var example testExample
						err := rows.Scan(&example.ID, &example.DatasetID, &example.Inputs, &example.Outputs, &example.Metadata, &example.AttachmentURLs)
						require.NoError(t, err)
						examples = append(examples, example)
						if example.DatasetID != datasetId {
							t.Fatalf("example was not inserted into the correct dataset")
						}
					}

					assert.Equal(t, len(tc.expectedExamples), len(examples), "number of examples in database does not match expected number")

					// Sort both slices by the stringified Inputs field
					sort.Slice(tc.expectedExamples, func(i, j int) bool {
						return stringifyInputs(tc.expectedExamples[i].Inputs) < stringifyInputs(tc.expectedExamples[j].Inputs)
					})
					sort.Slice(examples, func(i, j int) bool {
						return stringifyInputs(examples[i].Inputs) < stringifyInputs(examples[j].Inputs)
					})

					for i, example := range examples {
						assert.Equal(t, tc.expectedExamples[i].Inputs, example.Inputs, "inputs do not match")
						assert.Equal(t, tc.expectedExamples[i].Outputs, example.Outputs, "outputs do not match")
						assert.Equal(t, tc.expectedExamples[i].Metadata, example.Metadata, "metadata does not match")
					}

					// verify SELECT 1 audit log is present for CopyFrom
					var auditedQuery string
					for _, query := range tracer.Queries() {
						if strings.Contains(query.SQL, "SELECT 1;") {
							auditedQuery = query.SQL
							break
						}
					}
					assert.NotEmpty(t, auditedQuery, "SELECT 1 audit log not found in logs: %v", tracer.Queries())
					testutil.AssertAuditLogVarsInQuery(t, auditedQuery, "create_examples", orgId, tenantId, nil, nil, &shortApiKey)
				}
			}
		})
	}
}

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "delete from organizations; delete from users;")
	assert.NoError(t, err)
	config.Env.AuditLogsEnabled = false
}

// Function to convert Inputs to a string
func stringifyInputs(inputs interface{}) string {
	bytes, err := json.Marshal(inputs)
	if err != nil {
		return ""
	}
	return string(bytes)
}

type jsonlTestExample struct {
	Inputs   map[string]interface{} `json:"inputs"`
	Outputs  map[string]interface{} `json:"outputs"`
	Metadata map[string]interface{} `json:"metadata"`
}

type arbitraryJsonlData struct {
	examples []string
}

type jsonlUploadTestCase struct {
	name             string
	setupDataset     func(t *testing.T, dbpool *database.AuditLoggedPool, tenantId string) (string, error)
	fileData         arbitraryJsonlData
	expectedStatus   int
	inputKeys        *[]string
	outputKeys       *[]string
	metadataKeys     *[]string
	expectedExamples []jsonlTestExample
}
