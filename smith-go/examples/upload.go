package examples

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"langchain.com/smith/config"
	"langchain.com/smith/util"

	"github.com/go-chi/chi/v5"

	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"github.com/xeipuuv/gojsonschema"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
)

type UploadExamplesHandler struct {
	Pg           *database.AuditLoggedPool
	ExamplesCRUD *ExamplesCRUD
}

type exampleJsonlRecord struct {
	Inputs   map[string]interface{} `json:"inputs"`
	Outputs  map[string]interface{} `json:"outputs"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

var (
	UploadMaxFileSize = int64(50 * 1024 * 1024) // 50 MB

	// 400 Bad Request
	errUploadNoData                 = errors.New("no data in file")
	errInvalidRowNoInputsOrOutputs  = errors.New("invalid row in JSONL, each row must have a field named \"inputs\" and a field named \"outputs\" with a object values, or you must specify alternative input and output keys.\nExample row: {\"inputs\": {\"input\": \"value\"}, \"outputs\": {\"output\": \"value\"}}")
	errFailedInputSchemaValidation  = errors.New("failed input schema validation")
	errFailedOutputSchemaValidation = errors.New("failed output schema validation")

	// 403 Forbidden
	errUploadUnauthorized = errors.New("unauthorized")

	// 422 Unprocessable Entity
	errUploadMissingDatasetId  = errors.New("missing dataset ID")
	errUploadInvalidDatasetId  = errors.New("invalid dataset ID")
	errUploadMissingFile       = errors.New("missing file")
	errUploadIncorrectFileType = errors.New("incorrect file type")
	errUploadFileTooLarge      = errors.New("file too large")
	errUploadFileLineTooLarge  = errors.New("invalid row in JSONL, line may not be greater than 1MB in length")
	errInvalidDatasetId        = errors.New("invalid dataset id, must be a uuid")
)

// Uploads a JSONL file of examples to a specified dataset.
func (h *UploadExamplesHandler) UploadExamplesFromJSONL(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)
	ctx = context.WithValue(ctx, config.OperationCtxKey, "create_examples")

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		h.handleError(w, r, oplog, errUploadUnauthorized)
		return
	}

	if err := r.ParseMultipartForm(UploadMaxFileSize); err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	// get datasetID as a path parameter
	datasetId := chi.URLParam(r, "dataset_id")
	if datasetId == "" {
		h.handleError(w, r, oplog, errUploadMissingDatasetId)
		return
	}

	// check to see if datasetID is a valid UUID
	if err := uuid.Validate(datasetId); err != nil {
		h.handleError(w, r, oplog, errInvalidDatasetId)
		return
	}

	datasetInfo, err := h.ExamplesCRUD.readDatasetInfoForUpsert(ctx, datasetId, authInfo.TenantID)
	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	inputKeys := r.Form["input_keys"]
	outputKeys := r.Form["output_keys"]
	metadataKeys := r.Form["metadata_keys"]

	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		h.handleError(w, r, oplog, errUploadMissingFile)
		return
	}
	defer file.Close()

	if fileHeader.Size > UploadMaxFileSize {
		h.handleError(w, r, oplog, errUploadFileTooLarge)
		return
	}

	if !strings.HasSuffix(fileHeader.Filename, ".jsonl") {
		h.handleError(w, r, oplog, errUploadIncorrectFileType)
		return
	}

	examplesData := make(map[string]*exampleInfo)
	scanner := bufio.NewScanner(file)
	hasReadData := false

	// Set larger buffer size for scanner to handle large lines
	const maxScanTokenSize = 1024 * 1024 // 1MB per line
	buffer := make([]byte, maxScanTokenSize)
	scanner.Buffer(buffer, maxScanTokenSize)

	for scanner.Scan() {
		line := scanner.Text()
		hasReadData = true
		record, err := h.parseRecord(line, datasetInfo, inputKeys, outputKeys, metadataKeys)
		if err != nil {
			h.handleError(w, r, oplog, err)
			return
		}

		exampleId := uuid.NewString()
		exampleInfo := &exampleInfo{
			exampleCreateWithID: &exampleCreateWithID{
				id: exampleId,
				exampleCreate: &exampleCreate{
					DatasetID: datasetId,
					Metadata:  record.Metadata,
				},
			},
		}
		exampleInfo.SetInputs(record.Inputs)
		exampleInfo.SetOutputs(record.Outputs)
		examplesData[exampleId] = exampleInfo
	}

	if err := scanner.Err(); err != nil {
		if errors.Is(err, bufio.ErrTooLong) {
			h.handleError(w, r, oplog, errUploadFileLineTooLarge)
			return
		}
		h.handleError(w, r, oplog, err)
		return
	}

	if !hasReadData {
		h.handleError(w, r, oplog, errUploadNoData)
		return
	}

	insertedIds, err := h.ExamplesCRUD.bulkCreateExamples(ctx, examplesData, authInfo.TenantID)
	if err != nil {
		h.handleError(w, r, oplog, err)
		return
	}

	response := ExamplesCreatedResponse{
		Count:      len(insertedIds),
		ExampleIDs: insertedIds,
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, response)
}

func (h *UploadExamplesHandler) parseRecord(line string, datasetInfo *datasetInfo, inputKeys, outputKeys, metadataKeys []string) (*exampleJsonlRecord, error) {
	record := exampleJsonlRecord{}
	if len(inputKeys) == 0 && len(outputKeys) == 0 && len(metadataKeys) == 0 {
		if err := json.Unmarshal([]byte(line), &record); err != nil {
			return nil, errInvalidRowNoInputsOrOutputs
		}
	} else {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(line), &data); err != nil {
			return nil, fmt.Errorf("error parsing JSONL file: %w", err)
		}
		record.Inputs = extractFields(data, inputKeys)
		record.Outputs = extractFields(data, outputKeys)
		record.Metadata = extractFields(data, metadataKeys)
	}
	if record.Inputs == nil {
		return nil, errInvalidRowNoInputsOrOutputs
	}

	err := h.validateDict(record.Inputs, datasetInfo.inputsSchemaDefinition)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", errFailedInputSchemaValidation, err)
	}

	err = h.validateDict(record.Outputs, datasetInfo.outputsSchemaDefinition)
	if err != nil {
		return nil, fmt.Errorf("%w: %s", errFailedOutputSchemaValidation, err)
	}

	return &record, nil
}

func (h *UploadExamplesHandler) validateDict(record map[string]interface{}, schema gojsonschema.JSONLoader) error {
	if schema == nil {
		return nil
	}
	dataBytes, err := json.Marshal(record)
	if err != nil {
		return err
	}
	_, err = h.ExamplesCRUD.validateAgainstSchema(dataBytes, schema)
	return err
}

func (h *UploadExamplesHandler) handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error) {
	var status int
	var message string

	switch {
	case errors.Is(err, errUploadMissingDatasetId),
		errors.Is(err, errUploadMissingFile),
		errors.Is(err, errUploadIncorrectFileType),
		errors.Is(err, errUploadFileTooLarge),
		errors.Is(err, errUploadFileLineTooLarge),
		errors.Is(err, errInvalidDatasetId),
		errors.Is(err, errInvalidDataset):
		status = http.StatusUnprocessableEntity
		message = "Unprocessable entity: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errDatasetNotFound):
		status = http.StatusNotFound
		message = "Dataset not found: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errUploadUnauthorized):
		status = http.StatusForbidden
		message = "Unauthorized: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errExampleAlreadyExists):
		status = http.StatusConflict
		message = "Conflict: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errUploadNoData),
		errors.Is(err, errInvalidRowNoInputsOrOutputs),
		errors.Is(err, errFailedInputSchemaValidation),
		errors.Is(err, errFailedOutputSchemaValidation):
		status = http.StatusBadRequest
		message = "Bad request: " + err.Error()

	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			status = http.StatusServiceUnavailable
			message = "Service unavailable: " + err.Error()
			oplog.Warn(message, "error", err)
		} else {
			status = http.StatusInternalServerError
			referenceId := uuid.NewString()
			message = "Internal server error: reference ID " + referenceId
			oplog.Error(message, "err", err, "reference_id", referenceId)
		}
	}

	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message})
}

// Helper function to extract fields from data
func extractFields(data map[string]interface{}, keys []string) map[string]interface{} {
	result := make(map[string]interface{})
	for _, key := range keys {
		if value, ok := data[key]; ok {
			result[key] = value
		}
	}
	if len(result) == 0 {
		return nil
	}
	return result
}
