# smith-go
This is the platform/authentication service for LangSmith.

## Tests
To target a specific test, set the `RUN_ARG` environment variable, 
which is passed to the `-run` [testing flag](https://pkg.go.dev/cmd/go#hdr-Testing_flags).

Examples:

```shell
# Run all tests for a particular auth mode
RUN_ARG="'TestHandlerSupabase'" make test
# Run tests for a particular middleware and auth mode
RUN_ARG="'TestHandlerSupabase_WSMiddleware'" make test
# Run a specific test
RUN_ARG="'TestHandlerSupabase_WSMiddleware/present_with_org_admin_permissions'" make test
```

## Detecting import cycles

If you see an error like `import cycle not allowed`, use [godepgraph](https://github.com/kisielk/godepgraph)
to detect cycles (prereq: `brew install graphviz`):

```shell
godepgraph -nostdlib -novendor -ignoreprefixes github.com,golang.org,gopkg.in,go.opentelemetry.io -l 3 ./auth | dot -Tpng -o godepgraph.png
```

Change `./auth` to target the directory if possible, otherwise use `.` to show graph for all packages.

## API docs

API reference docs are created from endpoint docstrings. To update the API reference based on the latest docstrings run:

```shell
go install github.com/swaggo/swag/cmd/swag@latest
make update-api-docs
```
