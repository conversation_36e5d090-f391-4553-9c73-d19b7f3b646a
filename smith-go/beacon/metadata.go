package beacon

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"langchain.com/smith/util"
	"log/slog"
	"net/http"
	"time"

	"github.com/ggicci/httpin"
	"github.com/go-chi/render"
	"langchain.com/smith/auth"
)

var (
	// 400 Bad Request
	errRequiredParams = errors.New("either api_key or license_key must be provided")

	// 403 Unauthorized
	errJwtParse          = errors.New("error parsing JWT")
	errInvalidLicenseKey = errors.New("invalid license_key")
	errInvalidApiKey     = errors.New("invalid api_key")
	errMissingApiKey     = errors.New("api_key not found")

	// 500/503
	errMetricsInsertForLicense = errors.New("metrics insert for license key failed")
	errMetricsInsertForApiKey  = errors.New("metrics insert for api key failed")
)

type SubmitMetadataRequest struct {
	Payload SubmitMetadataPayload `in:"body"`
}

type SubmitMetadataPayload struct {
	ApiKey        string            `json:"api_key"`
	LicenseKey    string            `json:"license_key"`
	FromTimestamp time.Time         `json:"from_timestamp"`
	ToTimestamp   time.Time         `json:"to_timestamp"`
	Measures      map[string]int    `json:"measures"`
	Tags          map[string]string `json:"tags"`
	Logs          []json.RawMessage `json:"logs"`
}

func (h *BeaconHandler) handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error) {
	var status int
	var message string

	switch {

	case errors.Is(err, errRequiredParams):
		status = http.StatusBadRequest
		message = "Bad request: " + err.Error()
		oplog.Warn(message, "error", err)

	case errors.Is(err, errJwtParse),
		errors.Is(err, errInvalidLicenseKey),
		errors.Is(err, errInvalidApiKey),
		errors.Is(err, errMissingApiKey):
		status = http.StatusForbidden
		message = "Unauthorized: " + err.Error()
		oplog.Warn(message, "error", err)

	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			status = http.StatusServiceUnavailable
			message = "Service unavailable: " + err.Error()
			oplog.Warn(message, "error", err)
		} else {
			status = http.StatusInternalServerError
			referenceId := uuid.NewString()
			message = "Internal server error: reference ID " + referenceId
			oplog.Error(message, "err", err, "reference_id", referenceId)
		}
	}

	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message})
}

// mount at /v1/metadata/submit
func (h *BeaconHandler) SubmitMetadata(w http.ResponseWriter, r *http.Request) {
	req := r.Context().Value(httpin.Input).(*SubmitMetadataRequest)
	oplog := httplog.LogEntry(r.Context())

	if req.Payload.LicenseKey != "" {
		err := validateChecksum(req.Payload.LicenseKey)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidLicenseKey, err))
		}

		// Hash the provided license with salt
		hashedLicense := hashKey(req.Payload.LicenseKey)
		t, err := h.db.Exec(
			r.Context(),
			"INSERT INTO remote_metrics (license_key, from_timestamp, to_timestamp, measures, tags, logs, self_hosted_customer_id) SELECT $1, $2, $3, $4, $5, $6, self_hosted_customer_id FROM self_hosted_licenses where hashed_license_key = $1",
			hashedLicense, req.Payload.FromTimestamp, req.Payload.ToTimestamp, req.Payload.Measures, req.Payload.Tags, req.Payload.Logs)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errMetricsInsertForLicense, err))
			return
		} else if t.RowsAffected() == 0 {
			return
		}
	} else if req.Payload.ApiKey != "" {
		if err := auth.ValidateApiKey(req.Payload.ApiKey); err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errInvalidApiKey, err))
			return
		}
		hashedApiKey := auth.HashApiKey(req.Payload.ApiKey)
		t, err := h.db.Exec(
			r.Context(),
			"INSERT INTO remote_metrics (api_key, from_timestamp, to_timestamp, measures, tags, logs, tenant_id) SELECT $1, $2, $3, $4, $5, $6, tenant_id FROM api_keys where api_key = $1",
			hashedApiKey, req.Payload.FromTimestamp, req.Payload.ToTimestamp, req.Payload.Measures, req.Payload.Tags, req.Payload.Logs)
		if err != nil {
			h.handleError(w, r, oplog, fmt.Errorf("%w: %v", errMetricsInsertForApiKey, err))
			return
		}
		if t.RowsAffected() == 0 {
			h.handleError(w, r, oplog, errMissingApiKey)
			return
		}
	} else {
		h.handleError(w, r, oplog, errRequiredParams)
		return
	}

	render.NoContent(w, r)
}
