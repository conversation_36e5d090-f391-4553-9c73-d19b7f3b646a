package beacon

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ggicci/httpin"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"net/http"
	"time"
)

type VerifyPayload struct {
	License  string          `json:"license"`
	Metadata json.RawMessage `json:"metadata"`
}

type VerifyRequest struct {
	Payload *VerifyPayload `in:"body"`
}

// Validate license key and issue a short lived JWT token. This token will last for 7 days s.t errors validating/LangSmith
// going down will not affect the customer's ability to use the product.
// TODO: we should implement a JWKs endpoint that exposes public keys for a set of private keys. This way we can rotate keys
func (h *BeaconHandler) VerifyLicenseKey(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())

	reqBody := r.Context().Value(httpin.Input).(*VerifyRequest)
	req := reqBody.Payload
	err := h.validator.Struct(req)
	if err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	licenseInfo, err := verifyLicense(r.Context(), h.db, req.License, req.Metadata)
	if err != nil {
		var le *LicenseError
		if errors.As(err, &le) {
			oplog.Warn("Error verifying license", "error", le)
			http.Error(w, le.Message, le.StatusCode)
			return
		}
	}
	if licenseInfo == nil {
		oplog.Warn("License not found", "license", req.License)
		http.Error(w, "License not found", http.StatusNotFound)
		return
	}
	tokenString, err := h.IssueLicenseJWT(licenseInfo)
	if err != nil {
		oplog.Error("Error issuing JWT", "error", err)
		http.Error(w, "Error issuing JWT", http.StatusInternalServerError)
		return
	}
	response := map[string]interface{}{
		"token": tokenString,
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, response)
}

type CreateCustomerRequest struct {
	Payload *CreateCustomerPayload `in:"body"`
}

type CustomerConfig struct {
	LangGraphPlatformEnabled bool `json:"lgp_enabled"`
}

type CreateCustomerPayload struct {
	CustomerName   string         `json:"customer_name" validate:"required"`
	CustomerConfig CustomerConfig `json:"customer_config"` // Optional, for future use
}

func (h *BeaconHandler) InternalCreateCustomer(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())

	requestBody := r.Context().Value(httpin.Input).(*CreateCustomerRequest)
	req := requestBody.Payload

	// Validate request payload
	err := h.validator.Struct(req)
	if err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	// Insert or update the customer in the database
	var customerID string
	err = h.db.QueryRow(r.Context(), `
		INSERT INTO self_hosted_customers (customer_name, config)
		VALUES ($1, $2)
		ON CONFLICT (customer_name) DO UPDATE SET config = EXCLUDED.config
		RETURNING id
	`, req.CustomerName, req.CustomerConfig).Scan(&customerID)

	if err != nil {
		oplog.Error("Error inserting customer", "error", err)
		http.Error(w, "Error inserting customer", http.StatusInternalServerError)
		return
	}

	// Log and return the customer ID
	oplog.Info("Created customer", "customer_name", req.CustomerName, "customer_id", customerID)
	response := map[string]interface{}{
		"customer_id": customerID,
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, response)
}

type CreateLicenseRequest struct {
	Payload *CreateLicensePayload `in:"body"`
}

type CreateLicensePayload struct {
	CustomerID      string      `json:"customer_id" validate:"required"`
	LicenseType     LicenseType `json:"license_type" validate:"required"`
	ExpirationWeeks int         `json:"expiration_weeks"`
	ExpirationDate  string      `json:"expiration_date"` // Optional (RFC3339 or other format)
	OfflineMode     bool        `json:"offline_mode"`
}

func (h *BeaconHandler) InternalCreateLicense(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())

	requestBody := r.Context().Value(httpin.Input).(*CreateLicenseRequest)
	req := requestBody.Payload

	// Validate request payload
	err := h.validator.Struct(req)
	if err != nil {
		oplog.Error("Invalid request", "error", err)
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	// Check if the customer exists
	var customer selfHostedCustomer
	err = h.db.QueryRow(r.Context(), `
		SELECT id, customer_name, config  FROM self_hosted_customers WHERE id = $1
	`, req.CustomerID).Scan(&customer.CustomerID, &customer.CustomerName, &customer.Config)

	if err != nil {
		oplog.Warn("Error checking customer existence", "error", err)
		http.Error(w, "Customer not found", http.StatusNotFound)
		return
	}

	if customer.CustomerID != req.CustomerID {
		http.Error(w, "Customer not found", http.StatusNotFound)
		return
	}

	// Calculate the expiration date
	var expiresAt time.Time

	if req.ExpirationDate != "" {
		// Parse the expiration_date string
		// Expecting RFC3339, e.g. "2025-01-06T10:00:00Z"
		parsedTime, parseErr := time.Parse(time.RFC3339, req.ExpirationDate)
		if parseErr != nil {
			oplog.Error("Error parsing expiration_date", "error", parseErr)
			http.Error(w, "Invalid format for expiration_date. Must be RFC3339.", http.StatusBadRequest)
			return
		}

		expiresAt = parsedTime

		// Optional: Validate it's a future date
		if expiresAt.Before(time.Now()) {
			oplog.Error("Expiration date must be in the future", "expiration_date", expiresAt)
			http.Error(w, "Expiration date must be in the future", http.StatusBadRequest)
			return
		}
	} else {
		// Fallback: use ExpirationWeeks
		expiresAt = time.Now().AddDate(0, 0, req.ExpirationWeeks*7)
	}

	licenseId := uuid.New().String()
	var licenseKey string
	var hashedLicense string
	if req.OfflineMode {
		// Generate the JWT directly
		licenseInfo := &selfHostedLicenseWithCustomer{
			selfHostedLicense:  selfHostedLicense{LicenseID: licenseId, ExpiresAt: expiresAt},
			selfHostedCustomer: customer,
		}
		licenseKey, err = h.IssueLicenseJWT(licenseInfo)
		if err != nil {
			oplog.Warn("Error generating new license", "error", err)
			http.Error(w, "Error generating new license", http.StatusInternalServerError)
			return
		}
		hashedLicense = hashKey(licenseKey)
	} else {
		licenseKey, hashedLicense, err = GenerateKeyAndHash("lcl")
		if err != nil {
			oplog.Warn("Error generating new license", "error", err)
			http.Error(w, "Error generating new license", http.StatusInternalServerError)
			return
		}
	}

	shortKey := licenseKey[:8] + "..." + licenseKey[len(licenseKey)-8:]
	// Insert the license into the database
	var licenseInfo selfHostedLicense
	err = h.db.QueryRow(r.Context(), `
		INSERT INTO self_hosted_licenses (
		    id,
			self_hosted_customer_id,
			hashed_license_key,
			expires_at,
		    short_key,
		    offline_mode
		)
		VALUES (
			$1, $2, $3, $4, $5, $6
		)
		RETURNING id, expires_at
	`, licenseId, req.CustomerID, hashedLicense, expiresAt, shortKey, req.OfflineMode).Scan(
		&licenseInfo.LicenseID,
		&licenseInfo.ExpiresAt,
	)

	if err != nil {
		oplog.Warn("Error inserting license", "error", err)
		http.Error(w, fmt.Sprintf("Error inserting license: %v", err), http.StatusInternalServerError)
		return
	}

	// Log and return the license key
	oplog.Info("Created license for customer", "customer_id", req.CustomerID, "license_id", licenseInfo.LicenseID)
	response := map[string]string{
		"license_key": licenseKey,
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, response)
}
