package beacon

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/ggicci/httpin"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/jackc/pgx/v5"
)

type TraceTransaction struct {
	ID                    string    `json:"id"`
	TenantID              string    `json:"tenant_id"`
	SessionID             string    `json:"session_id"`
	TraceCount            int       `json:"trace_count"`
	StartInsertionTime    time.Time `json:"start_insertion_time"`
	EndInsertionTime      time.Time `json:"end_insertion_time"`
	StartIntervalTime     time.Time `json:"start_interval_time"`
	EndIntervalTime       time.Time `json:"end_interval_time"`
	Status                string    `json:"status"`
	NumFailedSendAttempts int       `json:"num_failed_send_attempts"`
	TransactionType       string    `json:"transaction_type"`
	OrganizationId        string    `json:"organization_id"`
}

type IngestTracesPayload struct {
	License           string             `json:"license" validate:"required"`
	TraceTransactions []TraceTransaction `json:"trace_transactions" validate:"required"`
}

type IngestTracesRequest struct {
	Payload *IngestTracesPayload `in:"body"`
}

const transactionQuery = `
    INSERT INTO trace_count_transactions (
        id,
        tenant_id,
        session_id,
        trace_count,
        insertion_time_range,
        interval,
        status,
        num_failed_send_attempts,
        transaction_type,
        organization_id,
        source,
        self_hosted_license_id
    )
    VALUES (
        $1,
        $2,
        $3,
        $4,
        tstzrange($5, $6, '[]'),
        tstzrange($7, $8, '(]'),
        $9,
        $10,
        $11,
        $12,
        $13,
        $14
    )
`

func (h *BeaconHandler) IngestTraces(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())

	reqBody := r.Context().Value(httpin.Input).(*IngestTracesRequest)
	req := reqBody.Payload
	err := h.validator.Struct(req)
	if err != nil {
		oplog.Warn("Invalid request", "error", err)
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	licenseInfo, err := verifyLicense(r.Context(), h.db, req.License, json.RawMessage(`{}`))
	if err != nil {
		var le *LicenseError
		if errors.As(err, &le) {
			oplog.Warn("Error verifying license", "error", le)
			http.Error(w, le.Message, le.StatusCode)
			return
		}
	}
	if licenseInfo == nil {
		oplog.Warn("Error verifying license", "error", err)
		http.Error(w, "Error verifying license", http.StatusInternalServerError)
		return
	}

	// Process the trace data using the license ID
	// Start a transaction
	tx, err := h.db.BeginTx(r.Context(), pgx.TxOptions{IsoLevel: pgx.ReadCommitted})
	if err != nil {
		oplog.Error("Error starting transaction", "error", err)
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	defer func() {
		if err != nil {
			tx.Rollback(r.Context())
			return
		}
		err = tx.Commit(r.Context()) // commit, set return error here
	}()

	// Loop over transactions and execute the insert
	for _, txn := range req.TraceTransactions {
		// Prepare the arguments
		args := []interface{}{
			txn.ID,
			txn.TenantID,
			txn.SessionID,
			txn.TraceCount,
			txn.StartInsertionTime,
			txn.EndInsertionTime,
			txn.StartIntervalTime,
			txn.EndIntervalTime,
			txn.Status,
			txn.NumFailedSendAttempts,
			txn.TransactionType,
			txn.OrganizationId,
			"remote_self_hosted",
			licenseInfo.LicenseID,
		}

		// Execute the statement
		_, err := tx.Exec(r.Context(), transactionQuery, args...)
		if err != nil {
			oplog.Error("Error inserting txn", "error", err)
			http.Error(w, "Database error, error inserting txn", http.StatusInternalServerError)
			return
		}
	}

	// Commit the transaction
	err = tx.Commit(r.Context())
	if err != nil {
		oplog.Error("Error commiting trace txns", "error", err)
		http.Error(w, "Database error, error committing txn", http.StatusInternalServerError)
		return
	}

	// Return a success response
	response := map[string]string{
		"inserted_count": fmt.Sprintf("%d", len(req.TraceTransactions)),
	}
	render.Status(r, http.StatusOK)
	render.JSON(w, r, response)
}
