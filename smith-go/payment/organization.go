package payment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"langchain.com/smith/database"
	"langchain.com/smith/util"
)

var (
	ErrOrganizationNotFound = errors.New("organization not found")
	ErrMultipleWalletsFound = errors.New("multiple wallets found for organization")
	ErrDatabaseQuery        = errors.New("database query failed")
)

type Wallet struct {
	CreditBalanceMicros   int64
	InflightBalanceMicros int64
}

type Organization struct {
	ID                    uuid.UUID
	DisplayName           string
	CreatedAt             *time.Time
	CreatedByUserID       *uuid.UUID
	ModifiedAt            *time.Time
	IsPersonal            bool
	Disabled              bool
	SSOLoginSlug          *string
	SSOOnly               bool
	PublicSharingDisabled bool
}

type PaymentInfo struct {
	StripeCustomerID         *string
	StripeConnectedAccountID *string
	MetronomeCustomerID      *string
}

type OrganizationWithWallet struct {
	Organization
	PaymentInfo
	Wallet *Wallet
}

type dbRow struct {
	// Organization fields
	ID                    uuid.UUID  `db:"id"`
	DisplayName           string     `db:"display_name"`
	CreatedAt             *time.Time `db:"created_at"`
	CreatedByUserID       *uuid.UUID `db:"created_by_user_id"`
	ModifiedAt            *time.Time `db:"modified_at"`
	IsPersonal            bool       `db:"is_personal"`
	Disabled              bool       `db:"disabled"`
	SSOLoginSlug          *string    `db:"sso_login_slug"`
	SSOOnly               bool       `db:"sso_only"`
	PublicSharingDisabled bool       `db:"public_sharing_disabled"`

	// Payment fields
	StripeCustomerID         *string `db:"stripe_customer_id"`
	StripeConnectedAccountID *string `db:"stripe_connected_account_id"`
	MetronomeCustomerID      *string `db:"metronome_customer_id"`

	// Wallet fields
	WalletCreditBalanceMicros   *int64 `db:"wallet_credit_balance_micros"`
	WalletInflightBalanceMicros *int64 `db:"wallet_inflight_balance_micros"`
}

const getOrganizationQuery = `
	SELECT 
		o.id,
		o.display_name,
		o.created_at,
		o.created_by_user_id,
		o.modified_at,
		o.is_personal,
		o.disabled,
		o.sso_login_slug,
		o.sso_only,
		o.public_sharing_disabled,
		o.stripe_customer_id,
		o.stripe_connected_account_id,
		o.metronome_customer_id,
		w.credit_balance_micros as wallet_credit_balance_micros,
		w.inflight_balance_micros as wallet_inflight_balance_micros
	FROM organizations o
	LEFT JOIN wallets w ON o.id = w.organization_id
	WHERE o.id = $1`

func GetOrganization(ctx context.Context, db *database.AuditLoggedPool, organizationID uuid.UUID) (*OrganizationWithWallet, error) {
	// Check for duplicate wallets
	if err := validateUniqueWallet(ctx, db, organizationID); err != nil {
		return nil, err
	}

	row, err := queryOrganization(ctx, db, organizationID)
	if err != nil {
		return nil, err
	}

	return mapToOrganizationWithWallet(row), nil
}

func validateUniqueWallet(ctx context.Context, db *database.AuditLoggedPool, organizationID uuid.UUID) error {
	rows, err := db.Query(ctx, getOrganizationQuery, organizationID)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrDatabaseQuery, err)
	}
	defer rows.Close()

	if !rows.Next() {
		return ErrOrganizationNotFound
	}

	if rows.Next() {
		return ErrMultipleWalletsFound
	}

	return nil
}

func queryOrganization(ctx context.Context, db *database.AuditLoggedPool, organizationID uuid.UUID) (*dbRow, error) {
	rows, err := db.Query(ctx, getOrganizationQuery, organizationID)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseQuery, err)
	}
	defer rows.Close()

	result, err := pgx.CollectExactlyOneRow(rows, pgx.RowToAddrOfStructByName[dbRow])
	if err != nil {
		return nil, fmt.Errorf("scanning organization row: %w", err)
	}

	return result, nil
}

func mapToOrganizationWithWallet(row *dbRow) *OrganizationWithWallet {
	org := &OrganizationWithWallet{
		Organization: Organization{
			ID:                    row.ID,
			DisplayName:           row.DisplayName,
			CreatedAt:             row.CreatedAt,
			CreatedByUserID:       row.CreatedByUserID,
			ModifiedAt:            row.ModifiedAt,
			IsPersonal:            row.IsPersonal,
			Disabled:              row.Disabled,
			SSOLoginSlug:          row.SSOLoginSlug,
			SSOOnly:               row.SSOOnly,
			PublicSharingDisabled: row.PublicSharingDisabled,
		},
		PaymentInfo: PaymentInfo{
			StripeCustomerID:         row.StripeCustomerID,
			StripeConnectedAccountID: row.StripeConnectedAccountID,
			MetronomeCustomerID:      row.MetronomeCustomerID,
		},
	}

	if row.WalletCreditBalanceMicros != nil || row.WalletInflightBalanceMicros != nil {
		org.Wallet = &Wallet{
			CreditBalanceMicros:   util.DerefInt64(row.WalletCreditBalanceMicros),
			InflightBalanceMicros: util.DerefInt64(row.WalletInflightBalanceMicros),
		}
	}

	return org
}
