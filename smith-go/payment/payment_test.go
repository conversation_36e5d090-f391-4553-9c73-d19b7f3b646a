package payment_test

import (
	"context"
	"testing"

	"langchain.com/smith/database"
	"langchain.com/smith/payment"
	"langchain.com/smith/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM wallets; DELETE FROM organizations;")
	assert.NoError(t, err)
}

func TestGetOrganization(t *testing.T) {
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	ctx := context.Background()

	// Create an organization with a wallet
	orgIDStr := testutil.OrgSetup(t, dbpool, "Test Organization", false, uuid.New().String())
	orgID := uuid.MustParse(orgIDStr)

	// Insert a wallet for the organization
	wallet := payment.Wallet{
		CreditBalanceMicros:   1000000,
		InflightBalanceMicros: 500000,
	}
	_, err := dbpool.Exec(ctx, `
		INSERT INTO wallets (organization_id, credit_balance_micros, inflight_balance_micros)
		VALUES ($1, $2, $3)
	`, orgID, wallet.CreditBalanceMicros, wallet.InflightBalanceMicros)
	assert.NoError(t, err)

	t.Run("successful retrieval with wallet", func(t *testing.T) {
		result, err := payment.GetOrganization(ctx, dbpool, orgID)
		assert.NoError(t, err)

		assert.Equal(t, orgID, result.ID)
		assert.Equal(t, "Test Organization", result.DisplayName)
		assert.NotNil(t, result.Wallet)
		assert.Equal(t, wallet.CreditBalanceMicros, result.Wallet.CreditBalanceMicros)
		assert.Equal(t, wallet.InflightBalanceMicros, result.Wallet.InflightBalanceMicros)
	})

	// Create an organization without a wallet
	orgIDStr2 := testutil.OrgSetup(t, dbpool, "Test Org No Wallet", false, uuid.New().String())
	orgID2 := uuid.MustParse(orgIDStr2)

	t.Run("successful retrieval without wallet", func(t *testing.T) {
		result, err := payment.GetOrganization(ctx, dbpool, orgID2)
		assert.NoError(t, err)

		assert.Equal(t, orgID2, result.ID)
		assert.Equal(t, "Test Org No Wallet", result.DisplayName)
		assert.Nil(t, result.Wallet)
	})

	t.Run("organization not found", func(t *testing.T) {
		nonExistentOrgID := uuid.New()
		result, err := payment.GetOrganization(ctx, dbpool, nonExistentOrgID)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "organization not found")
	})
}
