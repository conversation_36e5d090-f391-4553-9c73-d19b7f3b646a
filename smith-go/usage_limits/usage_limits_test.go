package usage_limits_test

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"testing"

	"github.com/Netflix/go-env"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"golang.org/x/sync/errgroup"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/usage_limits"
)

func DbCleanup(t *testing.T, testDbPool *database.AuditLoggedPool) {
	defer testDbPool.Close()
	_, err := testDbPool.Exec(context.Background(), "delete from organizations; delete from users; delete from api_keys;")
	assert.NoError(t, err)
}

func setupUsageLimitsTest(t *testing.T, testDbPool *database.AuditLoggedPool, routedRedisPools *lsredis.RoutedRedisPools, cachingRedisClient redis.UniversalClient) {
	if routedRedisPools == nil {
		t.Fatal("routedRedisPools is nil - required for usage limits test")
	}
	usage_limits.NewUsageLimitsClient(testDbPool, *routedRedisPools, cachingRedisClient)
}

var (
	testDbPool       *database.AuditLoggedPool
	cachingRedisPool redis.UniversalClient
	routedRedisPools *lsredis.RoutedRedisPools
)

func TestMain(m *testing.M) {
	testDbPool = database.PgConnect()
	defer testDbPool.Close()

	routedRedisPools, cachingRedisPool = testutil.InitTestRedisClients(&testing.T{})
	defer testutil.CleanupTestRoutedRedisPools(&testing.T{}, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(&testing.T{}, cachingRedisPool, true)

	setupUsageLimitsTest(nil, testDbPool, routedRedisPools, cachingRedisPool)

	code := m.Run()

	os.Exit(code)
}

func TestHasTenantExceededUsageLimits(t *testing.T) {

	setupUsageLimitsTest(t, testDbPool, routedRedisPools, cachingRedisPool)

	ctx := context.Background()

	maxEventsIngestedPerMinute := 1000
	maxHourlyTracingRequests := 50000
	maxHourlyTracingBytes := uint64(10000000)
	maxMonthlyTotalUniqueTraces := uint64(1000000)

	t.Run("Tenant has not exceeded any limits", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
			MaxHourlyTracingRequests:    maxHourlyTracingRequests,
			MaxHourlyTracingBytes:       maxHourlyTracingBytes,
			MaxMonthlyTotalUniqueTraces: maxMonthlyTotalUniqueTraces,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Test Tenant", "test-tenant", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		keysToDelete := []string{
			usage_limits.UsageLimitEventsIngestedPerMinuteCounterKey(tenantID),
			usage_limits.UsageLimitEventsIngestedPerHourCounterKey(tenantID),
			usage_limits.UsageLimitPayloadSizePerHourCounterKey(tenantID),
			usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID),
		}

		for _, delKey := range keysToDelete {
			err := routedRedisClient.Del(ctx, delKey).Err()
			assert.NoError(t, err)
		}

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, _, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
		assert.NoError(t, err)
		assert.False(t, exceeded)
	})

	t.Run("Tenant config uses defaults when empty", func(t *testing.T) {
		var tenantConfig auth.TenantConfig
		if _, err := env.UnmarshalFromEnviron(&tenantConfig); err != nil {
			slog.Error("Error loading tenant config from environment", "err", err)
			t.Fail()
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Test Tenant", "test-tenant-defaults", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		// Set values just under the defaults
		err := routedRedisClient.Set(ctx, usage_limits.UsageLimitEventsIngestedPerMinuteCounterKey(tenantID), maxEventsIngestedPerMinute-1, 0).Err()
		assert.NoError(t, err)
		err = routedRedisClient.Set(ctx, usage_limits.UsageLimitEventsIngestedPerHourCounterKey(tenantID), maxHourlyTracingRequests-1, 0).Err()
		assert.NoError(t, err)
		err = routedRedisClient.Set(ctx, usage_limits.UsageLimitPayloadSizePerHourCounterKey(tenantID), maxHourlyTracingBytes-1, 0).Err()
		assert.NoError(t, err)

		// Should not exceed any limits
		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, _, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
		assert.NoError(t, err)
		assert.False(t, exceeded)
	})

	t.Run("Tenant has exceeded MaxEventsIngestedPerMinute", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
			MaxHourlyTracingRequests:    maxHourlyTracingRequests,
			MaxHourlyTracingBytes:       maxHourlyTracingBytes,
			MaxMonthlyTotalUniqueTraces: maxMonthlyTotalUniqueTraces,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, uuid.New().String(), "test-tenant-1", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		totalEventsIngestedPerMinuteKey := usage_limits.UsageLimitEventsIngestedPerMinuteCounterKey(tenantID)
		overLimitValue := tenantConfig.MaxEventsIngestedPerMinute + 1
		err := routedRedisClient.Set(ctx, totalEventsIngestedPerMinuteKey, overLimitValue, 0).Err()
		assert.NoError(t, err)

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, message, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
		assert.NoError(t, err)
		assert.True(t, exceeded)
		assert.Contains(t, message, "Per minute events ingested rate limit exceeded")
	})

	t.Run("Tenant has exceeded MaxHourlyTracingRequests", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
			MaxHourlyTracingRequests:    maxHourlyTracingRequests,
			MaxHourlyTracingBytes:       maxHourlyTracingBytes,
			MaxMonthlyTotalUniqueTraces: maxMonthlyTotalUniqueTraces,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, uuid.New().String(), "test-tenant-2", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		totalEventsIngestedPerHourKey := usage_limits.UsageLimitEventsIngestedPerHourCounterKey(tenantID)
		overLimitValue := tenantConfig.MaxHourlyTracingRequests + 1
		err := routedRedisClient.Set(ctx, totalEventsIngestedPerHourKey, overLimitValue, 0).Err()
		assert.NoError(t, err)

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, message, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
		assert.NoError(t, err)
		assert.True(t, exceeded)
		assert.Contains(t, message, "Hourly events ingested usage limit exceeded")
	})

	t.Run("Tenant has exceeded MaxHourlyTracingBytes", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
			MaxHourlyTracingRequests:    maxHourlyTracingRequests,
			MaxHourlyTracingBytes:       maxHourlyTracingBytes,
			MaxMonthlyTotalUniqueTraces: maxMonthlyTotalUniqueTraces,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, uuid.New().String(), "test-tenant-3", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		totalPayloadSizePerHourKey := usage_limits.UsageLimitPayloadSizePerHourCounterKey(tenantID)
		overLimitValue := tenantConfig.MaxHourlyTracingBytes + 1
		err := routedRedisClient.Set(ctx, totalPayloadSizePerHourKey, overLimitValue, 0).Err()
		assert.NoError(t, err)

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, message, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
		assert.NoError(t, err)
		assert.True(t, exceeded)
		assert.Contains(t, message, "Hourly payload size usage limit exceeded")
	})

	t.Run("Tenant has exceeded MaxMonthlyTotalUniqueTraces", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
			MaxHourlyTracingRequests:    maxHourlyTracingRequests,
			MaxHourlyTracingBytes:       maxHourlyTracingBytes,
			MaxMonthlyTotalUniqueTraces: 10,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, uuid.New().String(), "test-tenant-4", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		hllKey := usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID)

		var eg errgroup.Group
		for i := 0; i < 20; i++ {
			i := i
			eg.Go(func() error {
				element := fmt.Sprintf("unique-trace-%d", i)
				return routedRedisClient.PFAdd(ctx, hllKey, element).Err()
			})
		}
		assert.NoError(t, eg.Wait())

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, message, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
		assert.NoError(t, err)
		assert.True(t, exceeded)
		assert.Contains(t, message, "Monthly unique traces usage limit exceeded")
	})

	userDefinedLimitsTests := []struct {
		name         string
		limitValue   int
		numTraces    int
		shouldExceed bool
	}{
		{
			name:         "0 limit 0 traces",
			limitValue:   0,
			numTraces:    0,
			shouldExceed: true,
		},
		{
			name:         "1 limit 0 traces",
			limitValue:   1,
			numTraces:    0,
			shouldExceed: false,
		},
		{
			name:         "0 limit",
			limitValue:   0,
			numTraces:    1,
			shouldExceed: true,
		},
		{
			name:         "1 limit",
			limitValue:   1,
			numTraces:    2,
			shouldExceed: true,
		},
		{
			name:         "2 limit",
			limitValue:   2,
			numTraces:    3,
			shouldExceed: true,
		},
		{
			name:         "2 limit 1 trace",
			limitValue:   2,
			numTraces:    1,
			shouldExceed: false,
		},
		{
			name:         "5 limit",
			limitValue:   5,
			numTraces:    6,
			shouldExceed: true,
		},
		{
			name:         "10 limit",
			limitValue:   10,
			numTraces:    11,
			shouldExceed: true,
		},
	}

	for _, tt := range userDefinedLimitsTests {
		t.Run(tt.name, func(t *testing.T) {
			tenantConfig := auth.TenantConfig{
				MaxEventsIngestedPerMinute:  maxEventsIngestedPerMinute,
				MaxHourlyTracingRequests:    maxHourlyTracingRequests,
				MaxHourlyTracingBytes:       maxHourlyTracingBytes,
				MaxMonthlyTotalUniqueTraces: maxMonthlyTotalUniqueTraces,
			}

			orgID := testutil.OrgSetup(t, testDbPool, "Test Org", false, uuid.New().String())
			tenantID := testutil.TenantSetup(t, testDbPool, orgID, uuid.New().String(), "test-tenant-"+tt.name, &tenantConfig, true)
			routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

			authInfo := auth.AuthInfo{
				TenantID:     tenantID,
				TenantConfig: &tenantConfig,
			}

			testutil.TenantUsageLimitsSetup(t, testDbPool, tenantID, usage_limits.MonthlyTraces, tt.limitValue)
			hllKey := usage_limits.MonthlyTraces.GetKeyForTenant(tenantID)

			if tt.numTraces > 0 {
				var eg errgroup.Group
				for i := 0; i < tt.numTraces; i++ {
					i := i
					eg.Go(func() error {
						element := fmt.Sprintf("unique-trace-%d", i)
						return routedRedisClient.PFAdd(ctx, hllKey, element).Err()
					})
				}
				assert.NoError(t, eg.Wait())
			}

			usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
			assert.NoError(t, err)
			exceeded, message, err := usageLimitsClient.HasTenantExceededUsageLimits(ctx, authInfo)
			assert.NoError(t, err)

			if tt.shouldExceed {
				assert.True(t, exceeded)
				expectedErrMsg := fmt.Sprintf("usage limit %s of %d exceeded", usage_limits.MonthlyTraces, tt.limitValue)
				assert.Contains(t, message, expectedErrMsg)
			} else {
				assert.False(t, exceeded)
				assert.Empty(t, message)
			}
		})
	}
}

func TestHasTenantExceededMonthlyLimits(t *testing.T) {
	testDbPool := database.PgConnect()
	defer DbCleanup(t, testDbPool)

	cachingRedisPool := lsredis.CachingRedisConnect()
	defer func(cachingRedisPool redis.UniversalClient) {
		err := cachingRedisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(cachingRedisPool)

	setupUsageLimitsTest(t, testDbPool, routedRedisPools, cachingRedisPool)
	ctx := context.Background()

	const defaultMonthlyLimit = uint64(100)

	t.Run("Tenant has not exceeded monthly unique traces limit", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxMonthlyTotalUniqueTraces: defaultMonthlyLimit,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org Not Exceeded", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Monthly Limit Tenant - Not Exceeded", "test-tenant-monthly-1", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		// Ensure HLL key is empty or under the limit
		hllKey := usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID)
		err := routedRedisClient.Del(ctx, hllKey).Err()
		assert.NoError(t, err)

		// Add fewer unique trace elements than the default limit
		for i := 0; i < 10; i++ {
			element := fmt.Sprintf("unique-trace-not-exceeded-%d", i)
			err := routedRedisClient.PFAdd(ctx, hllKey, element).Err()
			assert.NoError(t, err)
		}

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, _, err := usageLimitsClient.HasTenantExceededMonthlyLimits(ctx, authInfo)
		assert.NoError(t, err, "Expected no error when under monthly limit")
		assert.False(t, exceeded, "Expected not to exceed monthly limit")
	})

	t.Run("Tenant has exceeded monthly unique traces limit", func(t *testing.T) {
		tenantConfig := auth.TenantConfig{
			MaxMonthlyTotalUniqueTraces: 10,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org Exceeded", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Monthly Limit Tenant - Exceeded", "test-tenant-monthly-2", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		hllKey := usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID)
		err := routedRedisClient.Del(ctx, hllKey).Err()
		assert.NoError(t, err)

		// Add more unique traces than the configured limit
		for i := 0; i < 20; i++ {
			element := fmt.Sprintf("unique-trace-exceeded-%d", i)
			err := routedRedisClient.PFAdd(ctx, hllKey, element).Err()
			assert.NoError(t, err)
		}

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, message, err := usageLimitsClient.HasTenantExceededMonthlyLimits(ctx, authInfo)
		assert.True(t, exceeded, "Expected to exceed monthly limit")
		assert.NoError(t, err, "Expected no error")
		assert.Contains(t, message, "Monthly unique traces usage limit exceeded")
	})

	t.Run("Tenant has exceeded a user-defined monthly limit", func(t *testing.T) {
		// Keep the main config fairly high so only the user-defined limit triggers
		tenantConfig := auth.TenantConfig{
			MaxMonthlyTotalUniqueTraces: 10000,
		}

		orgID := testutil.OrgSetup(t, testDbPool, "Test Org User Defined", false, uuid.New().String())
		tenantID := testutil.TenantSetup(t, testDbPool, orgID, "Monthly Limit Tenant - User Defined", "test-tenant-monthly-3", &tenantConfig, true)
		routedRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, tenantID, lsredis.RedisOperationIngestion)

		authInfo := auth.AuthInfo{
			TenantID:     tenantID,
			TenantConfig: &tenantConfig,
		}

		// Set up a user-defined limit of 5 monthly traces
		testutil.TenantUsageLimitsSetup(t, testDbPool, tenantID, usage_limits.MonthlyTraces, 5)

		hllKey := usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(tenantID)

		err := routedRedisClient.Del(ctx, hllKey).Err()
		assert.NoError(t, err)

		// Add 10 unique traces to blow past the user-defined limit of 5
		for i := 0; i < 10; i++ {
			element := fmt.Sprintf("unique-trace-user-defined-%d", i)
			err := routedRedisClient.PFAdd(ctx, hllKey, element).Err()
			assert.NoError(t, err)
		}

		usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
		assert.NoError(t, err)
		exceeded, message, err := usageLimitsClient.HasTenantExceededMonthlyLimits(ctx, authInfo)
		assert.NoError(t, err, "Expected no error")
		assert.True(t, exceeded, "Expected to exceed the user-defined monthly limit")
		expectedSubstring := fmt.Sprintf("usage limit %s of %d exceeded", usage_limits.MonthlyTraces, 5)
		assert.Contains(t, message, expectedSubstring)
	})
}
