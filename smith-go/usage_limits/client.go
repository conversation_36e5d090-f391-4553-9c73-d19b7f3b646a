package usage_limits

import (
	"errors"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
)

type UsageLimitsClient struct {
	db               *database.AuditLoggedPool
	routedRedisPools *lsredis.RoutedRedisPools
	cache            *lsredis.Cache[string, []UsageLimit]
}

var (
	usageLimitsClientInstance *UsageLimitsClient
	usageLimitsClientOnce     sync.Once
)

func NewUsageLimitsClient(db *database.AuditLoggedPool, routedRedisPools lsredis.RoutedRedisPools, cachingRedisClient redis.UniversalClient) *UsageLimitsClient {
	usageLimitsClientOnce.Do(func() {
		usageLimitsClientInstance = &UsageLimitsClient{
			db:               db,
			routedRedisPools: &routedRedisPools,
			cache:            lsredis.NewCache[string, []UsageLimit](cachingRedisClient, "usage_limits", 60*time.Second),
		}
	})
	return usageLimitsClientInstance
}

func GetUsageLimitsClient() (*UsageLimitsClient, error) {
	if usageLimitsClientInstance == nil {
		return nil, errors.New("usage limits client not initialized")
	}
	return usageLimitsClientInstance, nil
}
