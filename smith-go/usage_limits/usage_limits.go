package usage_limits

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strconv"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"
)

type UsageLimitType string

const (
	MonthlyTraces          UsageLimitType = "monthly_traces"
	MonthlyLonglivedTraces UsageLimitType = "monthly_longlived_traces"

	// Usage limit exceeded messages
	PerMinuteEventsIngestedLimitExceeded = "Per minute events ingested rate limit exceeded"
	HourlyEventsIngestedLimitExceeded    = "Hourly events ingested usage limit exceeded"
	HourlyPayloadSizeLimitExceeded       = "Hourly payload size usage limit exceeded"
	MonthlyUniqueTracesLimitExceeded     = "Monthly unique traces usage limit exceeded"
)

type UsageLimitUnit string

const (
	UsageLimitUnitTraces          UsageLimitUnit = "traces"
	UsageLimitUnitLonglivedTraces UsageLimitUnit = "longlived_traces"
)

type UsageLimit struct {
	ID         string         `json:"id"`
	LimitType  UsageLimitType `json:"limit_type"`
	LimitValue int            `json:"limit_value"`
	TenantID   string         `json:"tenant_id"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
}

func (t UsageLimitType) GetUnit() UsageLimitUnit {
	switch t {
	case MonthlyTraces:
		return UsageLimitUnitTraces
	case MonthlyLonglivedTraces:
		return UsageLimitUnitLonglivedTraces
	default:
		panic(fmt.Sprintf("Unknown limit type %s", t))
	}
}

func (t UsageLimitType) GetKeyForTenant(tenantID string) string {
	switch t {
	case MonthlyTraces:
		return UsageLimitUniqueTracesPerMonthHLLKey(tenantID)
	case MonthlyLonglivedTraces:
		return UsageLimitUniqueLonglivedTracesPerMonthHLLKey(tenantID)
	default:
		panic(fmt.Sprintf("Unknown limit type %s", t))
	}
}

func (u *UsageLimit) GetRejectKey() string {
	return fmt.Sprintf("smith:user_defined_usage_limit_reject:%s:%s", u.LimitType, u.TenantID)
}

func (u *UsageLimit) CheckRejectSetFromPipe(ctx context.Context, pipe redis.Pipeliner) *redis.IntCmd {
	return pipe.Exists(ctx, u.GetRejectKey())
}

func (u *UsageLimit) GetCurrentLevelFromPipe(ctx context.Context, pipe redis.Pipeliner) *redis.IntCmd {
	key := u.LimitType.GetKeyForTenant(u.TenantID)
	return pipe.PFCount(ctx, key)
}

func (u *UsageLimit) GetRejectSetExpiry() time.Duration {
	return time.Duration(config.Env.UsageLimitRejectExpirySec) * time.Second
}

func (u *UsageLimit) CheckLevelWithPipe(ctx context.Context, oplog *slog.Logger, currentLevel uint64, pipe redis.Pipeliner) string {
	expiry := u.GetRejectSetExpiry()
	if currentLevel >= uint64(u.LimitValue) {
		oplog.Warn("Usage Limit Exceeded", "tenant_id", u.TenantID, "limit", u.LimitType, "limit_value", u.LimitValue, "current_value", currentLevel)
		pipe.SetNX(ctx, u.GetRejectKey(), "1", expiry)
		return fmt.Sprintf("usage limit %s of %d exceeded", u.LimitType, u.LimitValue)
	}
	return ""
}

func currentMinute() string {
	return time.Now().UTC().Format("2006-01-02:15:04")
}

func currentHour() string {
	return time.Now().UTC().Format("2006-01-02:15")
}

func currentMonth() string {
	return time.Now().UTC().Format("2006-01")
}

func ingestionEventsPerMinuteLimitRejectKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_reject:total_requests:%s:%s", currentMinute(), tenantID)
}

func hourlyPayloadSizeLimitRejectKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_reject:payload_size:%s:%s", currentHour(), tenantID)
}

func hourlyEventsIngestedLimitRejectKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_reject:total_requests:%s:%s", currentHour(), tenantID)
}

func monthlyUsageLimitRejectKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_reject:unique_traces:%s:%s", currentMonth(), tenantID)
}

func UsageLimitUniqueTracesPerMonthHLLKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_unique_run_ids_hll:%s:%s", currentMonth(), tenantID)
}

func UsageLimitUniqueLonglivedTracesPerMonthHLLKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_unique_longlived_run_ids_hll:%s:%s", currentMonth(), tenantID)
}

func UsageLimitEventsIngestedPerMinuteCounterKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_total_events_ingested_per_minute:%s:%s", currentMinute(), tenantID)
}

func UsageLimitEventsIngestedPerHourCounterKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_total_requests:%s:%s", currentHour(), tenantID)
}

func UsageLimitPayloadSizePerHourCounterKey(tenantID string) string {
	return fmt.Sprintf("smith:runs:usage_limit_payload_size:%s:%s", currentHour(), tenantID)
}

func SetRejectKey(ctx context.Context, oplog *slog.Logger, pipe redis.Pipeliner, rejectKey string, tenantID string, limitName string) {
	oplog.Warn("Limit Exceeded", "tenant_id", tenantID, "limit", limitName)
	expiry := time.Duration(config.Env.RateLimitRejectExpirySec) * time.Second
	pipe.SetNX(ctx, rejectKey, "1", expiry)
}

func (c *UsageLimitsClient) listUserDefinedUsageLimitsUncached(ctx context.Context, authInfo auth.AuthInfo) ([]UsageLimit, error) {
	rows, err := c.db.Query(ctx, `
        SELECT id, limit_type, limit_value, tenant_id, created_at, updated_at
        FROM per_tenant_usage_limits
        WHERE tenant_id = $1
    `, authInfo.TenantID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var limits []UsageLimit
	for rows.Next() {
		var limit UsageLimit
		if err := rows.Scan(&limit.ID, &limit.LimitType, &limit.LimitValue, &limit.TenantID, &limit.CreatedAt, &limit.UpdatedAt); err != nil {
			return nil, err
		}
		limits = append(limits, limit)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return limits, nil
}

func (c *UsageLimitsClient) ListUserDefinedUsageLimits(ctx context.Context, authInfo auth.AuthInfo) ([]UsageLimit, error) {
	cacheKey := "usage_limits:" + authInfo.TenantID

	limits, err := c.cache.GetFresh(ctx, cacheKey, func() ([]UsageLimit, error) {
		return c.listUserDefinedUsageLimitsUncached(ctx, authInfo)
	})

	if err != nil {
		return nil, err
	}

	return limits, nil
}

func (c *UsageLimitsClient) HasTenantExceededUsageLimits(ctx context.Context, authInfo auth.AuthInfo) (bool, string, error) {
	if config.Env.IsSelfHosted {
		return false, "", nil
	}

	oplog := httplog.LogEntry(ctx)

	ingestionEventsPerMinuteRejectKey := ingestionEventsPerMinuteLimitRejectKey(authInfo.TenantID)
	hourlyPayloadSizeRejectKey := hourlyPayloadSizeLimitRejectKey(authInfo.TenantID)
	hourlyTotalRequestsRejectKey := hourlyEventsIngestedLimitRejectKey(authInfo.TenantID)
	monthlyRejectKey := monthlyUsageLimitRejectKey(authInfo.TenantID)
	hllKey := UsageLimitUniqueTracesPerMonthHLLKey(authInfo.TenantID)

	userDefinedTraceLimits, err := c.ListUserDefinedUsageLimits(ctx, authInfo)
	if err != nil {
		return false, "", fmt.Errorf("error listing user-defined usage limits: %w", err)
	}

	var filteredUserDefinedLimits []UsageLimit
	for _, limit := range userDefinedTraceLimits {
		if limit.LimitType == MonthlyTraces {
			filteredUserDefinedLimits = append(filteredUserDefinedLimits, limit)
		}
	}

	client := c.routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	pipe := client.TxPipeline()

	totalUniqueTraceCountCmd := pipe.PFCount(ctx, hllKey)
	tenantRejectedTotalRequestsPerMinuteCmd := pipe.Exists(ctx, ingestionEventsPerMinuteRejectKey)
	tenantRejectedPayloadSizeCmd := pipe.Exists(ctx, hourlyPayloadSizeRejectKey)
	tenantRejectedTotalRequestsCmd := pipe.Exists(ctx, hourlyTotalRequestsRejectKey)
	tenantRejectedMonthlyCmd := pipe.Exists(ctx, monthlyRejectKey)

	totalEventsIngestedPerMinuteKey := UsageLimitEventsIngestedPerMinuteCounterKey(authInfo.TenantID)
	totalEventsIngestedPerHourKey := UsageLimitEventsIngestedPerHourCounterKey(authInfo.TenantID)
	totalPayloadSizePerHourKey := UsageLimitPayloadSizePerHourCounterKey(authInfo.TenantID)

	totalEventsIngestedPerMinuteCmd := pipe.Get(ctx, totalEventsIngestedPerMinuteKey)
	totalEventsIngestedPerHourCmd := pipe.Get(ctx, totalEventsIngestedPerHourKey)
	totalPayloadSizePerHourCmd := pipe.Get(ctx, totalPayloadSizePerHourKey)

	userDefinedRejectCmds := make([]*redis.IntCmd, len(filteredUserDefinedLimits))
	userDefinedCurrentLevelCmds := make([]*redis.IntCmd, len(filteredUserDefinedLimits))
	for i, limit := range filteredUserDefinedLimits {
		userDefinedRejectCmds[i] = limit.CheckRejectSetFromPipe(ctx, pipe)
		userDefinedCurrentLevelCmds[i] = limit.GetCurrentLevelFromPipe(ctx, pipe)
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error executing Redis pipeline: %w", err)
	}

	totalUniqueTraceCount, err := totalUniqueTraceCountCmd.Uint64()
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error getting totalUniqueTraceCount: %w", err)
	}

	tenantRejectedTotalRequestsPerMinute := tenantRejectedTotalRequestsPerMinuteCmd.Val()
	tenantRejectedPayloadSize := tenantRejectedPayloadSizeCmd.Val()
	tenantRejectedTotalRequests := tenantRejectedTotalRequestsCmd.Val()
	tenantRejectedMonthly := tenantRejectedMonthlyCmd.Val()

	totalEventsIngestedPerMinuteStr, err := totalEventsIngestedPerMinuteCmd.Result()
	totalEventsIngestedPerMinute := 0
	if err == nil {
		totalEventsIngestedPerMinute, _ = strconv.Atoi(totalEventsIngestedPerMinuteStr)
	}

	totalEventsIngestedPerHourStr, err := totalEventsIngestedPerHourCmd.Result()
	totalEventsIngestedPerHour := 0
	if err == nil {
		totalEventsIngestedPerHour, _ = strconv.Atoi(totalEventsIngestedPerHourStr)
	}

	totalPayloadSizePerHourStr, err := totalPayloadSizePerHourCmd.Result()
	totalPayloadSizePerHour := uint64(0)
	if err == nil {
		totalPayloadSizePerHour, _ = strconv.ParseUint(totalPayloadSizePerHourStr, 10, 64)
	}

	userDefinedRejects := make([]uint64, len(filteredUserDefinedLimits))
	userDefinedCurrentLevels := make([]uint64, len(filteredUserDefinedLimits))
	for i := range filteredUserDefinedLimits {
		userDefinedRejects[i], _ = userDefinedRejectCmds[i].Uint64()
		userDefinedCurrentLevels[i], _ = userDefinedCurrentLevelCmds[i].Uint64()
	}

	if tenantRejectedTotalRequestsPerMinute > 0 {
		return true, PerMinuteEventsIngestedLimitExceeded, nil
	}
	if tenantRejectedTotalRequests > 0 {
		return true, HourlyEventsIngestedLimitExceeded, nil
	}
	if tenantRejectedPayloadSize > 0 {
		return true, HourlyPayloadSizeLimitExceeded, nil
	}
	if tenantRejectedMonthly > 0 {
		return true, MonthlyUniqueTracesLimitExceeded, nil
	}

	for i, limit := range filteredUserDefinedLimits {
		if userDefinedRejects[i] > 0 {
			return true, fmt.Sprintf("usage limit %s of %d exceeded", limit.LimitType, limit.LimitValue), nil
		}
	}

	client = c.routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	pipe = client.TxPipeline()
	var limitExceededMessage string

	for i, limit := range filteredUserDefinedLimits {
		message := limit.CheckLevelWithPipe(ctx, oplog, userDefinedCurrentLevels[i], pipe)
		if message != "" && limitExceededMessage == "" {
			limitExceededMessage = message
		}
	}

	if totalEventsIngestedPerMinute > 0 &&
		totalEventsIngestedPerMinute > authInfo.TenantConfig.MaxEventsIngestedPerMinute {
		SetRejectKey(ctx, oplog, pipe, ingestionEventsPerMinuteRejectKey, authInfo.TenantID, "events_ingested_per_minute")
		if limitExceededMessage == "" {
			limitExceededMessage = PerMinuteEventsIngestedLimitExceeded
		}
	}

	if totalEventsIngestedPerHour > 0 &&
		totalEventsIngestedPerHour > authInfo.TenantConfig.MaxHourlyTracingRequests {
		SetRejectKey(ctx, oplog, pipe, hourlyTotalRequestsRejectKey, authInfo.TenantID, "events_ingested_per_hour")
		if limitExceededMessage == "" {
			limitExceededMessage = HourlyEventsIngestedLimitExceeded
		}
	}

	if totalPayloadSizePerHour > 0 &&
		totalPayloadSizePerHour > authInfo.TenantConfig.MaxHourlyTracingBytes {
		SetRejectKey(ctx, oplog, pipe, hourlyPayloadSizeRejectKey, authInfo.TenantID, "payload_size")
		if limitExceededMessage == "" {
			limitExceededMessage = HourlyPayloadSizeLimitExceeded
		}
	}

	if totalUniqueTraceCount > 0 &&
		totalUniqueTraceCount > authInfo.TenantConfig.MaxMonthlyTotalUniqueTraces {
		SetRejectKey(ctx, oplog, pipe, monthlyRejectKey, authInfo.TenantID, "total_unique_traces")
		if limitExceededMessage == "" {
			limitExceededMessage = MonthlyUniqueTracesLimitExceeded
		}
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error executing Redis pipeline: %w", err)
	}

	if limitExceededMessage != "" {
		return true, limitExceededMessage, nil
	}

	return false, "", nil
}

func (c *UsageLimitsClient) HasTenantExceededMonthlyLimits(ctx context.Context, authInfo auth.AuthInfo) (bool, string, error) {
	if config.Env.IsSelfHosted {
		return false, "", nil
	}

	oplog := httplog.LogEntry(ctx)

	monthlyRejectKey := monthlyUsageLimitRejectKey(authInfo.TenantID)
	hllKey := UsageLimitUniqueTracesPerMonthHLLKey(authInfo.TenantID)

	userDefinedLimits, err := c.ListUserDefinedUsageLimits(ctx, authInfo)
	if err != nil {
		return false, "", fmt.Errorf("error listing user-defined usage limits: %w", err)
	}

	var traceLimits []UsageLimit
	for _, limit := range userDefinedLimits {
		if limit.LimitType == MonthlyTraces {
			traceLimits = append(traceLimits, limit)
		}
	}

	client := c.routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	pipe := client.TxPipeline()

	totalUniqueTraceCountCmd := pipe.PFCount(ctx, hllKey)
	tenantRejectedMonthlyCmd := pipe.Exists(ctx, monthlyRejectKey)

	userDefinedRejectCmds := make([]*redis.IntCmd, len(traceLimits))
	userDefinedCurrentLevelCmds := make([]*redis.IntCmd, len(traceLimits))
	for i, limit := range traceLimits {
		userDefinedRejectCmds[i] = limit.CheckRejectSetFromPipe(ctx, pipe)
		userDefinedCurrentLevelCmds[i] = limit.GetCurrentLevelFromPipe(ctx, pipe)
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error executing Redis pipeline: %w", err)
	}

	totalUniqueTraceCount, err := totalUniqueTraceCountCmd.Uint64()
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error getting totalUniqueTraceCount: %w", err)
	}

	tenantRejectedMonthly := tenantRejectedMonthlyCmd.Val()

	userDefinedRejects := make([]uint64, len(traceLimits))
	userDefinedCurrentLevels := make([]uint64, len(traceLimits))
	for i := range traceLimits {
		userDefinedRejects[i], _ = userDefinedRejectCmds[i].Uint64()
		userDefinedCurrentLevels[i], _ = userDefinedCurrentLevelCmds[i].Uint64()
	}

	if tenantRejectedMonthly > 0 {
		return true, MonthlyUniqueTracesLimitExceeded, nil
	}

	for i, limit := range traceLimits {
		if userDefinedRejects[i] > 0 {
			return true, fmt.Sprintf("user-defined usage limit %s exceeded", limit.LimitType), nil
		}
	}

	client = c.routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	pipe = client.TxPipeline()
	var limitExceededMessage string

	for i, limit := range traceLimits {
		message := limit.CheckLevelWithPipe(ctx, oplog, userDefinedCurrentLevels[i], pipe)
		if message != "" && limitExceededMessage == "" {
			limitExceededMessage = message
		}
	}

	if totalUniqueTraceCount > 0 &&
		totalUniqueTraceCount > authInfo.TenantConfig.MaxMonthlyTotalUniqueTraces {
		SetRejectKey(
			ctx,
			oplog,
			pipe,
			monthlyRejectKey,
			authInfo.TenantID,
			"total_unique_traces",
		)
		if limitExceededMessage == "" {
			limitExceededMessage = MonthlyUniqueTracesLimitExceeded
		}
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error executing final Redis pipeline: %w", err)
	}

	if limitExceededMessage != "" {
		return true, limitExceededMessage, nil
	}

	return false, "", nil
}

func (c *UsageLimitsClient) CheckLonglivedUsageLimits(
	ctx context.Context,
	authInfo auth.AuthInfo,
) (bool, string, error) {
	if config.Env.IsSelfHosted {
		return false, "", nil
	}

	oplog := httplog.LogEntry(ctx)

	usageLimits, err := c.ListUserDefinedUsageLimits(ctx, authInfo)
	if err != nil {
		return false, "", fmt.Errorf("error listing user-defined usage limits: %w", err)
	}

	var longlivedUsageLimits []UsageLimit
	for _, limit := range usageLimits {
		unit := limit.LimitType.GetUnit()
		if unit == UsageLimitUnitLonglivedTraces {
			longlivedUsageLimits = append(longlivedUsageLimits, limit)
		}
	}

	if len(longlivedUsageLimits) == 0 {
		return false, "", nil
	}

	client := c.routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	pipe := client.TxPipeline()
	var cmds []redis.Cmder

	for _, limit := range longlivedUsageLimits {
		cmds = append(cmds, limit.CheckRejectSetFromPipe(ctx, pipe))
		cmds = append(cmds, limit.GetCurrentLevelFromPipe(ctx, pipe))
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error executing Redis pipeline: %w", err)
	}

	rejects := make([]uint64, len(longlivedUsageLimits))
	currentLevels := make([]uint64, len(longlivedUsageLimits))

	for i := 0; i < len(cmds); i += 2 {
		reject, err := cmds[i].(*redis.IntCmd).Uint64()
		if err != nil && err != redis.Nil {
			return false, "", fmt.Errorf("error getting reject result: %w", err)
		}
		rejects[i/2] = reject

		currentLevel, err := cmds[i+1].(*redis.IntCmd).Uint64()
		if err != nil && err != redis.Nil {
			return false, "", fmt.Errorf("error getting current level: %w", err)
		}
		currentLevels[i/2] = currentLevel
	}

	for i, limit := range longlivedUsageLimits {
		if rejects[i] > 0 {
			return true, fmt.Sprintf("usage limit %s of %d exceeded", limit.LimitType, limit.LimitValue), nil
		}
	}

	client = c.routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	pipe = client.TxPipeline()
	var limitExceededMessage string

	for i, limit := range longlivedUsageLimits {
		message := limit.CheckLevelWithPipe(ctx, oplog, currentLevels[i], pipe)
		if message != "" && limitExceededMessage == "" {
			limitExceededMessage = message
		}
	}

	_, err = pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return false, "", fmt.Errorf("error executing Redis pipeline: %w", err)
	}

	if limitExceededMessage != "" {
		return true, limitExceededMessage, nil
	}

	return false, "", nil
}

func GetAllLimits(unit UsageLimitUnit) []UsageLimitType {
	switch unit {
	case UsageLimitUnitTraces:
		return []UsageLimitType{MonthlyTraces}
	case UsageLimitUnitLonglivedTraces:
		return []UsageLimitType{MonthlyLonglivedTraces}
	default:
		panic(fmt.Sprintf("Unknown limit unit %s", unit))
	}
}

func (t UsageLimitType) MarkSeenEventsInPipe(ctx context.Context, eventIDs map[uuid.UUID]struct{}, pipe redis.Pipeliner, tenantID string) {
	oplog := httplog.LogEntry(ctx)

	key := t.GetKeyForTenant(tenantID)

	events := make([]interface{}, 0, len(eventIDs))
	for id := range eventIDs {
		jsonBytes, err := json.Marshal(id)
		if err != nil {
			oplog.Error("Failed to marshal event ID",
				"error", err,
				"event_id", id,
				"tenant_id", tenantID)
			continue
		}
		events = append(events, string(jsonBytes))
	}

	if len(events) > 0 {
		pipe.PFAdd(ctx, key, events...)

		nextMonth := time.Now().UTC().AddDate(0, 1, 0)
		firstOfNextMonth := time.Date(
			nextMonth.Year(),
			nextMonth.Month(),
			1,
			0, 0, 0, 0,
			time.UTC,
		)

		pipe.ExpireAt(ctx, key, firstOfNextMonth)
	}
}
