package redis

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/cespare/xxhash/v2"

	"github.com/redis/go-redis/v9"
	"golang.org/x/sync/singleflight"
)

var stripOutHeaders = []string{
	"Access-Control-Allow-Credentials",
	"Access-Control-Allow-Headers",
	"Access-Control-Allow-Methods",
	"Access-Control-Allow-Origin",
	"Access-Control-Expose-Headers",
	"Access-Control-Max-Age",
	"Access-Control-Request-Headers",
	"Access-Control-Request-Method",
}

type Cache[K comparable, V any] struct {
	redisClient redis.UniversalClient
	prefix      string
	ttl         time.Duration
	group       *singleflight.Group
}

func NewCache[K comparable, V any](redisClient redis.UniversalClient, prefix string, ttl time.Duration) *Cache[K, V] {
	return &Cache[K, V]{
		redisClient: redisClient,
		prefix:      prefix,
		ttl:         ttl,
		group:       &singleflight.Group{},
	}
}

func (c *Cache[K, V]) GetFresh(ctx context.Context, key K, fetchFunc func() (V, error)) (V, error) {
	var zeroV V
	redisKey := fmt.Sprintf("%s:%v", c.prefix, key)

	// Try to get the value from Redis
	val, err := c.redisClient.Get(ctx, redisKey).Result()
	if err == nil {
		var v V
		if err := json.Unmarshal([]byte(val), &v); err != nil {
			return zeroV, err
		}
		return v, nil
	} else if err != redis.Nil {
		return zeroV, err
	}

	// Use singleflight to prevent cache stampede
	untypedValue, err, _ := c.group.Do(redisKey, func() (interface{}, error) {
		v, err := fetchFunc()
		if err != nil {
			return nil, err
		}
		data, err := json.Marshal(v)
		if err != nil {
			return nil, err
		}
		// Don't cache if ttl is 0
		if c.ttl != 0 {
			if err := c.redisClient.Set(ctx, redisKey, data, c.ttl).Err(); err != nil {
				return nil, err
			}
		}
		return v, nil
	})
	if err != nil {
		return zeroV, err
	}
	return untypedValue.(V), nil
}

func (c *Cache[K, V]) Delete(ctx context.Context, key K) error {
	redisKey := fmt.Sprintf("%s:%v", c.prefix, key)
	return c.redisClient.Del(ctx, redisKey).Err()
}

func BytesToHash(b ...[]byte) uint64 {
	d := xxhash.New()
	for _, v := range b {
		d.Write(v)
	}
	return d.Sum64()
}

func StringToHash(s ...string) uint64 {
	d := xxhash.New()
	for _, v := range s {
		d.WriteString(v)
	}
	return d.Sum64()
}

// From Stampede library, modified to use Redis cache
// HandlerWithKey allows specifying a custom key function for caching.
func HandlerWithKey(redisClient redis.UniversalClient, ttl time.Duration, keyFunc func(r *http.Request) uint64, prefix string, paths ...string) func(next http.Handler) http.Handler {
	// Mapping of URL paths that are cacheable by the stampede handler
	pathMap := map[string]struct{}{}
	for _, path := range paths {
		pathMap[strings.ToLower(path)] = struct{}{}
	}

	// Stampede handler with set TTL for how long content is fresh.
	// Requests sent to this handler will be coalesced, and in scenarios
	// where there is a "stampede" or parallel requests for the same
	// method and arguments, there will be just a single handler that
	// executes, and the remaining handlers will use the response from
	// the first request. The content thereafter will be cached for up to
	// TTL time for subsequent requests for further caching.
	h := redisCacheHandler(redisClient, ttl, keyFunc, prefix)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Cache all paths if whitelist has not been provided
			if len(pathMap) == 0 {
				h(next).ServeHTTP(w, r)
				return
			}

			// Match specific whitelist of paths
			if _, ok := pathMap[strings.ToLower(r.URL.Path)]; ok {
				// Stampede-cache the matching path
				h(next).ServeHTTP(w, r)
			} else {
				// No caching
				next.ServeHTTP(w, r)
			}
		})
	}
}

// redisCacheHandler creates a caching middleware using stampede.
func redisCacheHandler(redisClient redis.UniversalClient, ttl time.Duration, keyFunc func(r *http.Request) uint64, prefix string) func(next http.Handler) http.Handler {
	cache := NewCache[uint64, responseValue](redisClient, prefix, ttl)
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

			// Cache key for the request
			key := keyFunc(r)

			// Mark the request that actually processes the response
			first := false

			// Process request (single flight)
			respVal, err := cache.GetFresh(r.Context(), key, func() (responseValue, error) {
				first = true
				buf := bytes.NewBuffer(nil)
				ww := &responseWriter{ResponseWriter: w, tee: buf}

				next.ServeHTTP(ww, r)

				val := responseValue{
					headers: ww.Header(),
					status:  ww.Status(),
					body:    buf.Bytes(),

					// The handler may not write header and body in some logic,
					// while writing only the body, an attempt is made to write the default header (http.StatusOK)
					skip: ww.IsHeaderWrong(),
				}
				return val, nil
			})

			// The first request to trigger the fetch should return as it's already
			// responded to the client
			if first {
				return
			}

			// Handle response for other listeners
			if err != nil {
				// You might want to log the error and execute the standard handler instead
				panic(fmt.Sprintf("stampede: failed to get value, %v", err))
			}

			if respVal.skip {
				return
			}

			header := w.Header()

		nextHeader:
			for k := range respVal.headers {
				for _, match := range stripOutHeaders {
					// Prevent any header in stripOutHeaders from overriding the current
					// value of that header. This is important when you don't want a
					// header to affect all subsequent requests (e.g., when working with several CORS domains, you don't want the first domain to be recorded and to be printed in all responses)
					if match == k {
						continue nextHeader
					}
				}
				header[k] = respVal.headers[k]
			}

			w.WriteHeader(respVal.status)
			w.Write(respVal.body)
		})
	}
}

type responseValue struct {
	headers http.Header
	status  int
	body    []byte
	skip    bool
}

type responseWriter struct {
	http.ResponseWriter
	wroteHeader bool
	code        int
	bytes       int
	tee         io.Writer
}

func (b *responseWriter) WriteHeader(code int) {
	if !b.wroteHeader {
		b.code = code
		b.wroteHeader = true
		b.ResponseWriter.WriteHeader(code)
	}
}

func (b *responseWriter) IsHeaderWrong() bool {
	return !b.wroteHeader && (b.code < 100 || b.code > 999)
}

func (b *responseWriter) Write(buf []byte) (int, error) {
	b.maybeWriteHeader()
	n, err := b.ResponseWriter.Write(buf)
	if b.tee != nil {
		_, err2 := b.tee.Write(buf[:n])
		if err == nil {
			err = err2
		}
	}
	b.bytes += n
	return n, err
}

func (b *responseWriter) maybeWriteHeader() {
	if !b.wroteHeader {
		b.WriteHeader(http.StatusOK)
	}
}

func (b *responseWriter) Status() int {
	return b.code
}

func (b *responseWriter) BytesWritten() int {
	return b.bytes
}
