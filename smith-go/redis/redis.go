package redis

import (
	"context"
	"net/url"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
)

func getSingleRedisInstanceOptions(redisURL string) *redis.UniversalOptions {
	parsedRedisUrl, err := url.Parse(redisURL)
	if err != nil {
		panic(err)
	}
	// Move username and password from Query to UserInfo scheme. This is because redis-py handles query params but Go doesn't
	query := parsedRedisUrl.Query()
	username := ""
	password := ""
	if query.Has("password") {
		password = query.Get("password")
		query.Del("password")
		username = "default"
	}
	if query.Has("username") {
		username = query.Get("username")
		query.Del("user")
	}
	sslCertReqs := query.Get("ssl_cert_reqs")
	if sslCertReqs != "" {
		query.Del("ssl_cert_reqs")
	}
	parsedRedisUrl.RawQuery = query.Encode()
	strippedRedisUrl := parsedRedisUrl.String()
	opts, err := redis.ParseURL(strippedRedisUrl)
	if err != nil {
		panic(err)
	}
	if password != "" {
		opts.Password = password
		opts.Username = username
	}
	if sslCertReqs == "none" {
		opts.TLSConfig.InsecureSkipVerify = true
	}

	// translate to universal client options
	return &redis.UniversalOptions{
		Addrs:           []string{opts.Addr}, // Convert single address to slice
		DB:              opts.DB,
		Username:        opts.Username,
		Password:        opts.Password,
		MaxRetries:      opts.MaxRetries,
		MinRetryBackoff: opts.MinRetryBackoff,
		MaxRetryBackoff: opts.MaxRetryBackoff,
		DialTimeout:     opts.DialTimeout,
		ReadTimeout:     opts.ReadTimeout,
		WriteTimeout:    opts.WriteTimeout,
		PoolSize:        opts.PoolSize,
		PoolTimeout:     opts.PoolTimeout,
		MinIdleConns:    opts.MinIdleConns,
		MaxIdleConns:    opts.MaxIdleConns,
		ConnMaxIdleTime: opts.ConnMaxIdleTime,
		ConnMaxLifetime: opts.ConnMaxLifetime,
		TLSConfig:       opts.TLSConfig,
		ClientName:      opts.ClientName,
	}
}

func getRedisClusterOptions(redisURLs []string) *redis.UniversalOptions {
	firstInstanceOptions := getSingleRedisInstanceOptions(redisURLs[0])
	var formatRedisURLs []string
	for _, redisURL := range redisURLs {
		parsedRedisUrl, err := redis.ParseURL(redisURL)
		if err != nil {
			panic(err)
		}
		formatRedisURLs = append(formatRedisURLs, parsedRedisUrl.Addr)
	}

	opts := &redis.UniversalOptions{
		Addrs:           formatRedisURLs,
		DB:              firstInstanceOptions.DB,
		Username:        firstInstanceOptions.Username,
		Password:        firstInstanceOptions.Password,
		MaxRetries:      firstInstanceOptions.MaxRetries,
		MinRetryBackoff: firstInstanceOptions.MinRetryBackoff,
		MaxRetryBackoff: firstInstanceOptions.MaxRetryBackoff,
		DialTimeout:     firstInstanceOptions.DialTimeout,
		ReadTimeout:     firstInstanceOptions.ReadTimeout,
		WriteTimeout:    firstInstanceOptions.WriteTimeout,
		PoolSize:        firstInstanceOptions.PoolSize,
		PoolTimeout:     firstInstanceOptions.PoolTimeout,
		MinIdleConns:    firstInstanceOptions.MinIdleConns,
		MaxIdleConns:    firstInstanceOptions.MaxIdleConns,
		ConnMaxIdleTime: firstInstanceOptions.ConnMaxIdleTime,
		ConnMaxLifetime: firstInstanceOptions.ConnMaxLifetime,
		TLSConfig:       firstInstanceOptions.TLSConfig,
	}
	return opts
}

// NewUniversalClient creates a new universal redis client
// this is a re-impl of https://github.com/redis/go-redis/blob/46484324a51770d4e39cad4838e6d0ec24e391cc/universal.go#L269
// this is needed because we need to pass a credentials provider to the cluster client and
// the UniversalOptions struct doesn't have a field for a credentials provider
func NewUniversalClient(opts *redis.UniversalOptions) redis.UniversalClient {
	if len(opts.Addrs) > 1 {
		clusterOpts := opts.Cluster()
		// if IAM auth is enabled, we need to provide a credentials provider
		if config.Env.RedisClusterIAMAuthEnabled {
			clusterOpts.CredentialsProvider = func() (username string, password string) {
				token, err := GetCredentials(context.Background())
				if err != nil {
					return "", ""
				}
				return "default", token
			}
		}
		return redis.NewClusterClient(clusterOpts)
	}
	return redis.NewClient(opts.Simple())
}

func SingleRedisConnect() redis.UniversalClient {
	redisURL := config.Env.RedisDatabaseURI
	opts := getSingleRedisInstanceOptions(redisURL)
	client := NewUniversalClient(opts)
	if config.Env.DatadogEnabled {
		client.AddHook(&DatadogHook{})
	}
	return client
}

func CachingRedisConnect() redis.UniversalClient {
	redisURL := config.Env.RedisCachingDatabaseURI
	opts := getSingleRedisInstanceOptions(redisURL)
	client := NewUniversalClient(opts)
	if config.Env.DatadogEnabled {
		client.AddHook(&DatadogHook{})
	}
	return client
}

func RedisClusterConnect() redis.UniversalClient {
	// If cluster is not enabled, return the single instance queueing client
	if !config.Env.RedisClusterEnabled {
		return SingleRedisConnect()
	}
	var opts *redis.UniversalOptions
	if !config.Env.RedisClusterIAMAuthEnabled {
		redisURLs := config.Env.RedisClusterDatabaseURIs
		redisURLsList := redisURLs.SplitList
		opts = getRedisClusterOptions(redisURLsList)
	} else {
		redisURLsList, err := DiscoverIAMClusterNodes(context.Background())
		if err != nil {
			panic(err)
		}
		opts = getRedisClusterOptions(redisURLsList)
	}
	client := NewUniversalClient(opts)
	if config.Env.DatadogEnabled {
		client.AddHook(&DatadogHook{})
	}
	return client
}
