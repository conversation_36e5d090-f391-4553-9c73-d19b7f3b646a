package redis

import (
	"context"
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"sort"
	"sync"

	"github.com/go-chi/httplog/v2"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
)

type RedisOperation string

const (
	RedisOperationEnqueue   RedisOperation = "enqueue"
	RedisOperationAlerts    RedisOperation = "alerts"
	RedisOperationIngestion RedisOperation = "ingestion"
	RedisOperationDualWrite RedisOperation = "dual_write"
)

// Redis pool for routed Redis Operations
// These depend on feature flag, or tenant_id
type RoutedRedisPools struct {
	SingleRedisPool  *redis.UniversalClient
	ShardManager     *ShardedRedisManager
	ClusterRedisPool *redis.UniversalClient
}

// hold the ketama hash ring and the client map for each shard.
type ShardedRedisManager struct {
	ring    *ketamaHashRing
	clients map[string]redis.UniversalClient
}

var (
	shardedManager *ShardedRedisManager
	shardedOnce    sync.Once
)

// TODO: Can remove singleton?
func NewShardedManager() *ShardedRedisManager {
	shardedOnce.Do(func() {
		nodeURIs := config.Env.RedisShardURIs
		if len(nodeURIs) == 0 {
			return
		}

		var nodeNames []string
		for nodeName := range nodeURIs {
			nodeNames = append(nodeNames, nodeName)
		}
		ring := NewKetamaHashRing(nodeNames)

		clients := make(map[string]redis.UniversalClient)
		for nodeName, uri := range nodeURIs {
			opts := getSingleRedisInstanceOptions(uri)
			clients[nodeName] = redis.NewUniversalClient(opts)
			if config.Env.DatadogEnabled {
				clients[nodeName].AddHook(&DatadogHook{})
			}
		}

		shardedManager = &ShardedRedisManager{
			ring:    ring,
			clients: clients,
		}
	})

	return shardedManager
}

func (h *ShardedRedisManager) GetClients() map[string]redis.UniversalClient {
	return h.clients
}

// Redis pool for routed Redis Operations
// These depend on feature flag, or tenant_id
func (h *RoutedRedisPools) GetRoutedRedisClient(ctx context.Context, authID string, op RedisOperation) redis.UniversalClient {
	oplog := httplog.LogEntry(ctx)

	if config.Env.RedisClusterEnabled && config.Env.RedisClusterAlertsEnabled && op == RedisOperationAlerts {
		return *h.ClusterRedisPool
	}

	if op == RedisOperationIngestion && IsRedisClusterIngestionEnabledForTenant(authID) {
		return *h.ClusterRedisPool
	}

	if config.Env.RedisClusterEnabled && config.Env.RedisClusterDualWriteEnabled && op == RedisOperationDualWrite && !IsRedisClusterIngestionEnabledForTenant(authID) {
		return *h.ClusterRedisPool
	}

	if !config.Env.RedisShardingEnabled {
		oplog.Debug("sharding disabled", "op", op)
		return *h.SingleRedisPool
	}

	NewShardedManager()
	if shardedManager == nil || shardedManager.ring == nil {
		oplog.Warn("sharded redis manager not initialized")
		return *h.SingleRedisPool
	}
	nodeName := getNodeNameWithOverride(authID, (*h.ShardManager).ring)
	if nodeName == "" {
		oplog.Warn("no sharded redis node found for authID", "authID", authID)
		return *h.SingleRedisPool
	}
	client := shardedManager.clients[nodeName]
	if client == nil {
		oplog.Warn("no sharded redis client found for nodeName", "nodeName", nodeName)
		return *h.SingleRedisPool
	}

	return client
}

// Checks if Redis cluster ingestion is enabled for a specific tenant
func IsRedisClusterIngestionEnabledForTenant(tenantID string) bool {
	// Check if Redis cluster is globally enabled
	if !config.Env.RedisClusterEnabled {
		return false
	}

	// Check if Redis cluster is enabled for ingestion globally or for tenant
	return config.Env.RedisClusterIngestionGlobalEnabled || config.Env.RedisClusterIngestionTenantIds.Contains(tenantID)
}

func IsDualWriteEnabledForTenant(tenantID string) bool {
	return (config.Env.RedisClusterDualWriteEnabled && !IsRedisClusterIngestionEnabledForTenant(tenantID)) ||
		(config.Env.RedisShardDualWriteEnabled && !config.Env.RedisShardDualWriteSkipTenantIds.Contains(tenantID))
}

// Checks if Redis sharded writes by trace are enabled for a specific tenant
func IsRedisShardedWritesByTraceEnabledForTenant(tenantID string) bool {
	return config.Env.RedisShardedWritesEnabled && config.Env.RedisShardingByTraceTenantIds.Contains(tenantID)
}

func getNodeNameWithOverride(authID string, ring *ketamaHashRing) string {
	if overrideNodeName, ok := config.Env.RedisShardingOverrideWorkspaces[authID]; ok {
		if _, exists := config.Env.RedisShardURIs[overrideNodeName]; exists {
			return overrideNodeName
		}
	}
	return ring.GetNode(authID)
}

type ketamaHashRing struct {
	nodes        []string
	replicas     int
	circle       map[uint32]string
	sortedHashes []uint32
}

func NewKetamaHashRing(nodes []string) *ketamaHashRing {
	hr := &ketamaHashRing{
		nodes:    nodes,
		replicas: 40, // match default uhashring
		circle:   make(map[uint32]string),
	}
	for _, node := range nodes {
		for i := 0; i < hr.replicas; i++ {
			key := fmt.Sprintf("%s-%d", node, i)
			digest := md5.Sum([]byte(key))
			for j := 0; j < 4; j++ {
				hashVal := binary.LittleEndian.Uint32(digest[j*4 : (j+1)*4])
				hr.circle[hashVal] = node
				hr.sortedHashes = append(hr.sortedHashes, hashVal)
			}
		}
	}
	sort.Slice(hr.sortedHashes, func(i, j int) bool {
		return hr.sortedHashes[i] < hr.sortedHashes[j]
	})
	return hr
}

func (hr *ketamaHashRing) ketamaHash(key string) uint32 {
	hash := md5.Sum([]byte(key))
	return binary.LittleEndian.Uint32(hash[:4])
}

func (hr *ketamaHashRing) GetNode(key string) string {
	if len(hr.circle) == 0 {
		return ""
	}
	hashVal := hr.ketamaHash(key)
	idx := sort.Search(len(hr.sortedHashes), func(i int) bool {
		return hr.sortedHashes[i] >= hashVal
	})
	if idx == len(hr.sortedHashes) {
		idx = 0
	}
	return hr.circle[hr.sortedHashes[idx]]
}
