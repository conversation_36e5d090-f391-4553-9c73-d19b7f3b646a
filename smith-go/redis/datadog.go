package redis

import (
	"context"

	"github.com/redis/go-redis/v9"
	ddtrace "gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// DatadogHook implements redis.Hook.
type DatadogHook struct{}

func (h *DatadogHook) DialHook(next redis.DialHook) redis.DialHook {
	return next
}

func (h *DatadogHook) ProcessHook(next redis.ProcessHook) redis.ProcessHook {
	return func(ctx context.Context, cmd redis.Cmder) error {
		span, ctx := ddtrace.StartSpanFromContext(ctx, "redis.command", ddtrace.ResourceName(cmd.Name()))
		span.SetTag("redis.key", cmd.Args()[1])

		err := next(ctx, cmd)

		if err != nil && err != redis.Nil {
			span.SetTag("error", err)
		}
		span.Finish()
		return err
	}
}

func (h *DatadogHook) ProcessPipelineHook(next redis.ProcessPipelineHook) redis.ProcessPipelineHook {
	return func(ctx context.Context, cmds []redis.Cmder) error {
		span, ctx := ddtrace.StartSpanFromContext(ctx, "redis.pipeline", ddtrace.ResourceName("pipeline"))
		span.SetTag("redis.pipeline.command_count", len(cmds))

		err := next(ctx, cmds)

		for _, cmd := range cmds {
			if errCmd := cmd.Err(); errCmd != nil && errCmd != redis.Nil {
				span.SetTag("error", errCmd)
				break
			}
		}
		span.Finish()
		return err
	}
}
