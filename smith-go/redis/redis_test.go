package redis_test

import (
	"testing"

	lsredis "langchain.com/smith/redis"
)

func TestKetamaHashRing(t *testing.T) {
	// Instantiate the ring with 3 nodes.
	ring := lsredis.NewKetamaHashRing([]string{"node-0", "node-1", "node-2"})

	testCases := []struct {
		key      string
		expected string
	}{
		{"035dc9f3-eea4-43ee-b8c5-98863b570ef5", "node-0"},
		{"00676a88-3110-4a27-a6fe-1d6d680783d6", "node-0"},
		{"ea2779fa-ea7f-492d-b338-de7e742f34aa", "node-0"},
		{"d4a26c2c-fc59-43ea-b901-dc412655480a", "node-0"},
		{"e2010df5-a475-424b-885f-c28c79c36197", "node-0"},

		{"ed54f928-4630-409b-b66a-947e0fbac288", "node-1"},
		{"5c61c688-5b24-4a48-a814-e6692f953fd8", "node-1"},
		{"9734bdba-bbaa-43d2-a39b-0987aeb4348f", "node-1"},
		{"dbd72aed-f47c-464a-8729-429dec14baec", "node-1"},
		{"7b779d79-c7b9-46af-9ca9-cee37d4a91fb", "node-1"},
		{"a064fd1c-afd3-4d06-89e3-6daef4843187", "node-1"},
		{"cd5404cb-a752-42cb-b399-02de44dc1615", "node-1"},

		{"07dc8052-707c-4664-8a5f-5eced8dc82a0", "node-2"},
		{"c8c9f246-c187-46a0-9a8e-24f155e2b00d", "node-2"},
		{"6a1687ca-e85d-4430-9703-2aa457996419", "node-2"},
		{"7301217e-d15a-437c-b370-f0dd5270b3be", "node-2"},
		{"ed858ac1-6463-4f5d-925f-9768e46f18d7", "node-2"},
	}

	for _, tc := range testCases {
		actual := ring.GetNode(tc.key)
		if actual != tc.expected {
			t.Errorf("For key %q, expected node %q but got %q", tc.key, tc.expected, actual)
		}
	}
}
