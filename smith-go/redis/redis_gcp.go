package redis

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"golang.org/x/oauth2/google"
	"langchain.com/smith/config"
)

type cachedToken struct {
	token     string
	expiresAt time.Time
}

var (
	tokenCache cachedToken
	tokenMutex sync.RWMutex
)

// GetCredentials generates a fresh IAM token when called
func GetCredentials(ctx context.Context) (string, error) {
	// Check if we have a cached token that is still valid
	if tokenCache.token != "" && time.Now().Add(time.Duration(config.Env.RedisClusterIAMAuthTokenCacheBuffer)*time.Minute).Before(tokenCache.expiresAt) {
		return tokenCache.token, nil
	}

	tokenMutex.Lock()
	defer tokenMutex.Unlock()

	// Check again in case another goroutine updated the cache
	if tokenCache.token != "" && time.Now().Add(time.Duration(config.Env.RedisClusterIAMAuthTokenCacheBuffer)*time.Minute).Before(tokenCache.expiresAt) {
		return tokenCache.token, nil
	}

	// Fetch a fresh token
	creds, err := google.FindDefaultCredentials(ctx, "https://www.googleapis.com/auth/cloud-platform")
	if err != nil {
		return "", fmt.Errorf("error getting default credentials: %w", err)
	}

	token, err := creds.TokenSource.Token()
	if err != nil {
		return "", fmt.Errorf("error generating token: %w", err)
	}

	tokenCache = cachedToken{
		token:     token.AccessToken,
		expiresAt: token.Expiry,
	}

	return token.AccessToken, nil
}

// DiscoverIAMClusterNodes discovers Redis cluster nodes using IAM authentication
func DiscoverIAMClusterNodes(ctx context.Context) ([]string, error) {
	// Get initial token
	token, err := GetCredentials(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Connect to the discovery node
	cluster := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    []string{fmt.Sprintf("%s:%d", config.Env.RedisClusterDiscoverIP, config.Env.RedisClusterDiscoverPort)},
		Password: token,
	})
	defer cluster.Close()

	// Get cluster nodes information
	cmd := cluster.Do(ctx, "CLUSTER", "NODES")
	if err := cmd.Err(); err != nil {
		return nil, fmt.Errorf("error executing CLUSTER NODES: %w", err)
	}

	// Parse the result to extract node information
	nodesInfo := cmd.String()
	nodeURIs := []string{}

	// Parse each line of the CLUSTER NODES response
	for _, line := range strings.Split(nodesInfo, "\n") {
		if line == "" {
			continue
		}
		parts := strings.Split(line, " ")
		if len(parts) < 2 {
			continue
		}
		addrParts := strings.Split(parts[1], "@")
		if len(addrParts) == 0 {
			continue
		}
		addr := addrParts[0]
		if strings.Contains(addr, "NODES") {
			continue
		}
		nodeURIs = append(nodeURIs, fmt.Sprintf("redis://%s", addr))
	}

	if len(nodeURIs) == 0 {
		return nil, fmt.Errorf("no Redis cluster nodes found")
	}

	return nodeURIs, nil
}
