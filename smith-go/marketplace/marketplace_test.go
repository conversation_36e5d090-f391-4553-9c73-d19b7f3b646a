package marketplace_test

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/gofrs/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/justinas/alice"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/client"
	"github.com/stripe/stripe-go/v80/form"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	"langchain.com/smith/marketplace"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
)

const FREE_TIER_REQUESTS = 10

type setupUserParams struct {
	Email               string
	APIKey              string
	HashedAPIKey        string
	ServiceAPIKey       string
	HashedServiceAPIKey string
	TenantHandle        string
}

type stripeBackendMock struct {
	mock.Mock
}

func (s *stripeBackendMock) Call(method, path, key string, params stripe.ParamsContainer, v stripe.LastResponseSetter) error {
	args := s.Called(method, path, key, params, v)
	return args.Error(0)
}

func (s *stripeBackendMock) CallStreaming(method, path, key string, params stripe.ParamsContainer, v stripe.StreamingLastResponseSetter) error {
	args := s.Called(method, path, key, params, v)
	return args.Error(0)
}

func (s *stripeBackendMock) CallRaw(method, path, key string, body *form.Values, params *stripe.Params, v stripe.LastResponseSetter) error {
	args := s.Called(method, path, key, params, v)
	return args.Error(0)
}

func (s *stripeBackendMock) CallMultipart(method, path, key, boundary string, body *bytes.Buffer, params *stripe.Params, v stripe.LastResponseSetter) error {
	args := s.Called(method, path, key, boundary, body, params, v)
	return args.Error(0)
}

func (s *stripeBackendMock) SetMaxNetworkRetries(maxNetworkRetries int64) {
	s.Called(maxNetworkRetries)
}

func (s *stripeBackendMock) Reset() {
	s.Mock.ExpectedCalls = nil
	s.Mock.Calls = nil
}

type MockHTTPClient struct {
	DoFunc func(req *http.Request) (*http.Response, error)
}

func (m *MockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	return m.DoFunc(req)
}

type MockRoundTripper struct {
	DoFunc func(req *http.Request) (*http.Response, error)
}

func (m *MockRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	return m.DoFunc(req)
}

var ownerUserParams = &setupUserParams{
	Email:               "<EMAIL>",
	APIKey:              "***************************************************",
	HashedAPIKey:        "f59a10ffcf811b368ed6da5c759c802ed95fc5749116065d34925d4f9b2ed6543e5fbdbc8e620287cf0b65cfff41a661969b4c1c0c9f9286bae5084b3d436fda",
	ServiceAPIKey:       "",
	HashedServiceAPIKey: "",
	TenantHandle:        "owner",
}

var callerUserParams = &setupUserParams{
	Email:               "<EMAIL>",
	APIKey:              "***************************************************",
	HashedAPIKey:        "37110cec3212c718a08dd789296a69147af6ff3809b12749a8feb2661d0e20de364ce3a006cbfc2cf8d2cc76ff216b6307e91e99b760fe05c1705a6671318674",
	ServiceAPIKey:       "***************************************************",
	HashedServiceAPIKey: "223ccab5ebc19e3d3c55a2b9fbb5bebf6808893afff285317b88fb5889867efd76049cd4dc04407c037ab3b353a11501304c0042a2ba9b37ded9f193f19a5b65",
	TenantHandle:        "caller",
}

func deleteMarketplaceCreditPurchases(t *testing.T, dbpool *database.AuditLoggedPool, orgID string) {
	_, err := dbpool.Exec(context.Background(), `
			DELETE FROM marketplace_credit_purchases 
			WHERE wallet_id IN (SELECT id FROM wallets WHERE organization_id = $1);
	`, orgID)
	assert.NoError(t, err)
}

func deleteWalletTransactions(t *testing.T, dbpool *database.AuditLoggedPool, orgID string) {
	_, err := dbpool.Exec(context.Background(), `
		DELETE FROM wallet_transactions 
		USING wallets 
		WHERE wallet_transactions.wallet_id = wallets.id 
		AND wallets.organization_id = $1;
	`, orgID)
	assert.NoError(t, err)
}

func deleteWallets(t *testing.T, dbpool *database.AuditLoggedPool, orgID string) {
	_, err := dbpool.Exec(context.Background(), `
		DELETE FROM wallets WHERE organization_id = $1;
	`, orgID)
	assert.NoError(t, err)
}

// func deleteMarketplaceRequestLogs(t *testing.T, dbpool *database.AuditLoggedPool, hostProjectID string) {
// 	_, err := dbpool.Exec(context.Background(), `
// 		DELETE FROM marketplace_request_logs WHERE host_project_id = $1;
// 	`, hostProjectID)
// 	assert.NoError(t, err)
// }

func clearAllMarketplaceRatings(t *testing.T, dbpool *database.AuditLoggedPool) {
	_, err := dbpool.Exec(context.Background(), `
		DELETE FROM marketplace_ratings;
	`)
	assert.NoError(t, err)
}

func deactivateHostProjectPricing(t *testing.T, dbpool *database.AuditLoggedPool, hostProjectID string) {
	_, err := dbpool.Exec(context.Background(), `
		UPDATE host_project_prices 
		SET is_active = false 
		WHERE host_project_id = $1
	`, hostProjectID)
	assert.NoError(t, err)
}

func resetHostProjectPricing(t *testing.T, dbpool *database.AuditLoggedPool, hostProjectID string, price int) {
	_, err := dbpool.Exec(context.Background(), `
		WITH deactivate_existing AS (
				UPDATE host_project_prices 
				SET is_active = false 
				WHERE host_project_id = $1
		)
		INSERT INTO host_project_prices (
				host_project_id,
				price_per_request_micros,
				host_project_url,
				is_active
		) VALUES ($1, $2, $3, $4)
	`, hostProjectID, price, "https://foo.bar", true)
	assert.NoError(t, err)
}

func setupOrgAndHostProject(params *setupUserParams, t *testing.T, dbpool *database.AuditLoggedPool) (string, string) {
	userID, _ := uuid.NewV4()
	userIdStr := userID.String()
	user := testutil.UserSetup(t, dbpool, userIdStr, params.Email, "test", "email", nil, nil)
	orgId := testutil.OrgSetup(t, dbpool, "test org", false, userIdStr)
	_, err := dbpool.Exec(context.Background(), "UPDATE organizations SET stripe_connected_account_id = 'foobar', config = jsonb_set(config, '{can_use_langgraph_cloud}', 'true'::jsonb) WHERE id = $1;", orgId)
	assert.NoError(t, err)
	tenantId := testutil.TenantSetup(t, dbpool, orgId, "test tenant", params.TenantHandle, &auth.TenantConfig{}, true)
	parentIdentityId := testutil.IdentitySetup(t, dbpool, userIdStr, orgId, nil, "ORGANIZATION_ADMIN", "organization", nil, user.LSUserID)
	workspaceIdentityId := testutil.IdentitySetup(t, dbpool, userIdStr, orgId, &tenantId, "WORKSPACE_ADMIN", "workspace", &parentIdentityId, user.LSUserID)
	apiKeyId := testutil.ApiKeySetup(t, dbpool, params.HashedAPIKey, tenantId, workspaceIdentityId, "hm", orgId, userIdStr, user.LSUserID)
	tracerSessionId := testutil.TracerSessionSetup(t, dbpool, tenantId)
	hostProjectId := testutil.HostProjectSetup(t, dbpool, tenantId, apiKeyId, tracerSessionId)
	if params.HashedServiceAPIKey != "" {
		serviceAccountId := testutil.ServiceAccountSetup(t, dbpool, "test service account", orgId)
		orgIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, nil, "ORGANIZATION_ADMIN", "organization", serviceAccountId, nil)
		serviceAccountIdentityId := testutil.ServiceIdentitySetup(t, dbpool, orgId, &tenantId, "WORKSPACE_ADMIN", "workspace", serviceAccountId, &orgIdentityId)
		testutil.ServiceApiKeySetup(t, dbpool, params.HashedServiceAPIKey, tenantId, serviceAccountIdentityId, "hm", serviceAccountId, orgId)
	}
	return orgId, hostProjectId
}

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	DbClear(t, dbpool)
}

func DbClear(t *testing.T, dbpool *database.AuditLoggedPool) {
	_, err := dbpool.Exec(context.Background(), "delete from marketplace_credit_purchases; delete from wallet_transactions; delete from marketplace_request_logs; delete from host_project_prices; delete from host_projects; delete from api_keys; delete from wallets; delete from organizations; delete from users;")
	assert.NoError(t, err)
}

func TestMarketplaceProxyHandler_RunProject(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	DbClear(t, dbpool)
	// ownerOrgId, hostProjectId := setupOrgAndHostProject(ownerUserParams, t, dbpool)
	_, hostProjectId := setupOrgAndHostProject(ownerUserParams, t, dbpool)
	callerOrgId, _ := setupOrgAndHostProject(callerUserParams, t, dbpool)
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewBasicAuth(dbpool, redisPool, 0)
	stripeBackendMock := new(stripeBackendMock)
	stripeTestBackends := &stripe.Backends{
		API:     stripeBackendMock,
		Connect: stripeBackendMock,
		Uploads: stripeBackendMock,
	}
	handler := &marketplace.MarketplaceProxyEndpointHandler{
		Pg:           dbpool,
		Signer:       auth.NewSignerXServiceKeyAuth(),
		StripeClient: client.New("foo", stripeTestBackends),
		HTTPClient: &http.Client{
			Transport: &MockRoundTripper{
				DoFunc: func(req *http.Request) (*http.Response, error) {
					return &http.Response{
						StatusCode: http.StatusOK,
						Body:       io.NopCloser(strings.NewReader("mock response")),
						Header:     make(http.Header),
					}, nil
				},
			},
		},
	}

	tests := []struct {
		name           string
		body           *strings.Reader
		apiKeys        []string
		expectedStatus int
		expectedBody   string
		requestPath    *string
		runCount       *int
		setup          func(t *testing.T, dbpool *database.AuditLoggedPool)
		verify         func(t *testing.T, dbpool *database.AuditLoggedPool)
	}{
		{
			name: "fails if no pricing set",
			body: strings.NewReader(`{
				"assistant_id": "graph",
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{callerUserParams.APIKey},
			expectedStatus: http.StatusNotFound,
			expectedBody:   "{\"message\":\"The requested project is not public.\"}\n",
			setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				_, err := dbpool.Exec(context.Background(), `
					UPDATE host_project_prices
					SET is_active = false
					WHERE host_project_id = $1
				`, hostProjectId)
				assert.NoError(t, err)
			},
		},
		// {
		// 	name: "fails before user creates a wallet",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusPaymentRequired,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// },
		// {
		// 	name: "fails if wallet has insufficient balance",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusPaymentRequired,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			INSERT INTO wallets (
		// 				organization_id,
		// 				credit_balance_micros
		// 			) VALUES ($1, $2)
		// 		`, callerOrgId, 1)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// },
		// {
		// 	name: "fails if there is no matching credit purchase",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusOK,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			INSERT INTO wallets (
		// 				organization_id,
		// 				credit_balance_micros
		// 			) VALUES ($1, $2)
		// 		`, callerOrgId, 10001)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		// Check wallet balance for user
		// 		rows, err := dbpool.Query(context.Background(), `
		// 			SELECT credit_balance_micros, inflight_balance_micros
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, callerOrgId)
		// 		assert.NoError(t, err)
		// 		marketplaceRequestLog, err := pgx.CollectExactlyOneRow(rows, pgx.RowToStructByName[struct {
		// 			CreditBalanceMicros   int64 `json:"credit_balance_micros"`
		// 			InflightBalanceMicros int64 `json:"inflight_balance_micros"`
		// 		}])
		// 		assert.NoError(t, err)
		// 		assert.Equal(t, int64(1), marketplaceRequestLog.CreditBalanceMicros, "Wallet balance should be 1 micro after deducting 10000 from initial 10001")
		// 		assert.Equal(t, int64(10000), marketplaceRequestLog.InflightBalanceMicros, "Wallet inflight balance should be 10000 micros since no credit purchase is found")
		// 	},
		// },
		// {
		// 	name: "succeeds if wallet has sufficient balance",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusOK,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			WITH new_wallet AS (
		// 				INSERT INTO wallets (organization_id, credit_balance_micros)
		// 				VALUES ($1, $2)
		// 				RETURNING id
		// 			)
		// 			INSERT INTO marketplace_credit_purchases (
		// 				wallet_id,
		// 				initial_amount_micros,
		// 				amount_micros_remaining,
		// 				stripe_charge_id
		// 			)
		// 			SELECT
		// 				id,
		// 				$2,
		// 				$2,
		// 				'test_charge_id'
		// 			FROM new_wallet;
		// 		`, callerOrgId, 10001)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		// Check wallet balance for user
		// 		var balance int64
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT credit_balance_micros
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, callerOrgId).Scan(&balance)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, int64(1), balance, "Wallet balance should be 1 micro after deducting 10000 from initial 10001")
		// 	},
		// },
		// {
		// 	name: "should drain balance to zero over many calls",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey: callerUserParams.ApiKey,
		// 	// Final call should 402
		// 	expectedStatus: http.StatusPaymentRequired,
		// 	runCount: func() *int {
		// 		count := 111
		// 		return &count
		// 	}(),
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			WITH new_wallet AS (
		// 				INSERT INTO wallets (organization_id, credit_balance_micros)
		// 				VALUES ($1, $2)
		// 				RETURNING id
		// 			)
		// 			INSERT INTO marketplace_credit_purchases (
		// 				wallet_id,
		// 				initial_amount_micros,
		// 				amount_micros_remaining,
		// 				stripe_charge_id
		// 			)
		// 			SELECT
		// 				id,
		// 				$2,
		// 				$2,
		// 				'test_charge_id'
		// 			FROM new_wallet;
		// 		`, callerOrgId, 1000001)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		// Check wallet balance for user
		// 		var balance int64
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT credit_balance_micros
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, callerOrgId).Scan(&balance)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, int64(1), balance, "Wallet balance should be 1 micro after all purchases")

		// 		var walletTransactions int
		// 		err = dbpool.QueryRow(context.Background(), `
		// 			SELECT COUNT(*)
		// 			FROM wallet_transactions wt
		// 			INNER JOIN wallets w ON w.id = wt.wallet_id
		// 			WHERE w.organization_id = $1
		// 		`, callerOrgId).Scan(&walletTransactions)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, 200, walletTransactions, "200 walletTransactions should exist (a debit and credit per request after free tier)")

		// 		var marketplaceRequestLogs int
		// 		err = dbpool.QueryRow(context.Background(), `
		// 			SELECT COUNT(*)
		// 			FROM marketplace_request_logs mrt
		// 			INNER JOIN identities i ON i.id = mrt.identity_id
		// 			WHERE i.organization_id = $1
		// 		`, callerOrgId).Scan(&marketplaceRequestLogs)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, 110, marketplaceRequestLogs, "110 request logs should exist")
		// 	},
		// },
		// {
		// 	name: "succeeds if wallet has sufficient balance across multiple credit purchases",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusOK,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		// First create the wallet
		// 		var walletID string
		// 		err := dbpool.QueryRow(context.Background(), `
		// 				INSERT INTO wallets (organization_id, credit_balance_micros)
		// 				VALUES ($1, $2)
		// 				RETURNING id
		// 		`, callerOrgId, 100001).Scan(&walletID)
		// 		assert.NoError(t, err)
		// 		// Then create two credit purchases
		// 		_, err = dbpool.Exec(context.Background(), `
		// 				INSERT INTO marketplace_credit_purchases (
		// 						wallet_id,
		// 						initial_amount_micros,
		// 						amount_micros_remaining,
		// 						stripe_charge_id
		// 				) VALUES ($1, $2, $2, $3), ($1, $2, $2, $4)
		// 		`, walletID, 60000, "test_charge_id_1", "test_charge_id_2")
		// 		assert.NoError(t, err)
		// 		_, err = dbpool.Exec(context.Background(), `
		// 				WITH deactivate_existing AS (
		// 					UPDATE host_project_prices
		// 					SET is_active = false
		// 					WHERE host_project_id = $1
		//         )
		//         INSERT INTO host_project_prices (
		// 					host_project_id,
		// 					price_per_request_micros,
		// 					host_project_url,
		// 					is_active
		//         ) VALUES ($1, $2, $3, $4)
		// 		`, hostProjectId, 100000, "https://foo.bar", true)
		// 		assert.NoError(t, err)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		// Check wallet balance for user
		// 		var walletID string
		// 		var balance int64
		// 		var inflightBalance int64
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT id, credit_balance_micros, inflight_balance_micros
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, callerOrgId).Scan(&walletID, &balance, &inflightBalance)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, int64(1), balance, "Wallet balance should be 1 micro after deducting 100000 from initial 100001")
		// 		assert.Equal(t, int64(0), inflightBalance, "Balance should be resolved")
		// 		// Check wallet balance for user
		// 		rows, err := dbpool.Query(context.Background(), `
		// 			SELECT amount_micros_remaining
		// 			FROM marketplace_credit_purchases
		// 			WHERE wallet_id = $1
		// 		`, walletID)
		// 		assert.NoError(t, err)
		// 		creditPurchases, err := pgx.CollectRows(rows, pgx.RowToStructByName[struct {
		// 			AmountMicrosRemaining int64 `json:"amount_micros_remaining"`
		// 		}])
		// 		assert.NoError(t, err)
		// 		assert.Equal(t, 2, len(creditPurchases), "Should have two credit purchases")

		// 		// Sort the remaining amounts to make the assertion order-independent
		// 		remainingAmounts := []int64{creditPurchases[0].AmountMicrosRemaining, creditPurchases[1].AmountMicrosRemaining}
		// 		sort.Slice(remainingAmounts, func(i, j int) bool {
		// 			return remainingAmounts[i] < remainingAmounts[j]
		// 		})

		// 		assert.Equal(t, int64(0), remainingAmounts[0], "First credit purchase should be fully consumed")
		// 		assert.Equal(t, int64(20000), remainingAmounts[1], "Second credit purchase should have 20000 micros remaining")

		// 		// Verify Stripe mock was called twice
		// 		stripeBackendMock.AssertNumberOfCalls(t, "Call", 2)

		// 		// If you want to verify the specific calls:
		// 		stripeBackendMock.AssertCalled(t, "Call",
		// 			"POST",
		// 			"/v1/transfers",
		// 			mock.Anything,
		// 			mock.Anything,
		// 			mock.Anything,
		// 		)
		// 	},
		// },
		// {
		// 	name: "succeeds if call is from the project owner",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         ownerUserParams.ApiKey,
		// 	expectedStatus: http.StatusOK,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			DELETE FROM marketplace_credit_purchases
		// 			WHERE wallet_id IN (SELECT id FROM wallets WHERE organization_id = $1);
		// 		`, ownerOrgId)
		// 		assert.NoError(t, err)
		// 		_, err = dbpool.Exec(context.Background(), `
		// 			DELETE FROM wallets WHERE organization_id = $1;
		// 		`, ownerOrgId)
		// 		assert.NoError(t, err)
		// 		_, err = dbpool.Exec(context.Background(), `
		// 			INSERT INTO wallets (organization_id, credit_balance_micros)
		// 			VALUES ($1, $2)
		// 			RETURNING id
		// 		`, ownerOrgId, 1)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		// Check wallet balance for user
		// 		var balance int64
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT credit_balance_micros
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, ownerOrgId).Scan(&balance)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, int64(1), balance, "Wallet balance should be unchanged if project is called by owner")
		// 	},
		// },
		// {
		// 	name: "succeeds if call is from the project owner even with no wallet",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         ownerUserParams.ApiKey,
		// 	expectedStatus: http.StatusOK,
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			DELETE FROM marketplace_credit_purchases
		// 			WHERE wallet_id IN (SELECT id FROM wallets WHERE organization_id = $1);
		// 		`, ownerOrgId)
		// 		assert.NoError(t, err)
		// 		_, err = dbpool.Exec(context.Background(), `
		// 			DELETE FROM wallets WHERE organization_id = $1;
		// 		`, ownerOrgId)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		// Check that no wallet exists for the owner
		// 		var count int
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT COUNT(*)
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, ownerOrgId).Scan(&count)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, 0, count, "No wallet should exist for the project owner")
		// 	},
		// },
		{
			name: "is free for free methods",
			body: strings.NewReader(`{
				"assistant_id": "graph",
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{ownerUserParams.APIKey},
			expectedStatus: http.StatusOK,
			requestPath: func() *string {
				path := fmt.Sprintf("/marketplace/%s/threads", hostProjectId)
				return &path
			}(),
			setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
				deleteWalletTransactions(t, dbpool, callerOrgId)
				deleteWallets(t, dbpool, callerOrgId)
				_, err := dbpool.Exec(context.Background(), `
					WITH new_wallet AS (
						INSERT INTO wallets (organization_id, credit_balance_micros)
						VALUES ($1, $2)
						RETURNING id
					)
					INSERT INTO marketplace_credit_purchases (
						wallet_id,
						initial_amount_micros,
						amount_micros_remaining,
						stripe_charge_id
					)
					SELECT 
						id,
						$2,
						$2,
						'test_charge_id'
					FROM new_wallet;
				`, callerOrgId, 10001)
				assert.NoError(t, err)
				resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
			},
			verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				// Check wallet balance for user
				var balance int64
				err := dbpool.QueryRow(context.Background(), `
					SELECT credit_balance_micros 
					FROM wallets 
					WHERE organization_id = $1
				`, callerOrgId).Scan(&balance)

				assert.NoError(t, err)
				assert.Equal(t, int64(10001), balance, "Wallet balance should be 10001 micros after a free request")
			},
		},
		{
			name: "should 404 for nonexistent paths",
			body: strings.NewReader(`{
				"assistant_id": "graph",
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{ownerUserParams.APIKey},
			expectedStatus: http.StatusNotFound,
			requestPath: func() *string {
				path := fmt.Sprintf("/marketplace/%s/foobar", hostProjectId)
				return &path
			}(),
		},
		// {
		// 	name: "should fail if a thread already exists created by a different user",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusForbidden,
		// 	expectedBody:   "{\"message\":\"You do not have access to that thread.\"}\n",
		// 	requestPath: func() *string {
		// 		path := fmt.Sprintf("/marketplace/%s/threads/%s/runs", hostProjectId, "00000000-0000-0000-0000-000000000000")
		// 		return &path
		// 	}(),
		// 	runCount: func() *int {
		// 		count := 1
		// 		return &count
		// 	}(),
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			WITH new_wallet AS (
		// 				INSERT INTO wallets (organization_id, credit_balance_micros)
		// 				VALUES ($1, $2)
		// 				RETURNING id
		// 			)
		// 			INSERT INTO marketplace_credit_purchases (
		// 				wallet_id,
		// 				initial_amount_micros,
		// 				amount_micros_remaining,
		// 				stripe_charge_id
		// 			)
		// 			SELECT
		// 				id,
		// 				$2,
		// 				$2,
		// 				'test_charge_id'
		// 			FROM new_wallet;
		// 		`, callerOrgId, 10001)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 		_, err = dbpool.Exec(context.Background(), `
		// 		  WITH identity AS (
		// 				SELECT id
		// 				FROM identities
		// 				WHERE organization_id = $1
		// 			)
		// 			INSERT INTO marketplace_request_logs (
		// 				project_organization_id,
		// 				price_micros,
		// 				id,
		// 				identity_id,
		// 				host_project_id,
		// 				currency,
		// 				thread_id,
		// 				request_count
		// 			)
		// 			SELECT
		// 			  $1,
		// 				$2,
		// 				gen_random_uuid(),
		// 				identity.id,
		// 				$3,
		// 				'usd',
		// 				'00000000-0000-0000-0000-000000000000',
		// 				1
		// 			FROM identity;
		// 		`, ownerOrgId, 10001, hostProjectId)
		// 		assert.NoError(t, err)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		var transactions int
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT COUNT(*)
		// 			FROM wallet_transactions wt
		// 			INNER JOIN wallets w ON w.id = wt.wallet_id
		// 			WHERE w.organization_id = $1
		// 		`, callerOrgId).Scan(&transactions)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, 0, transactions, "No transactions should exist")
		// 	},
		// },
		// {
		// 	name: "should succeed if a thread already exists created by the same user",
		// 	body: strings.NewReader(`{
		// 		"assistant_id": "graph",
		// 		"input": {
		// 			"test": "data"
		// 		}
		// 	}`),
		// 	apiKey:         callerUserParams.ApiKey,
		// 	expectedStatus: http.StatusOK,
		// 	requestPath: func() *string {
		// 		path := fmt.Sprintf("/marketplace/%s/threads/%s/runs", hostProjectId, "00000000-0000-0000-0000-000000000000")
		// 		return &path
		// 	}(),
		// 	runCount: func() *int {
		// 		count := 12
		// 		return &count
		// 	}(),
		// 	setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
		// 		deleteWalletTransactions(t, dbpool, callerOrgId)
		// 		deleteWallets(t, dbpool, callerOrgId)
		// 		deleteMarketplaceRequestLogs(t, dbpool, hostProjectId)
		// 		_, err := dbpool.Exec(context.Background(), `
		// 			WITH new_wallet AS (
		// 				INSERT INTO wallets (organization_id, credit_balance_micros)
		// 				VALUES ($1, $2)
		// 				RETURNING id
		// 			)
		// 			INSERT INTO marketplace_credit_purchases (
		// 				wallet_id,
		// 				initial_amount_micros,
		// 				amount_micros_remaining,
		// 				stripe_charge_id
		// 			)
		// 			SELECT
		// 				id,
		// 				$2,
		// 				$2,
		// 				'test_charge_id'
		// 			FROM new_wallet;
		// 		`, callerOrgId, 20001)
		// 		assert.NoError(t, err)
		// 		resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
		// 	},
		// 	verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
		// 		var transactions int
		// 		err := dbpool.QueryRow(context.Background(), `
		// 			SELECT COUNT(*)
		// 			FROM wallet_transactions wt
		// 			INNER JOIN wallets w ON w.id = wt.wallet_id
		// 			WHERE w.organization_id = $1
		// 		`, callerOrgId).Scan(&transactions)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, 4, transactions, "Four transactions should exist (a debit and credit per request after free tier)")

		// 		// Check wallet balance for user
		// 		var balance int64
		// 		err = dbpool.QueryRow(context.Background(), `
		// 			SELECT credit_balance_micros
		// 			FROM wallets
		// 			WHERE organization_id = $1
		// 		`, callerOrgId).Scan(&balance)

		// 		assert.NoError(t, err)
		// 		assert.Equal(t, int64(1), balance, "Wallet balance should be 1 micro after two requests")
		// 	},
		// },
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, apiKey := range tt.apiKeys {
				stripeBackendMock.Reset()
				stripeBackendMock.On("Call", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					stripeTransfer := args.Get(4).(*stripe.Transfer)
					*stripeTransfer = stripe.Transfer{
						ID:     "tr_123",
						Amount: 10000,
					}
				}).Return(nil)
				if tt.setup != nil {
					tt.setup(t, dbpool)
				}
				var path string
				if tt.requestPath == nil {
					path = fmt.Sprintf("/marketplace/%s/runs/stream", hostProjectId)
				} else {
					path = *tt.requestPath
				}

				var resp *http.Response
				var body []byte
				runCount := tt.runCount
				if runCount == nil {
					defaultRunCount := FREE_TIER_REQUESTS + 1
					runCount = &defaultRunCount
				}
				for i := 0; i < *runCount; i++ {
					req := httptest.NewRequest(http.MethodPost, path, tt.body)
					req.Header.Set("Content-Type", "application/json")
					req.Header.Set("X-Api-Key", apiKey)

					w := httptest.NewRecorder()
					handlerWithAuth := alice.New(testutil.TestLogger(t), ah.OrgMiddleware).ThenFunc(handler.ProxyRequest)
					handlerWithAuth.ServeHTTP(w, req)

					resp = w.Result()
					body, _ = io.ReadAll(resp.Body)
				}

				assert.Equal(t, tt.expectedStatus, resp.StatusCode, "Proxy request returned wrong status code")
				if tt.expectedBody != "" {
					assert.Equal(t, string(body), tt.expectedBody, "Body should match expected value")
				}
				// Add sleep to ensure all DB operations complete
				time.Sleep(100 * time.Millisecond)
				if tt.verify != nil {
					tt.verify(t, dbpool)
				}
			}
		})
	}
}

func TestCreateMarketplaceRatingsHandler_Run(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	dbpool := database.PgConnect()
	DbClear(t, dbpool)
	_, hostProjectId := setupOrgAndHostProject(ownerUserParams, t, dbpool)
	callerOrgId, _ := setupOrgAndHostProject(callerUserParams, t, dbpool)
	defer DbCleanup(t, dbpool)

	redisPool := lsredis.SingleRedisConnect()
	defer func(redisPool redis.UniversalClient) {
		err := redisPool.Close()
		if err != nil {
			t.Error(err)
		}
	}(redisPool)

	ah := auth.NewBasicAuth(dbpool, redisPool, 0)

	handler := &marketplace.RegistryEndpointHandler{
		Pg: dbpool,
	}

	tests := []struct {
		name           string
		body           *strings.Reader
		apiKeys        []string
		expectedStatus int
		expectedBody   string
		setup          func(t *testing.T, dbpool *database.AuditLoggedPool)
		verify         func(t *testing.T, dbpool *database.AuditLoggedPool)
	}{
		{
			name: "should fail for projects that are not public",
			body: strings.NewReader(`{
				"rating": 80,
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{callerUserParams.APIKey},
			expectedStatus: http.StatusNotFound,
			setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				deactivateHostProjectPricing(t, dbpool, hostProjectId)
			},
		},
		{
			name: "should fail for users who have not purchased credits",
			body: strings.NewReader(`{
				"rating": 80,
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{callerUserParams.APIKey},
			expectedStatus: http.StatusPaymentRequired,
			setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
				deleteWalletTransactions(t, dbpool, callerOrgId)
				deleteWallets(t, dbpool, callerOrgId)
				deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
			},
		},
		{
			name: "should succeed for users who have purchased credits",
			body: strings.NewReader(`{
				"rating": 80,
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{callerUserParams.APIKey},
			expectedStatus: http.StatusCreated,
			setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
				deleteWalletTransactions(t, dbpool, callerOrgId)
				deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
				deleteWallets(t, dbpool, callerOrgId)
				clearAllMarketplaceRatings(t, dbpool)
				_, err := dbpool.Exec(context.Background(), `
					WITH new_wallet AS (
						INSERT INTO wallets (organization_id, credit_balance_micros)
						VALUES ($1, $2)
						RETURNING id
					)
					INSERT INTO marketplace_credit_purchases (
						wallet_id,
						initial_amount_micros,
						amount_micros_remaining,
						stripe_charge_id
					)
					SELECT 
						id,
						$2,
						$2,
						'test_charge_id'
					FROM new_wallet;
				`, callerOrgId, 10001)
				assert.NoError(t, err)
			},
		},
		{
			name: "should upsert if a rating already exists",
			body: strings.NewReader(`{
				"rating": 60,
				"input": {
					"test": "data"
				}
			}`),
			apiKeys:        []string{callerUserParams.APIKey},
			expectedStatus: http.StatusCreated,
			setup: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				resetHostProjectPricing(t, dbpool, hostProjectId, 10000)
				deleteWalletTransactions(t, dbpool, callerOrgId)
				deleteMarketplaceCreditPurchases(t, dbpool, callerOrgId)
				deleteWallets(t, dbpool, callerOrgId)
				clearAllMarketplaceRatings(t, dbpool)
				_, err := dbpool.Exec(context.Background(), `
					WITH new_wallet AS (
						INSERT INTO wallets (organization_id, credit_balance_micros)
						VALUES ($1, $2)
						RETURNING id
					)
					INSERT INTO marketplace_credit_purchases (
						wallet_id,
						initial_amount_micros,
						amount_micros_remaining,
						stripe_charge_id
					)
					SELECT 
						id,
						$2,
						$2,
						'test_charge_id'
					FROM new_wallet;
				`, callerOrgId, 10001)
				assert.NoError(t, err)
				_, err = dbpool.Exec(context.Background(), `
					INSERT INTO marketplace_ratings (
						ls_user_id,
						host_project_id,
						rating,
						created_at
					)
					SELECT 
						u.ls_user_id,
						$2,
						60,
						NOW()
					FROM organizations o
					INNER JOIN users u ON u.id = o.created_by_user_id
					WHERE o.id = $1;
				`, callerOrgId, hostProjectId)
				assert.NoError(t, err)
			},
			verify: func(t *testing.T, dbpool *database.AuditLoggedPool) {
				rows, err := dbpool.Query(context.Background(), `
					SELECT rating
					FROM marketplace_ratings
					WHERE host_project_id = $1
				`, hostProjectId)
				assert.NoError(t, err)
				rating, err := pgx.CollectExactlyOneRow(rows, pgx.RowToStructByName[struct {
					Rating int `json:"rating"`
				}])
				assert.NoError(t, err)
				assert.Equal(t, 60, rating.Rating, "Rating should have upserted")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, apiKey := range tt.apiKeys {
				if tt.setup != nil {
					tt.setup(t, dbpool)
				}
				path := fmt.Sprintf("/registry/%s/ratings", hostProjectId)

				var resp *http.Response
				var body []byte

				req := httptest.NewRequest(http.MethodPost, path, tt.body)
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("X-Api-Key", apiKey)

				// Add Chi route context with URL parameters
				rctx := chi.NewRouteContext()
				rctx.URLParams.Add("projectID", hostProjectId)
				req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

				w := httptest.NewRecorder()
				handlerWithAuth := alice.New(testutil.TestLogger(t), ah.Middleware).ThenFunc(handler.CreateMarketplaceRatings)
				handlerWithAuth.ServeHTTP(w, req)

				resp = w.Result()
				body, _ = io.ReadAll(resp.Body)

				assert.Equal(t, tt.expectedStatus, resp.StatusCode, "Proxy request returned wrong status code")
				if tt.expectedBody != "" {
					assert.Equal(t, string(body), tt.expectedBody, "Body should match expected value")
				}
				if tt.verify != nil {
					tt.verify(t, dbpool)
				}
			}
		})
	}
}
