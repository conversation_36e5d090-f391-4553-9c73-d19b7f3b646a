package marketplace

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"langchain.com/smith/database"

	"langchain.com/smith/auth"
)

type Dataset struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	ShareToken  string `json:"share_token"`
}

const getPublicProjectsQuery = `
WITH base_projects AS (
  SELECT
	  hp.id,
		hp.tool_name,
		hp.display_name,
		hp.description,
		o.display_name as organization_display_name,
		hpp.price_per_request_micros,
		hp.readme_markdown,
		hp.input_json_schemas,
		hp.output_json_schemas,
		hp.example_input,
		hp.created_at,
		hp.updated_at,
		COALESCE(json_agg(
			json_build_object(
				'id', d.id,
				'name', d.name,
				'description', d.description,
				'share_token', dsk.share_token
			)
		) FILTER (WHERE d.id IS NOT NULL AND dsk.id IS NOT NULL), '[]') as datasets,
		COALESCE(AVG(mr.rating)::float, 0.0) as average_rating,
		COUNT(mr.id) as ratings_count,
		(SELECT rating FROM marketplace_ratings WHERE ls_user_id = $4 LIMIT 1) AS current_user_rating
  FROM host_projects hp
	INNER JOIN tenants t ON t.id = hp.tenant_id
	INNER JOIN organizations o ON o.id = t.organization_id
  INNER JOIN host_project_prices hpp ON hp.id = hpp.host_project_id
  LEFT JOIN host_project_dataset_links hpdl ON hp.id = hpdl.host_project_id
  LEFT JOIN dataset d ON hpdl.dataset_id = d.id
  LEFT JOIN datasets_share_keys dsk ON d.id = dsk.dataset_id
	LEFT JOIN marketplace_ratings mr ON mr.host_project_id = hp.id
  WHERE hpp.is_active = true 
    AND ($2::timestamptz IS NULL OR hp.created_at < $2)
    AND ($1::uuid IS NULL OR hp.id = $1)
  GROUP BY
		hp.id,
		o.display_name,
		hpp.price_per_request_micros,
		hp.readme_markdown,
		hp.created_at,
		hp.updated_at
  ORDER BY hp.created_at DESC
  LIMIT $3
)
SELECT * FROM base_projects;`

type Project struct {
	ID                      string           `json:"id"`
	ToolName                *string          `json:"tool_name"`
	DisplayName             *string          `json:"display_name"`
	Description             *string          `json:"description"`
	OrganizationDisplayName *string          `json:"organization_display_name"`
	PricePerRequestMicros   int64            `json:"price_per_request_micros"`
	ReadmeMarkdown          *string          `json:"readme_markdown"`
	InputJSONSchemas        json.RawMessage  `json:"input_json_schemas"`
	OutputJSONSchemas       json.RawMessage  `json:"output_json_schemas"`
	Datasets                []Dataset        `json:"datasets"`
	AverageRating           float64          `json:"average_rating"`
	RatingsCount            int64            `json:"ratings_count"`
	CurrentUserRating       *int64           `json:"current_user_rating"`
	ExampleInput            *json.RawMessage `json:"example_input"`
	CreatedAt               time.Time        `json:"created_at"`
	UpdatedAt               time.Time        `json:"updated_at"`
}

type ProjectsResponse struct {
	Data   []Project `json:"data"`
	Cursor *string   `json:"cursor"`
}

type RegistryEndpointHandler struct {
	Pg *database.AuditLoggedPool
}

func NewRegistryEndpointHandler(dbpool *database.AuditLoggedPool) *RegistryEndpointHandler {
	return &RegistryEndpointHandler{
		Pg: dbpool,
	}
}

func (h *RegistryEndpointHandler) Search(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())
	var projectID *string
	if id := r.URL.Query().Get("id"); id != "" {
		projectID = &id
	}

	if projectID == nil {
		id := chi.URLParam(r, "projectID")
		if id != "" {
			projectID = &id
		}
	}

	var currentLsUserID *string
	auth, ok := auth.MaybeGetAuthInfo(r)
	if ok {
		lsID := auth.LSUserID
		currentLsUserID = &lsID
	}

	var cursorTime *time.Time
	if cursor := r.URL.Query().Get("cursor"); cursor != "" {
		// Parse cursor as Unix timestamp
		unixTimestamp, err := strconv.ParseInt(cursor, 10, 64)
		if err != nil {
			render.Status(r, http.StatusBadRequest)
			render.JSON(w, r, Response{Message: "Invalid cursor format"})
			return
		}
		parsedTime := time.Unix(unixTimestamp, 0)
		cursorTime = &parsedTime
	}
	limit := 20 // default limit
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err != nil || parsedLimit <= 0 {
			render.Status(r, http.StatusBadRequest)
			render.JSON(w, r, Response{Message: "Invalid limit parameter"})
			return
		}
		limit = parsedLimit
	}
	rows, err := h.Pg.Query(r.Context(), getPublicProjectsQuery, projectID, cursorTime, limit, currentLsUserID)
	if err != nil {
		oplog.Error("Error fetching projects", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to query projects."})
		return
	}
	defer rows.Close()

	var projects []Project

	for rows.Next() {
		var p Project
		var datasetsJSON []byte
		err := rows.Scan(
			&p.ID,
			&p.ToolName,
			&p.DisplayName,
			&p.Description,
			&p.OrganizationDisplayName,
			&p.PricePerRequestMicros,
			&p.ReadmeMarkdown,
			&p.InputJSONSchemas,
			&p.OutputJSONSchemas,
			&p.ExampleInput,
			&p.CreatedAt,
			&p.UpdatedAt,
			&datasetsJSON,
			&p.AverageRating,
			&p.RatingsCount,
			&p.CurrentUserRating,
		)
		if err != nil {
			oplog.Error("Error scanning row", "err", err)
			render.Status(r, http.StatusInternalServerError)
			render.JSON(w, r, Response{Message: "Failed to retrieve projects."})
			return
		}

		// Parse the datasets JSON
		if err := json.Unmarshal(datasetsJSON, &p.Datasets); err != nil {
			oplog.Error("Error unmarshaling datasets", "err", err)
			render.Status(r, http.StatusInternalServerError)
			render.JSON(w, r, Response{Message: "Failed to parse project datasets."})
			return
		}

		projects = append(projects, p)
	}

	var nextCursor *string
	if len(projects) == limit {
		// Convert to Unix timestamp string
		cursorStr := strconv.FormatInt(projects[len(projects)-1].CreatedAt.Unix(), 10)
		nextCursor = &cursorStr
	}

	render.Status(r, http.StatusOK)
	render.JSON(w, r, ProjectsResponse{Data: projects, Cursor: nextCursor})
}

type CreateMarketplaceRatingRequest struct {
	Rating int `json:"rating"`
}

type RatingData struct {
	ID string `json:"id"`
}

type CreateMarketplaceRatingsResponse struct {
	Data []RatingData `json:"data"`
}

func (h *RegistryEndpointHandler) CreateMarketplaceRatings(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())
	projectID := chi.URLParam(r, "projectID")
	if projectID == "" {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{Message: "Project ID is required"})
		return
	}

	const projectIsPublicQuery = `
		SELECT EXISTS (
			SELECT 1
			FROM host_project_prices hpp
			WHERE hpp.is_active = true AND hpp.host_project_id = $1
		);`

	var isPublic bool
	if err := h.Pg.QueryRow(
		r.Context(),
		projectIsPublicQuery,
		projectID,
	).Scan(&isPublic); err != nil {
		oplog.Error("Error checking project visibility", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to check project status"})
		return
	}

	if !isPublic {
		render.Status(r, http.StatusNotFound)
		render.JSON(w, r, Response{Message: "Project not found or is not public."})
		return
	}

	var req CreateMarketplaceRatingRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{Message: "Invalid request body"})
		return
	}

	auth := auth.GetAuthInfo(r)
	lsUserID := auth.LSUserID
	tenantID := auth.TenantID

	if auth.TenantConfig.OrganizationConfig.CanUseLanggraphCloud == nil ||
		!*auth.TenantConfig.OrganizationConfig.CanUseLanggraphCloud {
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, Response{Message: "Must be on a paid plan to leave ratings"})
		return
	}

	// Make sure the tenant has purchased credits or is on a paid plan
	const purchaseQuery = `
		SELECT EXISTS (
			SELECT 1 FROM tenants t
			INNER JOIN organizations o ON o.id = t.organization_id
			LEFT JOIN wallets w ON w.organization_id = o.id
			LEFT JOIN marketplace_credit_purchases mcp ON mcp.wallet_id = w.id
			WHERE t.id = $1 AND (
				mcp.id IS NOT NULL
			)
		)`

	var hasPurchased bool
	if err := h.Pg.QueryRow(
		r.Context(),
		purchaseQuery,
		tenantID,
	).Scan(&hasPurchased); err != nil {
		oplog.Error("Error checking purchase status", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to add ratings"})
		return
	}

	if !hasPurchased {
		render.Status(r, http.StatusPaymentRequired)
		render.JSON(w, r, Response{Message: "Must have purchased marketplace credits or be on a paid plan to leave ratings"})
		return
	}

	if req.Rating < 0 || req.Rating > 100 {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, Response{Message: "Provided rating must be between 0 and 100"})
		return
	}

	// Insert or update the rating in the database
	const query = `
		INSERT INTO marketplace_ratings (
			host_project_id,
			ls_user_id,
			rating,
			created_at
		) VALUES ($1, $2, $3, NOW())
		ON CONFLICT (host_project_id, ls_user_id)
		DO UPDATE SET
			rating = EXCLUDED.rating,
			updated_at = NOW()
		RETURNING id`

	var ratingID string
	err := h.Pg.QueryRow(
		r.Context(),
		query,
		projectID,
		lsUserID,
		req.Rating,
	).Scan(&ratingID)

	if err != nil {
		oplog.Error("Error creating rating", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to create rating"})
		return
	}

	render.Status(r, http.StatusCreated)
	render.JSON(w, r, CreateMarketplaceRatingsResponse{
		Data: []RatingData{{ID: ratingID}},
	})
}
