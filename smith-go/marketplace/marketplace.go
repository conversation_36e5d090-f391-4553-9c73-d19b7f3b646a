package marketplace

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/client"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
)

const getHostProjectPricesQuery = `
SELECT hpp.host_project_id, hpp.host_project_url, hpp.price_per_request_micros, hp.tenant_id as owner_tenant_id
FROM host_project_prices hpp
INNER JOIN host_projects hp ON hp.id = hpp.host_project_id
WHERE hpp.host_project_id = $1 AND is_active = true;`

type ProjectPrice struct {
	HostProjectID         string `json:"host_project_id"`
	HostProjectURL        string `json:"host_project_url"`
	PricePerRequestMicros int64  `json:"price_per_request_micros"`
	OwnerTenantID         string `json:"owner_tenant_id"`
}

type Response struct {
	Message string `json:"message"`
}

type RequestData struct {
	HostProjectID   string
	Headers         http.Header
	Method          string
	Path            string
	NormalizedPath  string
	AuthInfo        auth.OrgAuthInfo
	LangsmithApiKey string
	IsFreeEndpoint  bool
	IsPaidEndpoint  bool
	ThreadID        *string
}

type CreateMarketplaceRequestLogParams struct {
	ID               string
	IdentityID       string
	HostProjectID    string
	AssistantID      string
	Method           string
	Path             string
	RequestStartedAt time.Time
	RequestEndedAt   time.Time
	IPAddress        string
	UserAgent        string
	LSUserID         *string
	OrganizationID   string
	ThreadID         *string
}

type MarketplaceRequestLog struct {
	ID               string    `json:"id"`
	IdentityID       string    `json:"identity_id"`
	HostProjectID    string    `json:"host_project_id"`
	AssistantID      string    `json:"assistant_id"`
	Method           string    `json:"method"`
	Path             string    `json:"path"`
	RequestStartedAt time.Time `json:"request_started_at"`
	RequestEndedAt   time.Time `json:"request_ended_at"`
	IPAddress        string    `json:"ip_address"`
	UserAgent        string    `json:"user_agent"`
	PriceMicros      int64     `json:"price_micros"`
}

type CreditPurchase struct {
	ID                    string `json:"id"`
	StripeChargeID        string `json:"stripe_charge_id"`
	AmountMicrosRemaining int64  `json:"amount_micros_remaining"`
}

// Allowlist of methods
var freeEndpointPatterns = map[string][][]string{
	"GET": {
		{"threads", "*", "state"},
		{"threads", "*", "history"},
		{"threads", "*"},
		{"threads", "*", "runs"},
		{"threads", "*", "runs", "*"},
		{"threads", "*", "runs", "*", "join"},
		{"threads", "*", "runs", "*", "stream"},
	},
	"POST": {
		{"threads"},
		{"threads", "*", "state"},
		{"threads", "*", "state", "checkpoint"},
		{"threads", "*", "history"},
		{"threads", "*", "copy"},
		{"threads", "*", "runs", "*", "cancel"},
	},
	"PATCH": {
		{"threads", "*"},
	},
	"DELETE": {
		{"threads", "*"},
		{"threads", "*", "runs", "*"},
	},
}

var pricedEndpointPatterns = map[string][][]string{
	"GET": {},
	"POST": {
		{"threads", "*", "runs"},
		{"threads", "*", "runs", "stream"},
		{"threads", "*", "runs", "wait"},
		{"runs", "stream"},
		{"runs", "wait"},
		{"runs"},
		// {"runs", "batch"}
	},
	"PATCH":  {},
	"DELETE": {},
}

type HttpError struct {
	Code    int    // HTTP status code
	Message string // Error message for the client
}

func (e *HttpError) Error() string {
	return e.Message
}

type MarketplaceProxyEndpointHandler struct {
	Pg           *database.AuditLoggedPool
	Signer       *auth.SignerXServiceKeyAuth
	HTTPClient   *http.Client
	StripeClient *client.API
}

func NewMarketplaceProxyEndpointHandler(dbpool *database.AuditLoggedPool) *MarketplaceProxyEndpointHandler {
	sc := &client.API{}
	sc.Init(config.Env.StripeSecretKey, nil)
	return &MarketplaceProxyEndpointHandler{
		Pg:     dbpool,
		Signer: auth.NewSignerXServiceKeyAuth(),
		// TODO: Confirm this respects keep alive and long-running requests
		HTTPClient:   &http.Client{},
		StripeClient: sc,
	}
}

func (h *MarketplaceProxyEndpointHandler) createMarketplaceRequestLog(
	ctx context.Context,
	requestLog *CreateMarketplaceRequestLogParams,
) (hostProjectURL string, pricePerRequestMicros int, ownerTenantID string, isOwner bool, err error) {
	// Start transaction
	tx, err := h.Pg.Begin(ctx)
	if err != nil {
		return "", 0, "", false, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx) // Rollback if not committed

	var threadExists bool
	err = tx.QueryRow(ctx, `
		WITH price_info AS (
				SELECT hpp.host_project_url,
							hpp.price_per_request_micros,
							hp.tenant_id as owner_tenant_id,
							t.organization_id,
							t.organization_id = (SELECT organization_id FROM identities WHERE id = $3) as is_owner
				FROM host_project_prices hpp
				INNER JOIN host_projects hp ON hp.id = hpp.host_project_id
				INNER JOIN tenants t ON t.id = hp.tenant_id
				WHERE hpp.host_project_id = $1 AND hpp.is_active = true
				AND hpp.price_per_request_micros > 0
		),
		thread_access_check AS (
				SELECT COUNT(mrl.id) > 0 AS thread_exists
				FROM marketplace_request_logs mrl
				INNER JOIN identities i ON i.id = mrl.identity_id
				WHERE mrl.thread_id = $11
				AND mrl.host_project_id = $4
				AND (
					CASE 
						WHEN $13::uuid IS NULL THEN i.organization_id != $14
						ELSE i.ls_user_id != $13::uuid
					END
				)
				AND mrl.thread_id IS NOT NULL
		),
		inserted_log AS (
				INSERT INTO marketplace_request_logs (
						project_organization_id,
						price_micros,
						id,
						identity_id,
						host_project_id,
						assistant_id,
						currency,
						method,
						path,
						ip_address,
						user_agent,
						thread_id,
						request_count
				)
				SELECT
						pi.organization_id,
						CASE WHEN pi.is_owner THEN 0
						WHEN (
								SELECT COALESCE(SUM(request_count), 0)
								FROM marketplace_request_logs m
								INNER JOIN identities i ON i.id = m.identity_id
								WHERE i.organization_id = $14 AND m.host_project_id = $4
						) < 10 THEN 0
						ELSE pi.price_per_request_micros END,
						$2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
				FROM price_info pi
				WHERE $11 IS NULL OR (SELECT thread_exists FROM thread_access_check) IS NOT TRUE
				RETURNING id, price_micros
		),
		wallet_tx AS (
			INSERT INTO wallet_transactions (
				wallet_id,
				credit_balance_delta_micros,
				inflight_balance_delta_micros,
				marketplace_request_log_id,
				type
			)
			SELECT 
				(SELECT id FROM wallets WHERE organization_id = (SELECT organization_id FROM identities WHERE id = $3)),
				-il.price_micros,
				il.price_micros,
				il.id,
				'inflight_request_hold'
			FROM inserted_log il
			CROSS JOIN price_info pi
			WHERE NOT pi.is_owner AND il.price_micros > 0
		)
		SELECT 
			pi.host_project_url,
			pi.price_per_request_micros,
			pi.owner_tenant_id,
			pi.is_owner,
			tac.thread_exists
		FROM price_info pi
		CROSS JOIN thread_access_check tac
	`, requestLog.HostProjectID, requestLog.ID, requestLog.IdentityID, requestLog.HostProjectID,
		requestLog.AssistantID, "usd", requestLog.Method,
		requestLog.Path, requestLog.IPAddress, requestLog.UserAgent, requestLog.ThreadID, 1, requestLog.LSUserID, requestLog.OrganizationID).Scan(&hostProjectURL, &pricePerRequestMicros, &ownerTenantID, &isOwner, &threadExists)
	if err != nil {
		if err == pgx.ErrNoRows {
			return "", 0, "", false, &HttpError{
				Code:    http.StatusNotFound,
				Message: "The requested project is not public.",
			}
		}
		return "", 0, "", false, fmt.Errorf("failed to insert marketplace request log: %w", err)
	}

	if threadExists {
		return "", 0, "", false, &HttpError{Code: http.StatusForbidden, Message: "You do not have access to that thread."}
	}

	if hostProjectURL == "" || ownerTenantID == "" {
		return "", 0, "", false, &HttpError{Code: http.StatusForbidden, Message: "This graph is not public."}
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		return "", 0, "", false, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return hostProjectURL, pricePerRequestMicros, ownerTenantID, isOwner, nil
}

func (h *MarketplaceProxyEndpointHandler) completeMarketplaceRequestLog(ctx context.Context, requestLogID string, status string, httpStatusCode int) (*MarketplaceRequestLog, error) {
	tx, err := h.Pg.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	var updatedLog MarketplaceRequestLog
	var projectOrgID, stripeConnectedAccountID string
	var currency string
	var walletID, transactionID *string

	err = tx.QueryRow(ctx, `
		WITH updated_log AS (
			UPDATE marketplace_request_logs 
			SET status = $1, http_status_code = $2, updated_at = NOW()
			WHERE id = $3 AND status != 'complete'
			RETURNING id, identity_id, host_project_id, price_micros, project_organization_id, currency
		),
		wallet_info AS (
			SELECT w.id as wallet_id
			FROM identities i
			LEFT JOIN wallets w ON w.organization_id = i.organization_id
			WHERE i.id = (SELECT identity_id FROM updated_log)
		),
		wallet_tx AS (
			INSERT INTO wallet_transactions (
				wallet_id,
				inflight_balance_delta_micros,
				marketplace_request_log_id,
				type
			)
			SELECT 
				(SELECT wallet_id FROM wallet_info),
				-ul.price_micros,
				ul.id,
				'completed_request_debit'
			FROM updated_log ul
			WHERE ul.price_micros > 0 AND EXISTS (SELECT 1 FROM wallet_info WHERE wallet_id IS NOT NULL)
			RETURNING id
		)
    SELECT 
        ul.id,
				ul.identity_id,
				ul.host_project_id,
				ul.price_micros,
        ul.project_organization_id,
				ul.currency,
        wi.wallet_id as wallet_id,
        wt.id as transaction_id,
        COALESCE(o.stripe_connected_account_id, '') as stripe_connected_account_id
    FROM updated_log ul
    LEFT JOIN wallet_info wi ON true
    LEFT JOIN wallet_tx wt ON true
    LEFT JOIN organizations o ON o.id = ul.project_organization_id
	`, status, httpStatusCode, requestLogID).Scan(
		&updatedLog.ID, &updatedLog.IdentityID, &updatedLog.HostProjectID,
		&updatedLog.PriceMicros, &projectOrgID, &currency,
		&walletID, &transactionID, &stripeConnectedAccountID)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("could not match a completed request to a pending transaction")
		}
		return nil, fmt.Errorf("failed to update request log: %w", err)
	}

	if updatedLog.PriceMicros > 0 {
		if stripeConnectedAccountID == "" {
			return nil, fmt.Errorf("the project owner does not have a valid Stripe connection. cancelling transaction")
		}

		if walletID == nil {
			return nil, fmt.Errorf("the project owner does not have a wallet. cancelling transaction")
		}

		// Spread the cost over at most 5 credit purchases
		// Should be relatively rare anyway
		rows, err := tx.Query(ctx, `
			SELECT id, stripe_charge_id, amount_micros_remaining
			FROM marketplace_credit_purchases
			WHERE wallet_id = $1 AND amount_micros_remaining > 0
			ORDER BY amount_micros_remaining ASC
			LIMIT 10
			FOR UPDATE
		`, walletID)

		if err != nil {
			return nil, fmt.Errorf("failed to query credit purchases: %w", err)
		}

		// Collect the rows using pgx.CollectRows
		selectedCreditPurchases, err := pgx.CollectRows(rows, pgx.RowToStructByName[CreditPurchase])
		if err != nil {
			return nil, fmt.Errorf("failed to collect rows: %w", err)
		}

		// Calculate total available credit
		var totalAvailableCredit int64
		for _, creditPurchase := range selectedCreditPurchases {
			totalAvailableCredit += creditPurchase.AmountMicrosRemaining
		}

		// Check if total credit is sufficient
		if totalAvailableCredit < updatedLog.PriceMicros {
			return nil, fmt.Errorf("insufficient total balance on credit purchase records: available %d, required %d", totalAvailableCredit, updatedLog.PriceMicros)
		}

		var lastCreatedTransferID string

		remainingAmountToTransfer := updatedLog.PriceMicros
		for _, creditPurchase := range selectedCreditPurchases {
			transferAmount := remainingAmountToTransfer
			if remainingAmountToTransfer > creditPurchase.AmountMicrosRemaining {
				transferAmount = creditPurchase.AmountMicrosRemaining
			}
			remainingAmountToTransfer = remainingAmountToTransfer - transferAmount
			metadata := map[string]string{
				"marketplace_request_log_id": updatedLog.ID,
				"host_project_id":            updatedLog.HostProjectID,
				"identity_id":                updatedLog.IdentityID,
			}
			if transactionID != nil {
				metadata["wallet_transaction_id"] = *transactionID
			}
			transferParams := &stripe.TransferParams{
				Amount:            stripe.Int64(transferAmount / 10000),
				Currency:          stripe.String(string(stripe.CurrencyUSD)),
				Destination:       stripe.String(stripeConnectedAccountID),
				SourceTransaction: stripe.String(creditPurchase.StripeChargeID),
				Metadata:          metadata,
			}
			createdTransfer, err := h.StripeClient.Transfers.New(transferParams)
			if err != nil {
				return nil, fmt.Errorf("an error occurred when calling the Stripe API to create a transfer: %w", err)
			}
			lastCreatedTransferID = createdTransfer.ID
			_, err = tx.Exec(ctx, `
				UPDATE marketplace_credit_purchases
				SET amount_micros_remaining = amount_micros_remaining - $1
				WHERE id = $2
			`, transferAmount, creditPurchase.ID)

			if err != nil {
				return nil, fmt.Errorf("failed to query credit purchases: %w", err)
			}
		}

		// TODO: Log all created transfer ids on wallet transactions?
		if len(selectedCreditPurchases) == 1 {
			_, err = tx.Exec(ctx, `
				UPDATE wallet_transactions 
				SET stripe_transfer_id = $1 
				WHERE id = $2
			`, lastCreatedTransferID, transactionID)

			if err != nil {
				return nil, fmt.Errorf("failed to update wallet transaction: %w", err)
			}
		}
	}

	if err = tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &updatedLog, nil
}

func matchPath(patternParts, pathParts []string) (bool, string) {
	if len(patternParts) != len(pathParts) {
		return false, ""
	}

	// Only extract thread ID if the pattern starts with "threads" and has a wildcard next
	var threadID string
	if len(patternParts) >= 2 && patternParts[0] == "threads" && patternParts[1] == "*" {
		threadID = pathParts[1]
	}

	// Check if path matches pattern
	for i, patternPart := range patternParts {
		if patternPart != "*" && patternPart != pathParts[i] {
			return false, ""
		}
	}
	return true, threadID
}

func (h *MarketplaceProxyEndpointHandler) validateRequest(r *http.Request, oplog *slog.Logger) (*RequestData, *HttpError) {
	headers := r.Header
	method := r.Method
	path := r.URL.Path
	if strings.HasSuffix(path, "/") {
		path = path[1 : len(path)-1]
	} else {
		path = path[1:]
	}

	langsmithApiKey := headers.Get("x-api-key")

	if langsmithApiKey == "" {
		return nil, &HttpError{Code: http.StatusUnauthorized, Message: `No LangSmith API key provided. Pass it in a header named "X-Api-Key".`}
	}

	authInfo := auth.GetOrgAuthInfo(r)

	if authInfo.OrganizationID == "" {
		return nil, &HttpError{Code: http.StatusUnauthorized, Message: "The provided LangSmith API key did not match an existing organization."}
	}

	oplog.Info("Authed", "organization_id", authInfo.OrganizationID)

	// Split the path into segments
	pathSegments := strings.Split(strings.Trim(path, "/"), "/")

	if len(pathSegments) < 3 {
		return nil, &HttpError{Code: http.StatusBadRequest, Message: "Invalid request path. Expected format: /marketplace/{deployment_id}/{method}"}
	}

	// Get deploymentId and remaining path segments
	var projectId string
	var normalizedPath string
	if len(pathSegments) > 0 {
		projectId = pathSegments[1]
		normalizedPath = strings.Join(pathSegments[2:], "/")
	}

	var isFreeEndpoint bool = false
	var isPaidEndpoint bool = false
	var threadID string

	pathParts := strings.Split(normalizedPath, "/")

	if paths, ok := freeEndpointPatterns[method]; ok {
		for _, pathPattern := range paths {
			var matched bool
			matched, threadID = matchPath(pathPattern, pathParts)
			if matched {
				isFreeEndpoint = true
				break
			}
		}
	}

	if paths, ok := pricedEndpointPatterns[method]; ok {
		for _, pathPattern := range paths {
			var matched bool
			matched, threadID = matchPath(pathPattern, pathParts)
			if matched {
				isPaidEndpoint = true
				break
			}
		}
	}

	if !isFreeEndpoint && !isPaidEndpoint {
		return nil, &HttpError{Code: http.StatusNotFound, Message: fmt.Sprintf("Path and HTTP method does not exist for marketplace agents: %s %s", method, normalizedPath)}
	}

	if isFreeEndpoint && isPaidEndpoint {
		return nil, &HttpError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("There was a problem routing to %s %s", method, normalizedPath)}
	}

	// All endpoints are currently free
	isFreeEndpoint = true
	isPaidEndpoint = false

	var threadIDPtr *string
	if threadID != "" {
		threadIDPtr = &threadID
	}

	return &RequestData{
		HostProjectID:   projectId,
		Headers:         headers,
		Path:            r.URL.Path,
		Method:          method,
		AuthInfo:        *authInfo,
		NormalizedPath:  normalizedPath,
		LangsmithApiKey: langsmithApiKey,
		IsFreeEndpoint:  isFreeEndpoint,
		IsPaidEndpoint:  isPaidEndpoint,
		ThreadID:        threadIDPtr,
	}, nil
}

func (h *MarketplaceProxyEndpointHandler) ProxyRequest(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())
	validationInfo, validationError := h.validateRequest(r, oplog)
	if validationError != nil {
		render.Status(r, validationError.Code)
		render.JSON(w, r, Response{Message: validationError.Message})
		return
	}

	// Add nil check for validationInfo
	if validationInfo == nil {
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Internal server error: validation failed"})
		return
	}

	requestLogID := uuid.New().String()
	var requestLog *CreateMarketplaceRequestLogParams

	var hostProjectURL string
	var pricePerRequestMicros int
	var ownerTenantID string
	var isOwner bool

	if validationInfo.IsFreeEndpoint {
		rows, _ := h.Pg.Query(r.Context(), getHostProjectPricesQuery, validationInfo.HostProjectID)
		projectPrice, err := pgx.CollectOneRow(rows, pgx.RowToStructByPos[ProjectPrice])
		if err == pgx.ErrNoRows {
			render.Status(r, http.StatusNotFound)
			render.JSON(w, r, Response{Message: "The requested project is not public."})
			return
		} else if err != nil {
			oplog.Error("Failed to fetch pricing", "err", err)
			render.Status(r, http.StatusInternalServerError)
			render.JSON(w, r, Response{Message: "Internal server error: failed to fetch public status."})
			return
		}
		hostProjectURL = projectPrice.HostProjectURL
		pricePerRequestMicros = 0
		ownerTenantID = projectPrice.OwnerTenantID
	} else {
		oplog.Info("Creating log for request", "request_id", requestLogID)

		var lsUserID *string
		if validationInfo.AuthInfo.LSUserID != "" {
			lsUserID = &validationInfo.AuthInfo.LSUserID
		}

		requestLog = &CreateMarketplaceRequestLogParams{
			ID:             requestLogID,
			IdentityID:     validationInfo.AuthInfo.IdentityID.String,
			LSUserID:       lsUserID,
			OrganizationID: validationInfo.AuthInfo.OrganizationID,
			HostProjectID:  validationInfo.HostProjectID,
			// TODO: Parse body and identify assistant id?
			// Should we make this a header instead?
			AssistantID:      "",
			Method:           validationInfo.Method,
			Path:             validationInfo.Path,
			RequestStartedAt: time.Now().UTC(),
			RequestEndedAt:   time.Now().UTC(),
			IPAddress:        r.Header.Get("X-Forwarded-For"),
			UserAgent:        r.Header.Get("User-Agent"),
			ThreadID:         validationInfo.ThreadID,
		}
		var err error
		hostProjectURL, pricePerRequestMicros, ownerTenantID, isOwner, err = h.createMarketplaceRequestLog(r.Context(), requestLog)
		if err != nil {
			oplog.Error("Error creating marketplace request log", "err", err)
			var pgErr *pgconn.PgError
			var httpErr *HttpError
			if errors.As(err, &pgErr) {
				switch pgErr.Code {
				case "P0402":
					render.Status(r, http.StatusPaymentRequired)
					render.JSON(w, r, Response{Message: "You have insufficient credit balance to call this agent."})
				default:
					render.Status(r, http.StatusInternalServerError)
					render.JSON(w, r, Response{Message: "Failed to log marketplace request"})
				}
				return
			} else if errors.As(err, &httpErr) {
				render.Status(r, httpErr.Code)
				render.JSON(w, r, Response{Message: httpErr.Message})
				return
			}
			render.Status(r, http.StatusInternalServerError)
			render.JSON(w, r, Response{Message: "Failed to proxy marketplace request"})
			return
		}
	}

	langGraphDeployUrl := fmt.Sprintf("%s/%s", hostProjectURL, validationInfo.NormalizedPath)

	oplog.Info("charging for marketplace request",
		"price_micros", pricePerRequestMicros,
		"method", validationInfo.Method,
		"url", langGraphDeployUrl,
		"is_owner", isOwner,
	)

	// Create new request
	proxyReq, err := http.NewRequestWithContext(r.Context(), validationInfo.Method, langGraphDeployUrl, r.Body)
	if err != nil {
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to create proxy request"})
		return
	}

	oplog.Info("Signing request", "tenant_id", ownerTenantID, "url", langGraphDeployUrl)

	// Copy headers
	proxyReq.Header = validationInfo.Headers
	token, err := h.Signer.SignXServiceKeyToken(ownerTenantID)
	if err != nil {
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to sign proxy request"})
		return
	}

	proxyReq.Header.Del("x-api-key")
	proxyReq.Header.Add("x-service-key", token)

	oplog.Info("Proxying request", "url", langGraphDeployUrl)

	// Send the request
	client := h.HTTPClient
	langGraphPlatformResponse, err := client.Do(proxyReq)
	if err != nil {
		oplog.Info("Error proxying request", "err", err)
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, Response{Message: "Failed to proxy request to LangGraph platform deployment"})
		return
	}

	defer langGraphPlatformResponse.Body.Close()

	// Run completion in background
	if validationInfo.IsPaidEndpoint {
		go func() {
			defer func() {
				if r := recover(); r != nil {
					oplog.Error("Panic in request completion goroutine", "panic", r)
				}
			}()
			oplog.Info("Completing request", "id", requestLog.ID)
			if _, err := h.completeMarketplaceRequestLog(context.Background(), requestLog.ID, "complete", langGraphPlatformResponse.StatusCode); err != nil {
				oplog.Error("Error completing marketplace request log", "err", err)
			}
		}()
	}

	// Copy response headers, excluding CORS headers and sensitive headers
	for key, values := range langGraphPlatformResponse.Header {
		lowerKey := strings.ToLower(key)
		if strings.HasPrefix(lowerKey, "access-control-") {
			continue
		}
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	w.Header().Add("x-langsmith-credits-debited", fmt.Sprintf("%d", pricePerRequestMicros))

	// Set status code
	w.WriteHeader(langGraphPlatformResponse.StatusCode)

	// Copy the body directly to the response writer
	if _, err := io.Copy(w, langGraphPlatformResponse.Body); err != nil {
		oplog.Error(fmt.Sprintf("Error copying response body: %v", err))
		// Note: Cannot write error response here as headers are already sent
	}
}
