package runs

// Prefetched run token stats
type QueriedRunTokenStats struct {
	TotalTokens      uint64   `json:"total_tokens"`
	CompletionTokens uint64   `json:"completion_tokens"`
	PromptTokens     uint64   `json:"prompt_tokens"`
	TotalCost        *float64 `json:"total_cost"`
	CompletionCost   *float64 `json:"completion_cost"`
	PromptCost       *float64 `json:"prompt_cost"`
	FirstTokenTime   *string  `json:"first_token_time"`
}

// Prefetched run data
type QueriedRun struct {
	Id                   string                 `json:"id"`
	Name                 string                 `json:"name"`
	IsRoot               bool                   `json:"is_root"`
	Extra                map[string]interface{} `json:"extra"`
	Status               string                 `json:"status"`
	DottedOrder          string                 `json:"dotted_order"`
	Tags                 []string               `json:"tags"`
	ParentRunID          string                 `json:"parent_run_id"`
	ModifiedAt           string                 `json:"modified_at"`
	S3Urls               map[string]interface{} `json:"s3_urls"`
	TenantID             string                 `json:"tenant_id"`
	TraceID              string                 `json:"trace_id"`
	SessionID            string                 `json:"session_id"`
	RunType              string                 `json:"run_type"`
	StartTime            string                 `json:"start_time"`
	EndTime              string                 `json:"end_time"`
	AppPath              string                 `json:"app_path"`
	QueriedRunTokenStats `json:"inline"`
}

type TraceQueryResponse struct {
	QueriedRuns []*QueriedRun `json:"runs"`
}
