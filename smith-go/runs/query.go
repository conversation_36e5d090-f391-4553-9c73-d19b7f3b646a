package runs

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/util"
)

func NewRunQueryHandler(clickHousePool *driver.Conn) *RunQueryHandler {
	return &RunQueryHandler{
		ClickHousePool: clickHousePool,
	}
}

type RunQueryHandler struct {
	ClickHousePool *driver.Conn
	Oplog          *slog.Logger
}

var (
	errTraceIDRequired     = errors.New("trace ID is required")
	errInvalidQueryParams  = errors.New("invalid query params")
	errServiceUnavailable  = errors.New("service unavailable")
	errInternalServerError = errors.New("internal server error")
)

func (h *RunQueryHandler) handleError(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, err error, logString string) {
	var status int
	message := err.Error()
	logMessage := fmt.Sprintf("%s: %s", err.Error(), logString)

	switch {
	case errors.Is(err, errTraceIDRequired),
		errors.Is(err, errInvalidQueryParams):
		status = http.StatusBadRequest
		oplog.Warn(logMessage)
		// propogate full error message in this case
		message = logMessage
	case errors.Is(err, errUnauthorized):
		oplog.Warn(logMessage)
		status = http.StatusForbidden
	default:
		// if it's known transient => 503, else 500
		if util.IsRetriableError(err) {
			oplog.Warn(logMessage)
			status = http.StatusServiceUnavailable
			message = errServiceUnavailable.Error()
		} else {
			oplog.Error(logMessage)
			status = http.StatusInternalServerError
			message = errInternalServerError.Error()
		}
	}
	render.Status(r, status)
	render.JSON(w, r, map[string]string{"error": message})
}

// QueryTrace queries the trace for the given session ID and trace ID.
func (h *RunQueryHandler) QueryTrace(
	ctx context.Context,
	sessionID string,
	traceID string,
	tenantID string,
	startTime *time.Time,
	endTime *time.Time,
) ([]*QueriedRun, *RunTimeBounds, error) {
	results := []*QueriedRun{}
	runTimeBounds := &RunTimeBounds{}

	query := `
	SELECT
		runs.id,
		runs.tenant_id, 
		runs.session_id, 
		runs.is_root, 
		runs.start_time,
		argMax(runs.tags, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS tags,
		argMax(runs.reference_example_id, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS reference_example_id,
		argMax(runs.trace_id, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS trace_id,
		argMax(runs.inputs, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS inputs,
		argMax(runs.run_type, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS run_type,
		argMax(runs.extra, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS extra,
		argMax(runs.outputs, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS outputs,
		argMax(runs.s3_urls, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS s3_urls,
		argMax(runs.events, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS events,
		argMax(runs.tenant_id, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS aggregated_tenant_id,
		argMax(runs.dotted_order, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS dotted_order,
		argMax(runs.status, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS status,
		argMax(runs.parent_run_id, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS parent_run_id,
		argMax(runs.inputs_s3_urls, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS inputs_s3_urls,
		argMax(runs.end_time, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS end_time,                
		argMax(runs.error, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS error,
		argMax(runs.name, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS name,
		argMax(runs.modified_at, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS aggregated_modified_at,
		argMax(runs.session_id, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS aggregated_session_id,
		argMax(runs.outputs_s3_urls, coalesce(runs.modified_at, runs.end_time, runs.start_time)) AS outputs_s3_urls
	FROM runs
	WHERE 
    runs.trace_id = $1 AND 
    runs.session_id IN ($2) AND 
    runs.tenant_id = $3 AND
    runs.is_root = 0 AND
    runs.start_time >= $4 AND 
    runs.start_time <= $5 AND
    runs.id IN (
		SELECT id
		FROM runs_trace_id
		WHERE trace_id = $1 AND (session_id IN $2) AND (tenant_id = $3)
    ) 
	GROUP BY 
		runs.id, runs.tenant_id, runs.session_id, runs.is_root, runs.start_time
	SETTINGS 
		multiple_joins_try_to_keep_original_names = 1, 
		optimize_read_in_order = 1, 
		max_execution_time = 30.0
	`

	var startTimeUTC, endTimeUTC time.Time
	if startTime != nil {
		startTimeUTC = startTime.UTC().Add(
			-time.Duration(config.Env.TokenStatsStartTimeBufferMs) * time.Millisecond,
		)
	}
	if endTime != nil {
		endTimeUTC = endTime.UTC().Add(
			time.Duration(config.Env.TokenStatsStartTimeBufferMs) * time.Millisecond,
		)
	}

	rows, err := (*h.ClickHousePool).Query(
		ctx,
		query,
		traceID,
		sessionID,
		tenantID,
		startTimeUTC,
		endTimeUTC,
	)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to query trace: %w", err)
	}

	defer rows.Close()

	for rows.Next() {
		var (
			id                  uuid.UUID
			tenantID            uuid.UUID
			sessionID           uuid.UUID
			isRoot              bool
			startTime           time.Time
			tags                []string
			referenceExampleID  uuid.UUID
			traceID             uuid.UUID
			inputs              string
			runType             string
			extra               string
			outputs             string
			s3Urls              string
			events              string
			aggregatedTenantID  uuid.UUID
			dottedOrder         string
			status              string
			parentRunID         uuid.UUID
			inputsS3Urls        string
			endTime             time.Time
			errorStr            string
			name                string
			modifiedAt          time.Time
			aggregatedSessionID uuid.UUID
			outputsS3Urls       string
		)
		if err := rows.Scan(
			&id,
			&tenantID,
			&sessionID,
			&isRoot,
			&startTime,
			&tags,
			&referenceExampleID,
			&traceID,
			&inputs,
			&runType,
			&extra,
			&outputs,
			&s3Urls,
			&events,
			&aggregatedTenantID,
			&dottedOrder,
			&status,
			&parentRunID,
			&inputsS3Urls,
			&endTime,
			&errorStr,
			&name,
			&modifiedAt,
			&aggregatedSessionID,
			&outputsS3Urls,
		); err != nil {
			return nil, nil, fmt.Errorf("failed to scan rows: %w", err)
		}

		// parse extra field as map[string]interface{}
		var extraMap map[string]interface{}
		if err := json.Unmarshal([]byte(extra), &extraMap); err != nil {
			return nil, nil, fmt.Errorf("failed to parse extra field JSON: %w", err)
		}

		results = append(results, &QueriedRun{
			Id:          id.String(),
			Name:        name,
			IsRoot:      isRoot,
			Extra:       extraMap,
			Status:      status,
			DottedOrder: dottedOrder,
			Tags:        tags,
			ParentRunID: parentRunID.String(),
			ModifiedAt:  modifiedAt.Format("2006-01-02T15:04:05.000000"),
			S3Urls: func() map[string]interface{} {
				var s3UrlsMap map[string]interface{}
				err := json.Unmarshal([]byte(s3Urls), &s3UrlsMap)
				if err != nil {
					return map[string]interface{}{}
				}
				return s3UrlsMap
			}(),
			TenantID:  tenantID.String(),
			TraceID:   traceID.String(),
			SessionID: sessionID.String(),
			RunType:   runType,
			AppPath: fmt.Sprintf(
				"/o/%s/projects/p/%s/r/%s?trace_id=%s&start_time=%s",
				tenantID.String(),
				sessionID.String(),
				id.String(),
				traceID.String(),
				startTime.Format("2006-01-02T15:04:05.000000"),
			),
			StartTime: startTime.Format("2006-01-02T15:04:05.000000"),
			EndTime:   endTime.Format("2006-01-02T15:04:05.000000"),
		})

		if runTimeBounds.MinStartTime.IsZero() || runTimeBounds.MaxStartTime.IsZero() {
			runTimeBounds.MinStartTime = startTime
			runTimeBounds.MaxStartTime = startTime
		} else if startTime.Before(runTimeBounds.MinStartTime) {
			runTimeBounds.MinStartTime = startTime
		} else if startTime.After(runTimeBounds.MaxStartTime) {
			runTimeBounds.MaxStartTime = startTime
		}
	}

	if !runTimeBounds.MinStartTime.IsZero() {
		runTimeBounds.MinStartTime = runTimeBounds.MinStartTime.Add(
			-time.Duration(config.Env.TokenStatsStartTimeBufferMs) * time.Millisecond,
		)
	}

	if !runTimeBounds.MaxStartTime.IsZero() {
		runTimeBounds.MaxStartTime = runTimeBounds.MaxStartTime.Add(
			time.Duration(config.Env.TokenStatsStartTimeBufferMs) * time.Millisecond,
		)
	}

	return results, runTimeBounds, nil
}

// EnrichTraceTokenStats enriches the trace with token stats
func (h *RunQueryHandler) EnrichTraceTokenStats(
	ctx context.Context,
	traceID string,
	sessionID string,
	tenantID string,
	runTimeBounds *RunTimeBounds,
	QueriedRuns []*QueriedRun,
) error {
	results := map[string]*QueriedRunTokenStats{}

	tokenStatsQuery := `
		 SELECT
			id,
			sum(total_tokens) as total_tokens,
			sum(completion_tokens) as completion_tokens,
			sum(prompt_tokens) as prompt_tokens,
			sum(total_cost) as total_cost,
			sum(completion_cost) as completion_cost,
			sum(prompt_cost) as prompt_cost,
			min(first_token_time) as first_token_time
		FROM runs_token_counts FINAL
		PREWHERE tenant_id = $1
		AND session_id = $2
		AND is_root = 0
		AND start_time >= $3
		AND start_time <= $4
		AND id IN $5
		AND runs_token_counts.total_tokens < 4000000000
		GROUP BY id;
	`

	runIds := []string{}
	for _, run := range QueriedRuns {
		runIds = append(runIds, run.Id)
	}

	rows, err := (*h.ClickHousePool).Query(
		ctx,
		tokenStatsQuery,
		tenantID,
		sessionID,
		runTimeBounds.MinStartTime.UTC(),
		runTimeBounds.MaxStartTime.UTC(),
		runIds,
	)
	if err != nil {
		return fmt.Errorf("failed to query trace token stats: %w", err)
	}

	defer rows.Close()

	for rows.Next() {
		var (
			id               uuid.UUID
			totalTokens      uint64
			completionTokens uint64
			promptTokens     uint64
			totalCost        *decimal.Decimal
			completionCost   *decimal.Decimal
			promptCost       *decimal.Decimal
			firstTokenTime   *time.Time
		)
		if err := rows.Scan(
			&id,
			&totalTokens,
			&completionTokens,
			&promptTokens,
			&totalCost,
			&completionCost,
			&promptCost,
			&firstTokenTime,
		); err != nil {
			return fmt.Errorf("failed to scan row: %w", err)
		}

		totalCostFloat := (*float64)(nil)
		completionCostFloat := (*float64)(nil)
		promptCostFloat := (*float64)(nil)

		if totalCost != nil {
			f, _ := totalCost.Float64()
			totalCostFloat = &f
		}
		if completionCost != nil {
			f, _ := completionCost.Float64()
			completionCostFloat = &f
		}
		if promptCost != nil {
			f, _ := promptCost.Float64()
			promptCostFloat = &f
		}

		firstTokenTimePtr := (*string)(nil)
		if firstTokenTime != nil && !firstTokenTime.IsZero() {
			firstTokenTimeStr := firstTokenTime.Format("2006-01-02T15:04:05.000000")
			firstTokenTimePtr = &firstTokenTimeStr
		}

		// add token stats to results map
		results[id.String()] = &QueriedRunTokenStats{
			TotalTokens:      totalTokens,
			CompletionTokens: completionTokens,
			PromptTokens:     promptTokens,
			TotalCost:        totalCostFloat,
			CompletionCost:   completionCostFloat,
			PromptCost:       promptCostFloat,
			FirstTokenTime:   firstTokenTimePtr,
		}
	}

	// enrich QueriedRuns with token stats
	for _, run := range QueriedRuns {
		if tokenStats, ok := results[run.Id]; ok {
			run.QueriedRunTokenStats = *tokenStats
		} else {
			run.QueriedRunTokenStats = QueriedRunTokenStats{}
		}
	}

	return nil
}

// SingleTraceQuery is the handler for the single trace query
func (h *RunQueryHandler) SingleTraceQuery(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()
	oplog := httplog.LogEntry(ctx)

	authInfo := auth.GetAuthInfo(r)
	if authInfo == nil {
		h.handleError(w, r, oplog, errUnauthorized, "")
		return
	}

	// parse trace_id from url param
	traceID := chi.URLParam(r, "trace_id")
	if traceID == "" {
		h.handleError(w, r, oplog, errTraceIDRequired, "")
		return
	}

	// parse session_id, is_root, start_time, end_time query params
	query := r.URL.Query()
	sessionID := query.Get("session_id")
	if sessionID == "" {
		h.handleError(w, r, oplog, errInvalidQueryParams, "")
		return
	}
	// parse start_time
	startTimeStr := query.Get("start_time")
	if startTimeStr == "" {
		h.handleError(w, r, oplog, errInvalidQueryParams, "")
		return
	}
	startTime, err := time.Parse(time.RFC3339, startTimeStr)
	if err != nil {
		h.handleError(w, r, oplog, errInvalidQueryParams, "")
		return
	}
	startTimePtr := &startTime
	// parse end_time
	var endTime time.Time
	endTimeStr := query.Get("end_time")
	if endTimeStr == "" {
		endTime = time.Now()
	} else {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			h.handleError(w, r, oplog, errInvalidQueryParams, "")
			return
		}
	}
	endTimePtr := &endTime

	// query trace
	QueriedRuns, runTimeBounds, err := h.QueryTrace(
		ctx,
		sessionID,
		traceID,
		authInfo.TenantID,
		startTimePtr,
		endTimePtr,
	)
	if err != nil {
		h.handleError(w, r, oplog, err, err.Error())
		return
	}

	// if the number of runs is less then the skip expensive
	// threshold we can enrich the trace with token stats
	if len(QueriedRuns) < config.Env.FetchRunsThresholdSkipExpensive {
		err = h.EnrichTraceTokenStats(
			ctx,
			traceID,
			sessionID,
			authInfo.TenantID,
			runTimeBounds,
			QueriedRuns,
		)
		if err != nil {
			h.handleError(w, r, oplog, err, err.Error())
			return
		}
	}

	traceQueryResponse := TraceQueryResponse{
		QueriedRuns: QueriedRuns,
	}

	// return response
	render.Status(r, http.StatusOK)
	render.JSON(w, r, traceQueryResponse)
}
