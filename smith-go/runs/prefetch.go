package runs

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2/lib/driver"
	"github.com/DataDog/datadog-go/v5/statsd"
	"github.com/DataDog/zstd"
	"github.com/go-chi/httplog/v2"
	"github.com/go-chi/render"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
)

// Prefetch request body
type PrefetchRequest struct {
	SessionId string   `json:"session_id" validate:"required,uuid"`
	TraceIds  []string `json:"trace_ids" validate:"required,min=1,max=100,dive,uuid"`
}

// Prefetched run token stats
type PrefetchedRunTokenStats struct {
	TotalTokens      uint64   `json:"total_tokens"`
	CompletionTokens uint64   `json:"completion_tokens"`
	PromptTokens     uint64   `json:"prompt_tokens"`
	TotalCost        *float64 `json:"total_cost"`
	CompletionCost   *float64 `json:"completion_cost"`
	PromptCost       *float64 `json:"prompt_cost"`
	FirstTokenTime   *string  `json:"first_token_time"`
}

// Prefetched run data
type PrefetchedRun struct {
	Id          string                 `json:"id"`
	Name        string                 `json:"name"`
	IsRoot      bool                   `json:"is_root"`
	Extra       map[string]interface{} `json:"extra"`
	Status      string                 `json:"status"`
	DottedOrder string                 `json:"dotted_order"`
	Tags        []string               `json:"tags"`
	ParentRunID string                 `json:"parent_run_id"`
	ModifiedAt  string                 `json:"modified_at"`
	S3Urls      map[string]interface{} `json:"s3_urls"`
	TenantID    string                 `json:"tenant_id"`
	TraceID     string                 `json:"trace_id"`
	SessionID   string                 `json:"session_id"`
	RunType     string                 `json:"run_type"`
	StartTime   string                 `json:"start_time"`
	EndTime     string                 `json:"end_time"`
	PrefetchedRunTokenStats
}

// Records the time bounds for all runs of the traces being prefetched
type RunTimeBounds struct {
	MinStartTime time.Time
	MaxStartTime time.Time
}

// Run prefetch handler
type RunPrefetchHandler struct {
	CachingRedisPool redis.UniversalClient
	ClickHousePool   *driver.Conn
	StatsdClient     *statsd.Client
}

// Maps a run id to its trace id and its position in the list of child runs for that trace
type RunIdIndex struct {
	TraceId string
	Index   int
}

func NewRunPrefetchHandler(cachingRedisPool redis.UniversalClient, clickHousePool *driver.Conn, statsdClient *statsd.Client) *RunPrefetchHandler {
	return &RunPrefetchHandler{
		CachingRedisPool: cachingRedisPool,
		ClickHousePool:   clickHousePool,
		StatsdClient:     statsdClient,
	}
}

// Prefetches runs from the runs table and token stats from the runs_token_counts table and stores in redis
func (h *RunPrefetchHandler) PrefetchRunTable(ctx context.Context, auth *auth.AuthInfo, results *map[string][]PrefetchedRun, runIdToTraceIdMap *map[string]RunIdIndex, runTimeBounds *RunTimeBounds, sessionID string, traceIDs []string) error {
	runsQuery := `
		WITH filtered_runs_cte AS (
		SELECT
			runs.tenant_id as tenant_id, runs.session_id as session_id, runs.is_root as is_root, runs.start_time as start_time, runs.id as id
		FROM runs
			WHERE ((runs.tenant_id, runs.is_root, runs.session_id, runs.start_time, runs.id) 
			IN (
				SELECT 
					tenant_id,
					is_root,
					session_id,
					start_time,
					id
				FROM runs_trace_id
				WHERE trace_id IN $3 AND is_root = 0 AND session_id = $2 AND tenant_id = $1
			)) AND ((runs.trace_first_received_at IS NOT NULL AND runs.ttl_seconds IS NOT NULL AND runs.trace_first_received_at < now() - toIntervalSecond(runs.ttl_seconds)) = 0) AND (runs.is_root = 0) AND (runs.session_id IN [$2]) AND (runs.tenant_id = $1) AND (toDateTime64(now(), 6, 'UTC') > toIntervalSecond($4) + runs.trace_first_received_at) AND runs.status <> 'pending'
					ORDER BY runs.start_time DESC, toString(runs.id) DESC)
		SELECT 
			runs.name AS name,
			runs.id AS id,
			runs.is_root AS is_root,
			runs.extra AS extra,
			runs.status AS status,
			runs.dotted_order AS dotted_order,
			runs.tags AS tags,
			runs.parent_run_id AS parent_run_id,
			runs.modified_at AS modified_at,
			runs.s3_urls AS s3_urls,
			runs.tenant_id AS tenant_id,
			runs.trace_id AS trace_id,
			runs.session_id AS session_id,
			runs.run_type AS run_type,
			runs.start_time AS start_time,
			runs.end_time AS end_time
		FROM runs 
		WHERE (runs.tenant_id, runs.session_id, runs.is_root, runs.start_time, runs.id) IN (SELECT tenant_id, session_id, is_root, start_time, id from filtered_runs_cte)
		ORDER BY runs.start_time DESC, toString(runs.id) DESC, coalesce(runs.modified_at, runs.end_time, runs.start_time) DESC
		LIMIT 1 BY runs.id;
	`

	rows, err := (*h.ClickHousePool).Query(ctx, runsQuery, auth.TenantID, sessionID, traceIDs, config.Env.MinimumTraceAgeSeconds)
	if err != nil {
		return fmt.Errorf("query execution failed: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var (
			name        string
			id          uuid.UUID
			isRoot      bool
			extra       string
			status      string
			dottedOrder string
			tags        []string
			parentRunID uuid.UUID
			modifiedAt  time.Time
			s3Urls      string
			tenantID    uuid.UUID
			traceID     uuid.UUID
			sessionID   uuid.UUID
			runType     string
			startTime   time.Time
			endTime     time.Time
		)
		if err := rows.Scan(&name, &id, &isRoot, &extra, &status, &dottedOrder, &tags, &parentRunID, &modifiedAt, &s3Urls, &tenantID, &traceID, &sessionID, &runType, &startTime, &endTime); err != nil {
			return fmt.Errorf("failed to scan rows: %w", err)
		}

		// parse extra field as map[string]interface{}
		var extraMap map[string]interface{}
		if err := json.Unmarshal([]byte(extra), &extraMap); err != nil {
			return fmt.Errorf("failed to parse extra field JSON: %w", err)
		}

		(*results)[traceID.String()] = append((*results)[traceID.String()], PrefetchedRun{
			Id:          id.String(),
			Name:        name,
			IsRoot:      isRoot,
			Extra:       extraMap,
			Status:      status,
			DottedOrder: dottedOrder,
			Tags:        tags,
			ParentRunID: parentRunID.String(),
			ModifiedAt:  modifiedAt.Format("2006-01-02T15:04:05.000000"),
			S3Urls: func() map[string]interface{} {
				var s3UrlsMap map[string]interface{}
				err := json.Unmarshal([]byte(s3Urls), &s3UrlsMap)
				if err != nil {
					return map[string]interface{}{}
				}
				return s3UrlsMap
			}(),
			TenantID:  tenantID.String(),
			TraceID:   traceID.String(),
			SessionID: sessionID.String(),
			RunType:   runType,
			StartTime: startTime.Format("2006-01-02T15:04:05.000000"),
			EndTime:   endTime.Format("2006-01-02T15:04:05.000000"),
		})

		(*runIdToTraceIdMap)[id.String()] = RunIdIndex{
			TraceId: traceID.String(),
			Index:   len((*results)[traceID.String()]) - 1,
		}

		if runTimeBounds.MinStartTime.IsZero() || runTimeBounds.MaxStartTime.IsZero() {
			runTimeBounds.MinStartTime = startTime
			runTimeBounds.MaxStartTime = startTime
		} else if startTime.Before(runTimeBounds.MinStartTime) {
			runTimeBounds.MinStartTime = startTime
		} else if startTime.After(runTimeBounds.MaxStartTime) {
			runTimeBounds.MaxStartTime = startTime
		}
	}

	if !runTimeBounds.MinStartTime.IsZero() {
		runTimeBounds.MinStartTime = runTimeBounds.MinStartTime.Add(-time.Duration(config.Env.TokenStatsStartTimeBufferMs) * time.Millisecond)
	}
	if !runTimeBounds.MaxStartTime.IsZero() {
		runTimeBounds.MaxStartTime = runTimeBounds.MaxStartTime.Add(time.Duration(config.Env.TokenStatsStartTimeBufferMs) * time.Millisecond)
	}
	return nil
}

func (h *RunPrefetchHandler) PrefetchTokenStats(ctx context.Context, auth *auth.AuthInfo, results *map[string][]PrefetchedRun, runIdToTraceIdMap *map[string]RunIdIndex, runTimeBounds *RunTimeBounds, sessionID string, traceIds []string) error {
	tokenStatsQuery := `
		WITH trace_cte AS 
		(
			SELECT
				id,
				tenant_id,
				session_id
			FROM runs_trace_id
			PREWHERE tenant_id = $1 AND session_id = $2 AND trace_id IN $3 AND is_root = 0
		)
		SELECT
			id,
			sum(total_tokens) as total_tokens,
			sum(completion_tokens) as completion_tokens,
			sum(prompt_tokens) as prompt_tokens,
			sum(total_cost) as total_cost,
			sum(completion_cost) as completion_cost,
			sum(prompt_cost) as prompt_cost,
			min(first_token_time) as first_token_time
		FROM runs_token_counts FINAL
		PREWHERE start_time >= $4 AND start_time <= $5
		WHERE (id, tenant_id, session_id) IN (
			SELECT id, tenant_id, session_id FROM trace_cte
		)
		GROUP BY runs_token_counts.id;
	`

	rows, err := (*h.ClickHousePool).Query(ctx, tokenStatsQuery, auth.TenantID, sessionID, traceIds, runTimeBounds.MinStartTime, runTimeBounds.MaxStartTime)
	if err != nil {
		return fmt.Errorf("query execution failed: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var (
			id               uuid.UUID
			totalTokens      uint64
			completionTokens uint64
			promptTokens     uint64
			totalCost        *decimal.Decimal
			completionCost   *decimal.Decimal
			promptCost       *decimal.Decimal
			firstTokenTime   *time.Time
		)
		if err := rows.Scan(&id, &totalTokens, &completionTokens, &promptTokens, &totalCost, &completionCost, &promptCost, &firstTokenTime); err != nil {
			return fmt.Errorf("failed to scan row: %w", err)
		}

		totalCostFloat := (*float64)(nil)
		completionCostFloat := (*float64)(nil)
		promptCostFloat := (*float64)(nil)

		if totalCost != nil {
			f, _ := totalCost.Float64()
			totalCostFloat = &f
		}
		if completionCost != nil {
			f, _ := completionCost.Float64()
			completionCostFloat = &f
		}
		if promptCost != nil {
			f, _ := promptCost.Float64()
			promptCostFloat = &f
		}

		firstTokenTimeStr := (*string)(nil)
		if firstTokenTime != nil {
			formatted := firstTokenTime.Format("2006-01-02T15:04:05.000000")
			firstTokenTimeStr = &formatted
		}

		traceId := (*runIdToTraceIdMap)[id.String()].TraceId
		index := (*runIdToTraceIdMap)[id.String()].Index

		_, exists := (*runIdToTraceIdMap)[id.String()]
		if !exists || index >= len((*results)[traceId]) {
			continue
		}

		(*results)[traceId][index].PrefetchedRunTokenStats = PrefetchedRunTokenStats{
			TotalTokens:      totalTokens,
			CompletionTokens: completionTokens,
			PromptTokens:     promptTokens,
			TotalCost:        totalCostFloat,
			CompletionCost:   completionCostFloat,
			PromptCost:       promptCostFloat,
			FirstTokenTime:   firstTokenTimeStr,
		}
	}
	return nil
}

func (h *RunPrefetchHandler) PrefetchTraces(ctx context.Context, oplog *slog.Logger, auth *auth.AuthInfo, pipeline *redis.Pipeliner, sessionID string, traceIDs []string) (*map[string][]PrefetchedRun, error) {
	// master result set
	results := map[string][]PrefetchedRun{}
	runIdToTraceIdMap := map[string]RunIdIndex{}
	runTimeBounds := &RunTimeBounds{}

	err := h.PrefetchRunTable(ctx, auth, &results, &runIdToTraceIdMap, runTimeBounds, sessionID, traceIDs)
	if err != nil {
		oplog.Error("failed to prefetch run table", "error", err)
		return nil, fmt.Errorf("failed to prefetch runs")
	}

	err = h.PrefetchTokenStats(ctx, auth, &results, &runIdToTraceIdMap, runTimeBounds, sessionID, traceIDs)
	if err != nil {
		oplog.Error("failed to prefetch token stats", "error", err)
		return nil, fmt.Errorf("failed to prefetch token stats")
	}

	for traceID := range results {
		jsonData, err := json.Marshal(results[traceID])
		if err != nil {
			oplog.Error("failed to marshal runs to JSON", "error", err)
			return nil, fmt.Errorf("failed to complete prefetch")
		}

		compressedData, err := zstd.Compress(nil, jsonData)
		if err != nil {
			oplog.Error("failed to compress runs", "error", err)
			return nil, fmt.Errorf("failed to complete prefetch")
		}

		// set the prefetched runs in redis
		key := fmt.Sprintf("smith:cache:traces_prefetch:%s:%s:%s", auth.TenantID, sessionID, traceID)
		(*pipeline).Set(ctx, key, compressedData, time.Duration(config.Env.PrefetchKeyExpirationSec)*time.Second)

		if h.StatsdClient != nil {
			h.StatsdClient.Histogram("smith.prefetch.traces.size", float64(len(compressedData)), []string{"tenant_id: " + auth.TenantID}, 1)
		}
	}

	return &results, nil
}

func (h *RunPrefetchHandler) HandlePrefetchRuns(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	authInfo := auth.GetAuthInfo(r)
	oplog := httplog.LogEntry(ctx)

	if !config.Env.FFTracePrefetchEnabledTenantIds.Contains(authInfo.TenantID) {
		render.Status(r, http.StatusForbidden)
		render.JSON(w, r, map[string]string{"error": "forbidden"})
		return
	}

	var req PrefetchRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]string{"error": fmt.Sprintf("invalid request body: %s", err.Error())})
		return
	}

	validate := validator.New()
	err := validate.Struct(req)
	if err != nil {
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]string{"error": fmt.Sprintf("invalid request body: %s", err.Error())})
		return
	}

	pipeline := h.CachingRedisPool.Pipeline()

	results, err := h.PrefetchTraces(ctx, oplog, authInfo, &pipeline, req.SessionId, req.TraceIds)

	if err != nil {
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]string{"error": err.Error()})
		return
	}

	if len(*results) == 0 {
		render.Status(r, http.StatusCreated)
		render.JSON(w, r, map[string]string{})
		return
	}

	_, err = pipeline.Exec(ctx)

	// return response
	if err != nil {
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]string{"error": "error prefetching traces"})
		return
	}
	render.Status(r, http.StatusCreated)
	render.JSON(w, r, map[string]string{})
}
