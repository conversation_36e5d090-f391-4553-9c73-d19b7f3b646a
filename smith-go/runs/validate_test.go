package runs_test

import (
	"strings"
	"testing"

	"github.com/google/uuid"
	"langchain.com/smith/runs"
)

func TestValidateDottedOrder(t *testing.T) {
	// Generate UUIDs for testing
	traceID := uuid.New()
	runID := uuid.New()
	parentRunID := uuid.New()

	// Helper function to create a dotted_order part
	createDottedOrderPart := func(ts string, id uuid.UUID) string {
		return ts + "Z" + id.String()
	}

	// Valid timestamp string
	validTimestamp := "20240919T171648521691"

	// Test cases
	tests := []struct {
		name        string
		traceID     *uuid.UUID
		dottedOrder string
		parentRunID *uuid.UUID
		runID       uuid.UUID
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Both traceID and dottedOrder are nil/empty",
			traceID:     nil,
			dottedOrder: "",
			parentRunID: nil,
			runID:       runID,
			expectError: false,
		},
		{
			name:        "traceID provided without dottedOrder",
			traceID:     &traceID,
			dottedOrder: "",
			parentRunID: nil,
			runID:       runID,
			expectError: true,
			errorMsg:    "dotted_order is required when trace_id is provided",
		},
		{
			name:        "dottedOrder provided without traceID",
			traceID:     nil,
			dottedOrder: createDottedOrderPart(validTimestamp, traceID),
			parentRunID: nil,
			runID:       runID,
			expectError: true,
			errorMsg:    "trace_id is required when dotted_order is provided",
		},
		{
			name:        "Valid root run",
			traceID:     &traceID,
			dottedOrder: createDottedOrderPart(validTimestamp, traceID),
			parentRunID: nil,
			runID:       traceID,
			expectError: false,
		},
		{
			name:        "Valid child run",
			traceID:     &traceID,
			dottedOrder: createDottedOrderPart(validTimestamp, traceID) + "." + createDottedOrderPart(validTimestamp, runID),
			parentRunID: &traceID,
			runID:       runID,
			expectError: false,
		},
		{
			name:        "Invalid dottedOrder part (missing 'Z')",
			traceID:     &traceID,
			dottedOrder: validTimestamp + traceID.String(),
			parentRunID: nil,
			runID:       runID,
			expectError: true,
			errorMsg:    "invalid dotted_order part",
		},
		{
			name:        "Invalid UUID in dottedOrder",
			traceID:     &traceID,
			dottedOrder: validTimestamp + "Zinvalid-uuid",
			parentRunID: nil,
			runID:       runID,
			expectError: true,
			errorMsg:    "invalid UUID in dotted_order",
		},
		{
			name:        "Invalid timestamp in dottedOrder",
			traceID:     &traceID,
			dottedOrder: "invalidtimestampZ" + traceID.String(),
			parentRunID: nil,
			runID:       traceID, // Set runID to traceID to avoid run_id mismatch error
			expectError: true,
			errorMsg:    "invalid timestamp in dotted_order",
		},
		{
			name:        "TraceID does not match first part of dottedOrder",
			traceID:     &parentRunID, // Using parentRunID to cause mismatch
			dottedOrder: createDottedOrderPart(validTimestamp, traceID),
			parentRunID: nil,
			runID:       runID,
			expectError: true,
			errorMsg:    "trace_id",
		},
		{
			name:        "RunID does not match last part of dottedOrder",
			traceID:     &traceID,
			dottedOrder: createDottedOrderPart(validTimestamp, traceID) + "." + createDottedOrderPart(validTimestamp, parentRunID),
			parentRunID: &parentRunID,
			runID:       runID,
			expectError: true,
			errorMsg:    "run_id",
		},
		{
			name:        "ParentRunID does not match second-to-last part of dottedOrder",
			traceID:     &traceID,
			dottedOrder: createDottedOrderPart(validTimestamp, traceID) + "." + createDottedOrderPart(validTimestamp, runID),
			parentRunID: &parentRunID, // Mismatch here
			runID:       runID,
			expectError: true,
			errorMsg:    "parent_run_id",
		},
		{
			name:        "Duplicate UUID in dottedOrder",
			traceID:     &traceID,
			dottedOrder: createDottedOrderPart(validTimestamp, traceID) + "." + createDottedOrderPart(validTimestamp, traceID),
			parentRunID: &traceID,
			runID:       traceID,
			expectError: true,
			errorMsg:    "appears more than once",
		},
		{
			name:        "Timestamp earlier than parent timestamp",
			traceID:     &traceID,
			dottedOrder: createDottedOrderPart("20240429T004912090000", traceID) + "." + createDottedOrderPart("20240428T004912090000", runID),
			parentRunID: &traceID,
			runID:       runID,
			expectError: true,
			errorMsg:    "has timestamp",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			err := runs.ValidateDottedOrder(tc.traceID, tc.dottedOrder, tc.parentRunID, tc.runID)
			if tc.expectError {
				if err == nil {
					t.Errorf("Expected error but got nil")
				} else if !strings.Contains(err.Error(), tc.errorMsg) {
					t.Errorf("Expected error message to contain '%s', but got '%s'", tc.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}
