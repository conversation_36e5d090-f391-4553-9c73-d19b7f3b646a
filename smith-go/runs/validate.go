package runs

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/mitchellh/mapstructure"
	orderedmap "github.com/wk8/go-ordered-map/v2"
	"github.com/xeipuuv/gojsonschema"
	"langchain.com/smith/util"
)

func uuidToString(u *uuid.UUID) string {
	if u == nil {
		return "<nil>"
	}
	return u.String()
}

func ValidateDottedOrder(traceID *uuid.UUID, dottedOrder string, parentRunID *uuid.UUID, runID uuid.UUID) error {
	suffix := fmt.Sprintf(" for run_id:%s trace_id:%s dotted_order:%s parent_run_id:%s", runID.String(), uuidToString(traceID), dottedOrder, uuidToString(parentRunID))

	if traceID == nil && dottedOrder == "" {
		return nil
	} else if traceID != nil && dottedOrder == "" {
		return fmt.Errorf("dotted_order is required when trace_id is provided%s", suffix)
	} else if traceID == nil && dottedOrder != "" {
		return fmt.Errorf("trace_id is required when dotted_order is provided%s", suffix)
	} else if traceID != nil && dottedOrder != "" {
		dottedOrderParts := strings.Split(dottedOrder, ".")
		if parentRunID == nil {
			if len(dottedOrderParts) != 1 {
				return fmt.Errorf("dotted_order must contain a single part for root runs%s", suffix)
			}
		} else {
			if len(dottedOrderParts) < 2 {
				return fmt.Errorf("dotted_order must contain at least two parts for child runs%s", suffix)
			}
		}

		seenIDs := make(map[uuid.UUID]bool)
		var parentTs time.Time

		for i, part := range dottedOrderParts {
			tsAndUUID := strings.Split(part, "Z")
			if len(tsAndUUID) != 2 {
				return fmt.Errorf("invalid dotted_order part %s at index %d%s", part, i, suffix)
			}
			tsStr := tsAndUUID[0]
			uuidStr := tsAndUUID[1]

			partID, err := uuid.Parse(uuidStr)
			if err != nil {
				return fmt.Errorf("invalid UUID in dotted_order %s at index %d%s", uuidStr, i, suffix)
			}
			if seenIDs[partID] {
				return fmt.Errorf("dotted_order %s appears more than once%s", partID.String(), suffix)
			} else {
				seenIDs[partID] = true
			}

			if i == 0 && partID != *traceID {
				return fmt.Errorf("trace_id %s does not match first part of dotted_order %s%s", traceID.String(), partID.String(), suffix)
			}
			if i == len(dottedOrderParts)-1 && partID != runID {
				return fmt.Errorf("run_id %s does not match last part of dotted_order %s%s", runID.String(), partID.String(), suffix)
			}
			if i == len(dottedOrderParts)-2 && parentRunID != nil && partID != *parentRunID {
				return fmt.Errorf("parent_run_id %s does not match second-to-last part of dotted_order %s%s", parentRunID.String(), partID.String(), suffix)
			}

			partTs, err := util.ParseFlexibleTimestamp(tsStr)
			if err != nil {
				return fmt.Errorf("invalid timestamp in dotted_order %s at index %d: %v%s", tsStr, i, err, suffix)
			}

			if parentTs.IsZero() {
				parentTs = partTs
			} else {
				// Floor the microseconds to milliseconds
				prevTsFloored := parentTs.Truncate(time.Millisecond)
				currTsFloored := partTs.Truncate(time.Millisecond)
				if currTsFloored.Before(prevTsFloored) {
					return fmt.Errorf("dotted_order %s has timestamp %v earlier than parent timestamp %v%s", partID.String(), partTs, parentTs, suffix)
				} else {
					parentTs = partTs
				}
			}
		}
	}
	return nil
}

func ParseUUID(val *string, fieldName string) (*uuid.UUID, error) {
	if val == nil {
		return nil, nil
	}
	parsed, err := uuid.Parse(*val)
	if err != nil {
		return nil, fmt.Errorf("invalid UUID received for %s: %v", fieldName, *val)
	}
	return &parsed, nil
}

func ParseValidatePost(raw []byte) (*Run, error) {
	return parseAndValidate(raw, ValidatePostSchema)
}

func ParseValidatePatch(raw []byte) (*Run, error) {
	return parseAndValidate(raw, ValidatePatchSchema)
}

func ParseValidatePostMap(m map[string]interface{}) (*Run, error) {
	if err := ValidatePostSchema(m); err != nil {
		return nil, fmt.Errorf("schema validation failed: %w", err)
	}
	if err := ProcessTimestamps(m); err != nil {
		return nil, fmt.Errorf("timestamp processing failed: %w", err)
	}
	return decodeMapToRun(m)
}

func ParseValidatePatchMap(m map[string]interface{}) (*Run, error) {
	if err := ValidatePatchSchema(m); err != nil {
		return nil, fmt.Errorf("schema validation failed: %w", err)
	}
	if err := ProcessTimestamps(m); err != nil {
		return nil, fmt.Errorf("timestamp processing failed: %w", err)
	}
	return decodeMapToRun(m)
}

func ParseValidatePostOrderedMap(om *orderedmap.OrderedMap[string, interface{}]) (*Run, error) {
	return ParseValidatePostMap(orderedMapToMap(om))
}

func ParseValidatePatchOrderedMap(om *orderedmap.OrderedMap[string, interface{}]) (*Run, error) {
	return ParseValidatePatchMap(orderedMapToMap(om))
}

func orderedMapToMap(om *orderedmap.OrderedMap[string, interface{}]) map[string]interface{} {
	if om == nil {
		return nil
	}
	dst := make(map[string]interface{}, om.Len())
	for el := om.Oldest(); el != nil; el = el.Next() {
		dst[el.Key] = el.Value
	}
	return dst
}

func decodeMapToRun(m map[string]interface{}) (*Run, error) {
	var run Run

	decoderConfig := &mapstructure.DecoderConfig{
		Metadata:         nil,
		Result:           &run,
		TagName:          "json",
		WeaklyTypedInput: true,
		ErrorUnused:      false,
	}

	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create mapstructure decoder: %w", err)
	}

	if err := decoder.Decode(m); err != nil {
		return nil, fmt.Errorf("map decode error: %w", err)
	}

	return &run, nil
}

func decodeToRun(data map[string]interface{}) (*Run, error) {
	var run Run

	decoderConfig := &mapstructure.DecoderConfig{
		Result:           &run,
		TagName:          "json",
		WeaklyTypedInput: true,
		ErrorUnused:      false,
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create decoder: %w", err)
	}

	if err := decoder.Decode(data); err != nil {
		return nil, fmt.Errorf("failed to decode into Run struct: %w", err)
	}

	return &run, nil
}

func parseAndValidate(
	raw []byte,
	validateSchemaFn func(map[string]interface{}) error,
) (*Run, error) {

	var data map[string]interface{}
	if err := json.Unmarshal(raw, &data); err != nil {
		return nil, fmt.Errorf("invalid JSON: %w", err)
	}

	if err := validateSchemaFn(data); err != nil {
		return nil, fmt.Errorf("schema validation failed: %w", err)
	}

	if err := ProcessTimestamps(data); err != nil {
		return nil, fmt.Errorf("timestamp processing failed: %w", err)
	}

	run, err := decodeToRun(data)
	if err != nil {
		return nil, err
	}

	return run, nil
}

var (
	compiledPostSchema  *gojsonschema.Schema
	compiledPatchSchema *gojsonschema.Schema
	compiledBatchSchema *gojsonschema.Schema
	schemaOnce          sync.Once
)

// Initialize schemas once
func initSchemas() {
	schemaOnce.Do(func() {
		postLoader := gojsonschema.NewStringLoader(schemaPostStr)
		patchLoader := gojsonschema.NewStringLoader(schemaPatchStr)
		batchLoader := gojsonschema.NewStringLoader(schemaBatchStr)

		var err error
		compiledPostSchema, err = gojsonschema.NewSchema(postLoader)
		if err != nil {
			panic(fmt.Sprintf("failed to compile post schema: %v", err))
		}

		compiledPatchSchema, err = gojsonschema.NewSchema(patchLoader)
		if err != nil {
			panic(fmt.Sprintf("failed to compile patch schema: %v", err))
		}

		compiledBatchSchema, err = gojsonschema.NewSchema(batchLoader)
		if err != nil {
			panic(fmt.Sprintf("failed to compile batch schema: %v", err))
		}
	})
}

func ValidatePostSchema(data map[string]interface{}) error {
	initSchemas()

	documentLoader := gojsonschema.NewGoLoader(data)
	result, err := compiledPostSchema.Validate(documentLoader)
	if err != nil {
		return fmt.Errorf("schema validation error: %w", err)
	}

	if !result.Valid() {
		errs := result.Errors()
		if len(errs) > 0 {
			return fmt.Errorf("%s: %s", errs[0].Field(), errs[0].Description())
		}
	}

	return nil
}

func ValidatePatchSchema(data map[string]interface{}) error {
	initSchemas()

	documentLoader := gojsonschema.NewGoLoader(data)
	result, err := compiledPatchSchema.Validate(documentLoader)
	if err != nil {
		return fmt.Errorf("schema validation error: %w", err)
	}

	if !result.Valid() {
		errs := result.Errors()
		if len(errs) > 0 {
			return fmt.Errorf("%s: %s", errs[0].Field(), errs[0].Description())
		}
	}

	return nil
}

func ValidateBatchSchema(data map[string]interface{}) error {
	initSchemas()

	documentLoader := gojsonschema.NewGoLoader(data)
	result, err := compiledBatchSchema.Validate(documentLoader)
	if err != nil {
		return fmt.Errorf("schema validation error: %w", err)
	}
	if !result.Valid() {
		if errs := result.Errors(); len(errs) > 0 {
			return fmt.Errorf("batch schema error %s: %s", errs[0].Field(), errs[0].Description())
		}
	}
	return nil
}

func ProcessTimestamps(data map[string]interface{}) error {
	timeFields := []string{"start_time", "end_time"}

	for _, field := range timeFields {
		if ts, exists := data[field]; exists {
			isoTime, err := NormalizeTimestamp(ts)
			if err != nil {
				return fmt.Errorf("%s: %w", field, err)
			}
			if isoTime != "" {
				data[field] = isoTime
			}
		}
	}

	return nil
}

func NormalizeTimestamp(ts interface{}) (string, error) {
	if ts == nil {
		return "", nil
	}

	var t time.Time
	var err error

	switch v := ts.(type) {
	case string:
		t, err = util.ParseFlexibleTimestamp(v)
		if err != nil {
			return "", fmt.Errorf("invalid timestamp format")
		}
	case float64:
		t, err = parseNumericTimestampFloat64(v)
		if err != nil {
			return "", fmt.Errorf("invalid numeric timestamp")
		}
	case int64:
		t, err = parseNumericTimestampInt64(v)
		if err != nil {
			return "", fmt.Errorf("invalid numeric timestamp")
		}
	case int:
		t, err = parseNumericTimestampInt64(int64(v))
		if err != nil {
			return "", fmt.Errorf("invalid numeric timestamp")
		}
	default:
		return "", fmt.Errorf("unsupported timestamp type: %T", ts)
	}

	return t.UTC().Format(time.RFC3339Nano), nil
}

func parseNumericTimestampFloat64(v float64) (time.Time, error) {
	if v > 1e11 {
		return time.UnixMilli(int64(v)), nil
	}

	sec := int64(v)
	nsec := int64((v - float64(sec)) * 1e9)
	return time.Unix(sec, nsec), nil
}

func parseNumericTimestampInt64(v int64) (time.Time, error) {
	if v > 1e11 {
		return time.UnixMilli(v), nil
	}
	return time.Unix(v, 0), nil
}
