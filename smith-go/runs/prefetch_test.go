package runs_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/DataDog/zstd"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/runs"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

func TestPrefetchHandler(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer testutil.DbCleanup(t, dbpool)
	queueingRedisPool := lsredis.SingleRedisConnect()
	defer func(redis redis.UniversalClient) {
		err := redis.Close()
		if err != nil {
			fmt.Printf("Error closing redis connections: %v\n", err)
		}
	}(queueingRedisPool)
	var cachingRedisPool redis.UniversalClient
	if config.Env.RedisCachingDatabaseURI != "" {
		cachingRedisPool = lsredis.CachingRedisConnect()
		defer func(redis redis.UniversalClient) {
			err := redis.Close()
			if err != nil {
				fmt.Printf("Error closing redis connections: %v\n", err)
			}
		}(cachingRedisPool)

	} else {
		cachingRedisPool = queueingRedisPool
	}

	// initialize clickhouse client
	chConn, chError := database.ChConnect(true)
	if chError != nil {
		panic(chError)
	}
	defer func(ch clickhouse.Conn) {
		err := ch.Close()
		if err != nil {
			fmt.Printf("Error closing clickhouse connection: %v\n", err)
		}
	}(chConn)

	h := runs.NewRunPrefetchHandler(cachingRedisPool, &chConn, nil)

	t.Run("fail without tenant id in tenants without feature enabled", func(t *testing.T) {
		w := httptest.NewRecorder()
		b := strings.NewReader(`{"key": "value"}`)
		r := httptest.NewRequest("POST", "/prefetch", b)

		// Create AuthInfo and set it in the context
		authInfo := &auth.AuthInfo{
			TenantID:            "00000000-0000-0000-0000-000000000002",
			OrganizationID:      "test-org-id",
			UserID:              "test-user-id",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		ctx := context.WithValue(r.Context(), auth.AuthCtxKey, authInfo)
		r = r.WithContext(ctx)

		h.HandlePrefetchRuns(w, r)
		require.Equal(t, http.StatusForbidden, w.Code, w.Body.String())
	})

	t.Run("fail without session id in body", func(t *testing.T) {
		config.Env.FFTracePrefetchEnabledTenantIds = config.List{SplitList: []string{"00000000-0000-0000-0000-000000000002"}}
		w := httptest.NewRecorder()
		b := strings.NewReader(`{"tenantIds": "[479364c8-1b9e-4188-a678-547f5027eb76, 173757c8-6ecb-4735-a5ae-0b3a646bfc6d]"}`)
		r := httptest.NewRequest("POST", "/prefetch", b)

		// Create AuthInfo and set it in the context
		authInfo := &auth.AuthInfo{
			TenantID:            "00000000-0000-0000-0000-000000000002",
			OrganizationID:      "test-org-id",
			UserID:              "test-user-id",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		ctx := context.WithValue(r.Context(), auth.AuthCtxKey, authInfo)
		r = r.WithContext(ctx)

		h.HandlePrefetchRuns(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})

	t.Run("fail without tenant ids in body", func(t *testing.T) {
		config.Env.FFTracePrefetchEnabledTenantIds = config.List{SplitList: []string{"00000000-0000-0000-0000-000000000002"}}
		w := httptest.NewRecorder()
		b := strings.NewReader(`{"sessionId": "479364c8-1b9e-4188-a678-547f5027eb76"}`)
		r := httptest.NewRequest("POST", "/prefetch", b)

		// Create AuthInfo and set it in the context
		authInfo := &auth.AuthInfo{
			TenantID:            "00000000-0000-0000-0000-000000000002",
			OrganizationID:      "test-org-id",
			UserID:              "test-user-id",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		ctx := context.WithValue(r.Context(), auth.AuthCtxKey, authInfo)
		r = r.WithContext(ctx)

		h.HandlePrefetchRuns(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})

	t.Run("fail without tenant ids in body", func(t *testing.T) {
		config.Env.FFTracePrefetchEnabledTenantIds = config.List{SplitList: []string{"00000000-0000-0000-0000-000000000002"}}
		w := httptest.NewRecorder()
		b := strings.NewReader(`{"sessionId": "479364c8-1b9e-4188-a678-547f5027eb76"}`)
		r := httptest.NewRequest("POST", "/prefetch", b)

		// Create AuthInfo and set it in the context
		authInfo := &auth.AuthInfo{
			TenantID:            "00000000-0000-0000-0000-000000000002",
			OrganizationID:      "test-org-id",
			UserID:              "test-user-id",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		ctx := context.WithValue(r.Context(), auth.AuthCtxKey, authInfo)
		r = r.WithContext(ctx)

		h.HandlePrefetchRuns(w, r)
		require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
	})

	t.Run("prefetch success and verify", func(t *testing.T) {
		now := time.Now().UTC()
		// set up the session for the tenant by inserting into ch
		rootRun := testutil.InsertRun{
			Run: runs.Run{
				ID:          util.StringPtr("00000000-0000-4000-8000-000000000001"),
				Name:        util.StringPtr("test-run-1"),
				RunType:     util.StringPtr("chain"),
				StartTime:   util.StringPtr("2025-02-03T06:28:25.000000"),
				EndTime:     util.StringPtr("2025-02-03T06:28:38.000000"),
				Inputs:      map[string]interface{}{"input": "Who was the king of France?"},
				Outputs:     map[string]interface{}{"output": "Louis XIV"},
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr("00000000-0000-4000-8000-000000000001"), // same as parent run id
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr("00000000-0000-4000-8000-000000000001"),
				DottedOrder: util.StringPtr("20250203T062825000000Z00000000-0000-4000-8000-000000000001"),
				SessionID:   util.StringPtr("00000000-0000-4000-8000-000000000015"),
			},
			IsRoot:               true,
			PromptTokens:         0,
			CompletionTokens:     0,
			TotalTokens:          0,
			FirstTokenTime:       util.StringPtr(now.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(now.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0,
			CompletionCost:       0,
			TotalCost:            0,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            0,
			OutputSize:           0,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr("2025-02-03T06:28:25.000000"),
		}

		err := testutil.InsertRunIntoClickhouse(t, chConn, "00000000-0000-4000-8000-000000000000", "00000000-0000-4000-8000-000000000015", rootRun)
		require.NoError(t, err)

		// insert a child run into ch
		childRun := testutil.InsertRun{
			Run: runs.Run{
				ID:        util.StringPtr("00000000-0000-4000-8000-000000000002"),
				Name:      util.StringPtr("test-run-1"),
				RunType:   util.StringPtr("llm"),
				StartTime: util.StringPtr("2025-02-03T06:28:38.000000"),
				EndTime:   util.StringPtr("2025-02-03T06:28:42.000000"),
				Inputs:    nil,

				Outputs:     nil,
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr("00000000-0000-4000-8000-000000000001"), // same as parent run id
				Events:      []map[string]interface{}{{"event": "test-event"}},
				Tags:        []string{"test-tag"},
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr("00000000-0000-4000-8000-000000000001"),
				DottedOrder: util.StringPtr("20250203T062825000000Z00000000-0000-4000-8000-000000000001.20250203T062838000000Z00000000-0000-4000-8000-000000000002"),
				SessionID:   util.StringPtr("00000000-0000-4000-8000-000000000015"),
			},
			IsRoot:               false,
			PromptTokens:         100,
			CompletionTokens:     100,
			TotalTokens:          200,
			FirstTokenTime:       util.StringPtr(now.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(now.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0.1,
			CompletionCost:       0.1,
			TotalCost:            0.2,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            100,
			OutputSize:           100,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr("2025-02-03T06:28:25.000000"),
		}

		err = testutil.InsertRunIntoClickhouse(t, chConn, "00000000-0000-4000-8000-000000000000", "00000000-0000-4000-8000-000000000015", childRun)
		require.NoError(t, err)

		// run the prefetch
		config.Env.FFTracePrefetchEnabledTenantIds = config.List{SplitList: []string{"00000000-0000-4000-8000-000000000000"}}
		w := httptest.NewRecorder()
		b := strings.NewReader(`{"trace_ids": ["00000000-0000-4000-8000-000000000001"], "session_id": "00000000-0000-4000-8000-000000000015"}`)
		r := httptest.NewRequest("POST", "/prefetch", b)
		authInfo := &auth.AuthInfo{
			TenantID:            "00000000-0000-4000-8000-000000000000",
			OrganizationID:      "00000000-0000-4000-8000-000000000000",
			UserID:              "00000000-0000-4000-8000-000000000000",
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		ctx := context.WithValue(r.Context(), auth.AuthCtxKey, authInfo)
		r = r.WithContext(ctx)
		h.HandlePrefetchRuns(w, r)
		require.Equal(t, http.StatusCreated, w.Code, w.Body.String())

		// check that the redis cache has the right data for each trace
		payloadVal, err := cachingRedisPool.Get(context.Background(), "smith:cache:traces_prefetch:00000000-0000-4000-8000-000000000000:00000000-0000-4000-8000-000000000015:00000000-0000-4000-8000-000000000001").Result()
		require.NotEqual(t, err, redis.Nil)

		decompressedData := []byte{}
		decompressedData, err = zstd.Decompress(decompressedData, []byte(payloadVal))
		require.NoError(t, err)
		var runs []*runs.PrefetchedRun
		err = json.Unmarshal(decompressedData, &runs)
		require.NoError(t, err)
		// ensure that only the non root run is prefetched
		require.Equal(t, 1, len(runs))
		require.Equal(t, *childRun.Run.ID, runs[0].Id)
		require.Equal(t, *childRun.Run.Name, runs[0].Name)
		require.Equal(t, *childRun.Run.RunType, runs[0].RunType)
		require.Equal(t, *childRun.Run.StartTime, runs[0].StartTime)
		require.Equal(t, *childRun.Run.EndTime, runs[0].EndTime)
		require.Equal(t, childRun.Run.Extra, runs[0].Extra)
		require.Nil(t, childRun.Run.Error)
		require.Equal(t, *childRun.Run.ParentRunID, runs[0].ParentRunID)
		require.Equal(t, childRun.Run.Tags, runs[0].Tags)
		require.Equal(t, *childRun.Run.Status, runs[0].Status)
		require.Equal(t, *childRun.Run.TraceID, runs[0].TraceID)
		require.Equal(t, *childRun.Run.DottedOrder, runs[0].DottedOrder)
		require.Equal(t, *childRun.Run.SessionID, runs[0].SessionID)
		require.Equal(t, childRun.PromptTokens, int(runs[0].PrefetchedRunTokenStats.PromptTokens))
		require.Equal(t, childRun.CompletionTokens, int(runs[0].PrefetchedRunTokenStats.CompletionTokens))
		require.Equal(t, childRun.TotalTokens, int(runs[0].PrefetchedRunTokenStats.TotalTokens))
		require.Equal(t, childRun.PromptCost, *runs[0].PrefetchedRunTokenStats.PromptCost)
		require.Equal(t, childRun.CompletionCost, *runs[0].PrefetchedRunTokenStats.CompletionCost)
		require.Equal(t, childRun.TotalCost, *runs[0].PrefetchedRunTokenStats.TotalCost)
	})
}
