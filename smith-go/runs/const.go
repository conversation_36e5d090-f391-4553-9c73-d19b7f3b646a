package runs

const schemaPostStr = `
{
  "required": [
    "name",
    "run_type"
  ],
  "type": "object",
  "properties": {
    "name": {
      "type": "string"
    },
    "inputs": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "run_type": {
      "type": "string",
      "enum": [
        "tool",
        "chain",
        "llm",
        "retriever",
        "embedding",
        "prompt",
        "parser"
      ]
    },
    "start_time": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "number"
        },
        {
          "type": "null"
        }
      ]
    },
    "end_time": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "number"
        },
        {
          "type": "null"
        }
      ]
    },
    "extra": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "error": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "null"
        }
      ]
    },
    "serialized": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "outputs": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "parent_run_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "events": {
      "oneOf": [
        {
          "type": "array",
          "items": {
            "type": "object"
          }
        },
        {
          "type": "null"
        }
      ]
    },
    "tags": {
      "oneOf": [
        {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        {
          "type": "null"
        }
      ]
    },
    "trace_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "dotted_order": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "null"
        }
      ]
    },
    "id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "session_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "session_name": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "null"
        }
      ]
    },
    "reference_example_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "input_attachments": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "output_attachments": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    }
  }
}
`

const schemaPatchStr = `
{
  "type": "object",
  "properties": {
    "trace_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "dotted_order": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "null"
        }
      ]
    },
    "parent_run_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "end_time": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "number"
        },
        {
          "type": "null"
        }
      ]
    },
    "error": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "null"
        }
      ]
    },
    "inputs": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "outputs": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "events": {
      "oneOf": [
        {
          "type": "array",
          "items": {
            "type": "object"
          }
        },
        {
          "type": "null"
        }
      ]
    },
    "tags": {
      "oneOf": [
        {
          "type": "array",
          "items": {
            "type": "string"
          }
        },
        {
          "type": "null"
        }
      ]
    },
    "extra": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "input_attachments": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "output_attachments": {
      "oneOf": [
        {
          "type": "object"
        },
        {
          "type": "null"
        }
      ]
    },
    "session_id": {
      "oneOf": [
        {
          "type": "string",
          "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
        },
        {
          "type": "null"
        }
      ]
    },
    "session_name": {
      "oneOf": [
        {
          "type": "string"
        },
        {
          "type": "null"
        }
      ]
    }
  }
}
`

const schemaBatchStr = `
{
  "type": "object",
  "properties": {
    "post": {
      "type": "array",
      "items": {
        "type": "object",
        "required": [
          "id",
          "trace_id",
          "dotted_order",
          "start_time",
          "name",
          "run_type"
        ],
        "properties": {
          "name": {
            "type": "string"
          },
          "inputs": {
            "type": "object"
          },
          "run_type": {
            "type": "string",
            "enum": [
              "tool",
              "chain",
              "llm",
              "retriever",
              "embedding",
              "prompt",
              "parser"
            ]
          },
          "start_time": {
            "oneOf": [
              {
                "type": "string"
              },
              {
                "type": "number"
              }
            ]
          },
          "end_time": {
            "oneOf": [
              {
                "type": "string"
              },
              {
                "type": "number"
              },
              {
                "type": "null"
              }
            ]
          },
          "extra": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "error": {
            "oneOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ]
          },
          "serialized": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "outputs": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "parent_run_id": {
            "oneOf": [
              {
                "type": "string",
                "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
              },
              {
                "type": "null"
              }
            ]
          },
          "events": {
            "oneOf": [
              {
                "type": "array",
                "items": {
                  "type": "object"
                }
              },
              {
                "type": "null"
              }
            ]
          },
          "tags": {
            "oneOf": [
              {
                "type": "array",
                "items": {
                  "type": "string"
                }
              },
              {
                "type": "null"
              }
            ]
          },
          "trace_id": {
            "type": "string",
            "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
          },
          "dotted_order": {
            "type": "string"
          },
          "id": {
            "type": "string",
            "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
          },
          "session_id": {
            "oneOf": [
              {
                "type": "string",
                "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
              },
              {
                "type": "null"
              }
            ]
          },
          "session_name": {
            "oneOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ]
          },
          "reference_example_id": {
            "oneOf": [
              {
                "type": "string",
                "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
              },
              {
                "type": "null"
              }
            ]
          },
          "input_attachments": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "output_attachments": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          }
        }
      }
    },
    "patch": {
      "type": "array",
      "items": {
        "required": [
          "id",
          "trace_id",
          "dotted_order",
          "end_time"
        ],
        "type": "object",
        "properties": {
          "trace_id": {
            "type": "string",
            "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
          },
          "dotted_order": {
            "type": "string"
          },
          "parent_run_id": {
            "oneOf": [
              {
                "type": "string",
                "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
              },
              {
                "type": "null"
              }
            ]
          },
          "end_time": {
            "oneOf": [
              {
                "type": "string"
              },
              {
                "type": "number"
              }
            ]
          },
          "error": {
            "oneOf": [
              {
                "type": "string"
              },
              {
                "type": "null"
              }
            ]
          },
          "inputs": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "outputs": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "events": {
            "oneOf": [
              {
                "type": "array",
                "items": {
                  "type": "object"
                }
              },
              {
                "type": "null"
              }
            ]
          },
          "tags": {
            "oneOf": [
              {
                "type": "array",
                "items": {
                  "type": "string"
                }
              },
              {
                "type": "null"
              }
            ]
          },
          "extra": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "input_attachments": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "output_attachments": {
            "oneOf": [
              {
                "type": "object"
              },
              {
                "type": "null"
              }
            ]
          },
          "id": {
            "type": "string",
            "pattern": "^[0-9a-f]{32}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
          }
        }
      }
    }
  },
  "description": "Schema for a batch of runs to be ingested."
}
`
