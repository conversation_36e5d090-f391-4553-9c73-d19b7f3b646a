package runs_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/auth"
	"langchain.com/smith/database"
	"langchain.com/smith/runs"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/util"
)

func SetupRequestWithParams(
	t *testing.T,
	authInfo *auth.AuthInfo,
	method string,
	endpoint string,
	body *strings.Reader,
	traceID string,
	sessionID string,
	startTime string,
	endTime string,
) *http.Request {
	rctx := chi.NewRouteContext()
	fmt.Println("traceID", traceID)
	rctx.URLParams.Add("trace_id", traceID)

	r := httptest.NewRequest(method, endpoint, body)

	query := r.URL.Query()
	query.Add("session_id", sessionID)
	query.Add("start_time", startTime)
	query.Add("end_time", endTime)
	r.URL.RawQuery = query.Encode()

	ctx := r.Context()
	ctx = context.WithValue(ctx, chi.RouteCtxKey, rctx)
	ctx = context.WithValue(ctx, auth.AuthCtxKey, authInfo)
	r = r.WithContext(ctx)

	return r
}

func TestQueryHandler(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// initialize clickhouse client
	chConn, chError := database.ChConnect(true)
	if chError != nil {
		panic(chError)
	}
	defer func(ch clickhouse.Conn) {
		err := ch.Close()
		if err != nil {
			fmt.Printf("Error closing clickhouse connection: %v\n", err)
		}
	}(chConn)

	h := runs.NewRunQueryHandler(&chConn)

	t.Run("query success and verify", func(t *testing.T) {
		now := time.Now().UTC()
		// set up the trace in clickhouse
		userId := uuid.New().String()
		organizationId := uuid.New().String()
		tenantId := uuid.New().String()
		sessionId := uuid.New().String()

		rootRunId := uuid.New().String()
		rootRunStartTime := now.UTC().Add(-time.Second * 10)
		rootRunEndTime := rootRunStartTime.Add(time.Second * 3)

		rootRun := testutil.InsertRun{
			Run: runs.Run{
				ID:          util.StringPtr(rootRunId),
				Name:        util.StringPtr("test-run-1"),
				RunType:     util.StringPtr("chain"),
				StartTime:   util.StringPtr(rootRunStartTime.Format("2006-01-02T15:04:05.000000")),
				EndTime:     util.StringPtr(rootRunEndTime.Format("2006-01-02T15:04:05.000000")),
				Inputs:      map[string]interface{}{"input": "Who was the king of France?"},
				Outputs:     map[string]interface{}{"output": "Louis XIV"},
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr(rootRunId), // same as parent run id
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr(rootRunId),
				DottedOrder: util.StringPtr(fmt.Sprintf("%s000000Z%s", rootRunStartTime.Format("20060102T150405"), rootRunId)),
				SessionID:   util.StringPtr(sessionId),
			},
			IsRoot:               true,
			PromptTokens:         0,
			CompletionTokens:     0,
			TotalTokens:          0,
			FirstTokenTime:       util.StringPtr(rootRunStartTime.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(rootRunEndTime.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0,
			CompletionCost:       0,
			TotalCost:            0,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            0,
			OutputSize:           0,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr(rootRunStartTime.Format("2006-01-02T15:04:05.000000")),
		}

		err := testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, rootRun)
		require.NoError(t, err)

		// insert a child run into ch
		childRunId := uuid.New().String()
		childRunStartTime := rootRunStartTime.Add(25 * time.Millisecond)
		childRunEndTime := childRunStartTime.Add(time.Second * 1)

		childRun := testutil.InsertRun{
			Run: runs.Run{
				ID:        util.StringPtr(childRunId),
				Name:      util.StringPtr("test-child-run-1"),
				RunType:   util.StringPtr("llm"),
				StartTime: util.StringPtr(childRunStartTime.Format("2006-01-02T15:04:05.000000")),
				EndTime:   util.StringPtr(childRunEndTime.Format("2006-01-02T15:04:05.000000")),
				Inputs:    nil,

				Outputs:     nil,
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr(rootRunId), // same as parent run id
				Events:      []map[string]interface{}{{"event": "test-event"}},
				Tags:        []string{"test-tag"},
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr(rootRunId),
				DottedOrder: util.StringPtr(fmt.Sprintf("%s000000Z%s", childRunStartTime.Format("20060102T150405"), childRunId)),
				SessionID:   util.StringPtr(sessionId),
			},
			IsRoot:               false,
			PromptTokens:         100,
			CompletionTokens:     100,
			TotalTokens:          200,
			FirstTokenTime:       util.StringPtr(childRunStartTime.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(childRunEndTime.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0.1,
			CompletionCost:       0.1,
			TotalCost:            0.2,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            100,
			OutputSize:           100,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr(childRunStartTime.Format("2006-01-02T15:04:05.000000")),
		}

		err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, childRun)
		require.NoError(t, err)

		// run the query
		w := httptest.NewRecorder()
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      organizationId,
			UserID:              userId,
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		r := SetupRequestWithParams(
			t,
			authInfo,
			"GET",
			fmt.Sprintf(
				"/traces/%s/runs?session_id=%s&start_time=%s&end_time=%s",
				rootRunId,
				sessionId,
				rootRunStartTime.Add(-time.Second*10).Format("2006-01-02T15:04:05.000000Z"),
				rootRunEndTime.Add(time.Second*10).Format("2006-01-02T15:04:05.000000Z"),
			),
			strings.NewReader(""),
			rootRunId,
			sessionId,
			rootRunStartTime.Add(-time.Second*10).Format("2006-01-02T15:04:05.000000Z"),
			rootRunEndTime.Add(time.Second*10).Format("2006-01-02T15:04:05.000000Z"),
		)
		h.SingleTraceQuery(w, r)

		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		var queryResponse runs.TraceQueryResponse
		err = json.Unmarshal(w.Body.Bytes(), &queryResponse)
		require.NoError(t, err)

		// verify the runs are returned
		require.Equal(t, 1, len(queryResponse.QueriedRuns))
	})

	t.Run("query multiple runs", func(t *testing.T) {
		now := time.Now().UTC()
		// set up the trace in clickhouse
		userId := uuid.New().String()
		organizationId := uuid.New().String()
		tenantId := uuid.New().String()
		sessionId := uuid.New().String()

		rootRunId := uuid.New().String()
		rootRunStartTime := now.UTC().Add(-time.Second * 10)
		rootRunEndTime := rootRunStartTime.Add(time.Second * 3)

		rootRun := testutil.InsertRun{
			Run: runs.Run{
				ID:          util.StringPtr(rootRunId),
				Name:        util.StringPtr("test-run-1"),
				RunType:     util.StringPtr("chain"),
				StartTime:   util.StringPtr(rootRunStartTime.Format("2006-01-02T15:04:05.000000")),
				EndTime:     util.StringPtr(rootRunEndTime.Format("2006-01-02T15:04:05.000000")),
				Inputs:      map[string]interface{}{"input": "Who was the king of France?"},
				Outputs:     map[string]interface{}{"output": "Louis XIV"},
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr(rootRunId), // same as parent run id
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr(rootRunId),
				DottedOrder: util.StringPtr(fmt.Sprintf("%s000000Z%s", rootRunStartTime.Format("20060102T150405"), rootRunId)),
				SessionID:   util.StringPtr(sessionId),
			},
			IsRoot:               true,
			PromptTokens:         0,
			CompletionTokens:     0,
			TotalTokens:          0,
			FirstTokenTime:       util.StringPtr(rootRunStartTime.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(rootRunEndTime.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0,
			CompletionCost:       0,
			TotalCost:            0,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            0,
			OutputSize:           0,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr(rootRunStartTime.Format("2006-01-02T15:04:05.000000")),
		}

		err := testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, rootRun)
		require.NoError(t, err)

		childRunId := uuid.New().String()
		childRunStartTime := rootRunStartTime.Add(25 * time.Millisecond)
		childRunEndTime := childRunStartTime.Add(time.Second * 1)

		childRun := testutil.InsertRun{
			Run: runs.Run{
				ID:          util.StringPtr(childRunId),
				Name:        util.StringPtr("test-child-run-1"),
				RunType:     util.StringPtr("llm"),
				StartTime:   util.StringPtr(childRunStartTime.Format("2006-01-02T15:04:05.000000")),
				EndTime:     util.StringPtr(childRunEndTime.Format("2006-01-02T15:04:05.000000")),
				Inputs:      nil,
				Outputs:     nil,
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr(rootRunId), // same as parent run id
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr(rootRunId),
				DottedOrder: util.StringPtr(fmt.Sprintf(
					"%s000000Z%s.%s000000Z%s",
					rootRunStartTime.Format("20060102T150405"),
					rootRunId,
					childRunStartTime.Format("20060102T150405"),
					childRunId,
				)),
				SessionID: util.StringPtr(sessionId),
			},
			IsRoot:               false,
			PromptTokens:         100,
			CompletionTokens:     100,
			TotalTokens:          200,
			FirstTokenTime:       util.StringPtr(childRunStartTime.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(childRunEndTime.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0.1,
			CompletionCost:       0.1,
			TotalCost:            0.2,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            100,
			OutputSize:           100,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr(childRunStartTime.Format("2006-01-02T15:04:05.000000")),
		}

		err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, childRun)
		require.NoError(t, err)

		childRun2Id := uuid.New().String()
		childRun2StartTime := rootRunStartTime.Add(50 * time.Millisecond)
		childRun2EndTime := childRun2StartTime.Add(time.Second * 1)

		childRun2 := testutil.InsertRun{
			Run: runs.Run{
				ID:          util.StringPtr(childRun2Id),
				Name:        util.StringPtr("test-child-run-2"),
				RunType:     util.StringPtr("llm"),
				StartTime:   util.StringPtr(childRun2StartTime.Format("2006-01-02T15:04:05.000000")),
				EndTime:     util.StringPtr(childRun2EndTime.Format("2006-01-02T15:04:05.000000")),
				Inputs:      nil,
				Outputs:     nil,
				Extra:       map[string]interface{}{"extra": "test-extra"},
				Error:       nil,
				ParentRunID: util.StringPtr(rootRunId), // same as parent run id
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr(rootRunId),
				DottedOrder: util.StringPtr(fmt.Sprintf(
					"%s000000Z%s.%s000000Z%s",
					rootRunStartTime.Format("20060102T150405"),
					rootRunId,
					childRun2StartTime.Format("20060102T150405"),
					childRun2Id,
				)),
				SessionID: util.StringPtr(sessionId),
			},
			IsRoot:               false,
			PromptTokens:         100,
			CompletionTokens:     100,
			TotalTokens:          200,
			FirstTokenTime:       util.StringPtr(childRun2StartTime.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(childRun2EndTime.Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0.1,
			CompletionCost:       0.1,
			TotalCost:            0.2,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            100,
			OutputSize:           100,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr(childRun2StartTime.Format("2006-01-02T15:04:05.000000")),
		}

		err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, childRun2)
		require.NoError(t, err)

		// run the query
		w := httptest.NewRecorder()
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      organizationId,
			UserID:              userId,
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		r := SetupRequestWithParams(
			t,
			authInfo,
			"GET",
			fmt.Sprintf(
				"/traces/%s/runs?session_id=%s&start_time=%s&end_time=%s",
				rootRunId,
				sessionId,
				rootRunStartTime.Add(-time.Second*10).Format("2006-01-02T15:04:05.000000Z"),
				rootRunEndTime.Add(time.Second*10).Format("2006-01-02T15:04:05.000000Z"),
			),
			strings.NewReader(""),
			rootRunId,
			sessionId,
			rootRunStartTime.Format("2006-01-02T15:04:05.000000Z"),
			rootRunEndTime.Format("2006-01-02T15:04:05.000000Z"),
		)
		h.SingleTraceQuery(w, r)

		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		var queryResponse runs.TraceQueryResponse
		err = json.Unmarshal(w.Body.Bytes(), &queryResponse)
		require.NoError(t, err)

		// verify the runs are returned
		require.Equal(t, 2, len(queryResponse.QueriedRuns))
	})
}

func TestRunTreeConstruction(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// initialize clickhouse client
	chConn, chError := database.ChConnect(true)
	if chError != nil {
		panic(chError)
	}
	defer func(ch clickhouse.Conn) {
		err := ch.Close()
		if err != nil {
			fmt.Printf("Error closing clickhouse connection: %v\n", err)
		}
	}(chConn)

	h := runs.NewRunQueryHandler(&chConn)

	t.Run("tree construction test", func(t *testing.T) {
		// Create IDs for the user, organization, tenant, and session
		userId := uuid.New().String()
		organizationId := uuid.New().String()
		tenantId := uuid.New().String()
		sessionId := uuid.New().String()

		// Create a base timestamp
		baseTime, err := time.Parse("2006-01-02T15:04:05.000000", "2023-05-05T05:13:24.571809")
		require.NoError(t, err)

		// Generate UUIDs for all nodes
		rootId := uuid.New().String()

		// Create timestamps for each level
		rootTimestamp := baseTime.Format("20060102T150405")
		childTimestamp := baseTime.Add(1 * time.Second).Format("20060102T150405")
		grandchildTimestamp := baseTime.Add(2 * time.Second).Format("20060102T150405")
		greatGrandchildTimestamp := baseTime.Add(3 * time.Second).Format("20060102T150405")

		// Build child IDs (10 children)
		childIds := make([]string, 10)
		for i := range childIds {
			childIds[i] = uuid.New().String()
		}

		// Build grandchild IDs (10 per child = 100 total)
		grandchildIds := make([][]string, 10)
		for i := range grandchildIds {
			grandchildIds[i] = make([]string, 10)
			for j := range grandchildIds[i] {
				grandchildIds[i][j] = uuid.New().String()
			}
		}

		// Build great-grandchild IDs (2 per grandchild = 200 total)
		greatGrandchildIds := make([][][]string, 10)
		for i := range greatGrandchildIds {
			greatGrandchildIds[i] = make([][]string, 10)
			for j := range greatGrandchildIds[i] {
				greatGrandchildIds[i][j] = make([]string, 2)
				for k := range greatGrandchildIds[i][j] {
					greatGrandchildIds[i][j][k] = uuid.New().String()
				}
			}
		}

		// Insert the root run
		rootRun := testutil.InsertRun{
			Run: runs.Run{
				ID:          util.StringPtr(rootId),
				Name:        util.StringPtr("Root"),
				RunType:     util.StringPtr("chain"),
				StartTime:   util.StringPtr(baseTime.Format("2006-01-02T15:04:05.000000")),
				EndTime:     util.StringPtr(baseTime.Add(time.Second * 8).Format("2006-01-02T15:04:05.000000")),
				Inputs:      map[string]interface{}{"input": "test input"},
				Outputs:     map[string]interface{}{"output": "test output"},
				Extra:       map[string]interface{}{"metadata": map[string]interface{}{"foo": "bar"}},
				Error:       nil,
				ParentRunID: nil,
				Status:      util.StringPtr("success"),
				TraceID:     util.StringPtr(rootId),
				DottedOrder: util.StringPtr(fmt.Sprintf("%s000000Z%s", rootTimestamp, rootId)),
				SessionID:   util.StringPtr(sessionId),
			},
			IsRoot:               true,
			PromptTokens:         0,
			CompletionTokens:     0,
			TotalTokens:          0,
			FirstTokenTime:       util.StringPtr(baseTime.Format("2006-01-02T15:04:05.000000")),
			ModifiedAt:           util.StringPtr(baseTime.Add(time.Second * 8).Format("2006-01-02T15:04:05.000000")),
			PromptCost:           0,
			CompletionCost:       0,
			TotalCost:            0,
			InputTokens:          0,
			OutputTokens:         0,
			InputSize:            0,
			OutputSize:           0,
			TraceTier:            util.StringPtr("longlived"),
			TraceUpgrade:         false,
			TraceFirstReceivedAt: util.StringPtr(baseTime.Format("2006-01-02T15:04:05.000000")),
		}

		err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, rootRun)
		require.NoError(t, err)

		// Insert child runs
		for i, childId := range childIds {
			childDottedOrder := fmt.Sprintf("%s000000Z%s.%s000000Z%s", rootTimestamp, rootId, childTimestamp, childId)
			childRun := testutil.InsertRun{
				Run: runs.Run{
					ID:          util.StringPtr(childId),
					Name:        util.StringPtr(fmt.Sprintf("Child_%d", i)),
					RunType:     util.StringPtr("chain"),
					StartTime:   util.StringPtr(baseTime.Add(time.Second).Format("2006-01-02T15:04:05.000000")),
					EndTime:     util.StringPtr(baseTime.Add(time.Second * 7).Format("2006-01-02T15:04:05.000000")),
					Inputs:      map[string]interface{}{"input": "test input"},
					Outputs:     map[string]interface{}{"output": "test output"},
					Extra:       map[string]interface{}{"metadata": map[string]interface{}{"foo": "bar"}},
					Error:       nil,
					ParentRunID: util.StringPtr(rootId),
					Status:      util.StringPtr("success"),
					TraceID:     util.StringPtr(rootId),
					DottedOrder: util.StringPtr(childDottedOrder),
					SessionID:   util.StringPtr(sessionId),
				},
				IsRoot:               false,
				PromptTokens:         0,
				CompletionTokens:     0,
				TotalTokens:          0,
				FirstTokenTime:       util.StringPtr(baseTime.Add(time.Second).Format("2006-01-02T15:04:05.000000")),
				ModifiedAt:           util.StringPtr(baseTime.Add(time.Second * 7).Format("2006-01-02T15:04:05.000000")),
				PromptCost:           0,
				CompletionCost:       0,
				TotalCost:            0,
				InputTokens:          0,
				OutputTokens:         0,
				InputSize:            0,
				OutputSize:           0,
				TraceTier:            util.StringPtr("longlived"),
				TraceUpgrade:         false,
				TraceFirstReceivedAt: util.StringPtr(baseTime.Add(time.Second).Format("2006-01-02T15:04:05.000000")),
			}

			err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, childRun)
			require.NoError(t, err)

			// Insert grandchild runs
			for j, grandchildId := range grandchildIds[i] {
				grandchildDottedOrder := fmt.Sprintf("%s.%s000000Z%s", childDottedOrder, grandchildTimestamp, grandchildId)
				grandchildRun := testutil.InsertRun{
					Run: runs.Run{
						ID:          util.StringPtr(grandchildId),
						Name:        util.StringPtr(fmt.Sprintf("Grandchild_%d_%d", i, j)),
						RunType:     util.StringPtr("chain"),
						StartTime:   util.StringPtr(baseTime.Add(time.Second * 2).Format("2006-01-02T15:04:05.000000")),
						EndTime:     util.StringPtr(baseTime.Add(time.Second * 6).Format("2006-01-02T15:04:05.000000")),
						Inputs:      map[string]interface{}{"input": "test input"},
						Outputs:     map[string]interface{}{"output": "test output"},
						Extra:       map[string]interface{}{"metadata": map[string]interface{}{"foo": "bar"}},
						Error:       nil,
						ParentRunID: util.StringPtr(childId),
						Status:      util.StringPtr("success"),
						TraceID:     util.StringPtr(rootId),
						DottedOrder: util.StringPtr(grandchildDottedOrder),
						SessionID:   util.StringPtr(sessionId),
					},
					IsRoot:               false,
					PromptTokens:         0,
					CompletionTokens:     0,
					TotalTokens:          0,
					FirstTokenTime:       util.StringPtr(baseTime.Add(time.Second * 2).Format("2006-01-02T15:04:05.000000")),
					ModifiedAt:           util.StringPtr(baseTime.Add(time.Second * 6).Format("2006-01-02T15:04:05.000000")),
					PromptCost:           0,
					CompletionCost:       0,
					TotalCost:            0,
					InputTokens:          0,
					OutputTokens:         0,
					InputSize:            0,
					OutputSize:           0,
					TraceTier:            util.StringPtr("longlived"),
					TraceUpgrade:         false,
					TraceFirstReceivedAt: util.StringPtr(baseTime.Add(time.Second * 2).Format("2006-01-02T15:04:05.000000")),
				}

				err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, grandchildRun)
				require.NoError(t, err)

				// Insert great-grandchild runs
				for k, greatGrandchildId := range greatGrandchildIds[i][j] {
					greatGrandchildDottedOrder := fmt.Sprintf("%s.%s000000Z%s", grandchildDottedOrder, greatGrandchildTimestamp, greatGrandchildId)
					greatGrandchildRun := testutil.InsertRun{
						Run: runs.Run{
							ID:          util.StringPtr(greatGrandchildId),
							Name:        util.StringPtr(fmt.Sprintf("GreatGrandchild_%d_%d_%d", i, j, k)),
							RunType:     util.StringPtr("chain"),
							StartTime:   util.StringPtr(baseTime.Add(time.Second * 3).Format("2006-01-02T15:04:05.000000")),
							EndTime:     util.StringPtr(baseTime.Add(time.Second * 5).Format("2006-01-02T15:04:05.000000")),
							Inputs:      map[string]interface{}{"input": "test input"},
							Outputs:     map[string]interface{}{"output": "test output"},
							Extra:       map[string]interface{}{"metadata": map[string]interface{}{"foo": "bar"}},
							Error:       nil,
							ParentRunID: util.StringPtr(grandchildId),
							Status:      util.StringPtr("success"),
							TraceID:     util.StringPtr(rootId),
							DottedOrder: util.StringPtr(greatGrandchildDottedOrder),
							SessionID:   util.StringPtr(sessionId),
						},
						IsRoot:               false,
						PromptTokens:         0,
						CompletionTokens:     0,
						TotalTokens:          0,
						FirstTokenTime:       util.StringPtr(baseTime.Add(time.Second * 3).Format("2006-01-02T15:04:05.000000")),
						ModifiedAt:           util.StringPtr(baseTime.Add(time.Second * 5).Format("2006-01-02T15:04:05.000000")),
						PromptCost:           0,
						CompletionCost:       0,
						TotalCost:            0,
						InputTokens:          0,
						OutputTokens:         0,
						InputSize:            0,
						OutputSize:           0,
						TraceTier:            util.StringPtr("longlived"),
						TraceUpgrade:         false,
						TraceFirstReceivedAt: util.StringPtr(baseTime.Add(time.Second * 3).Format("2006-01-02T15:04:05.000000")),
					}

					err = testutil.InsertRunIntoClickhouse(t, chConn, tenantId, sessionId, greatGrandchildRun)
					require.NoError(t, err)
				}
			}
		}

		// Run the query to retrieve the tree
		w := httptest.NewRecorder()
		authInfo := &auth.AuthInfo{
			TenantID:            tenantId,
			OrganizationID:      organizationId,
			UserID:              userId,
			IdentityPermissions: []string{},
			ServiceIdentity:     "false",
		}
		r := SetupRequestWithParams(
			t,
			authInfo,
			"GET",
			fmt.Sprintf(
				"/traces/%s/runs?session_id=%s&start_time=%s&end_time=%s",
				rootId,
				sessionId,
				baseTime.Format(time.RFC3339),
				baseTime.Add(4*time.Second).Format(time.RFC3339), // Add one second buffer
			),
			strings.NewReader(""),
			rootId,
			sessionId,
			baseTime.Format(time.RFC3339),
			baseTime.Add(4*time.Second).Format(time.RFC3339), // Add one second buffer
		)
		h.SingleTraceQuery(w, r)

		require.Equal(t, http.StatusOK, w.Code, w.Body.String())

		var queryResponse runs.TraceQueryResponse
		err = json.Unmarshal(w.Body.Bytes(), &queryResponse)
		require.NoError(t, err)

		// Verify all runs are returned (10 children + 100 grandchildren + 200 great-grandchildren = 310)
		require.Equal(t, 310, len(queryResponse.QueriedRuns), "Expected 310 runs in the response")

		// Build a tree from the response and verify it
		tree := buildTree(queryResponse.QueriedRuns)

		// Verify child IDs (should be positions 0-9)
		childIdSet := make(map[string]bool)
		for _, id := range childIds {
			childIdSet[id] = true
		}

		for i := 0; i < 10; i++ {
			require.True(t, childIdSet[tree[i]], "Expected child ID at position %d", i)
			delete(childIdSet, tree[i])
		}
		require.Equal(t, 0, len(childIdSet), "All child IDs should be accounted for")

		// Verify grandchild IDs (should be positions 10-109)
		grandchildIdSet := make(map[string]bool)
		for _, row := range grandchildIds {
			for _, id := range row {
				grandchildIdSet[id] = true
			}
		}

		for i := 10; i < 110; i++ {
			require.True(t, grandchildIdSet[tree[i]], "Expected grandchild ID at position %d", i)
			delete(grandchildIdSet, tree[i])
		}
		require.Equal(t, 0, len(grandchildIdSet), "All grandchild IDs should be accounted for")

		// Verify great-grandchild IDs (should be positions 110-309)
		greatGrandchildIdSet := make(map[string]bool)
		for _, level1 := range greatGrandchildIds {
			for _, level2 := range level1 {
				for _, id := range level2 {
					greatGrandchildIdSet[id] = true
				}
			}
		}

		for i := 110; i < 310; i++ {
			require.True(t, greatGrandchildIdSet[tree[i]], "Expected great-grandchild ID at position %d", i)
			delete(greatGrandchildIdSet, tree[i])
		}
		require.Equal(t, 0, len(greatGrandchildIdSet), "All great-grandchild IDs should be accounted for")
	})
}

// buildTree constructs a tree from runs and returns IDs in BFS order
func buildTree(runs []*runs.QueriedRun) []string {
	// Map each run ID to its parent run ID
	tree := make(map[string]string)
	// Map of parent IDs to their children
	childrenMap := make(map[string][]string)
	// Map to track if a run is a child of another
	isChild := make(map[string]bool)

	// Build the maps
	for _, run := range runs {
		if run.ParentRunID != "" {
			tree[run.Id] = run.ParentRunID
			childrenMap[run.ParentRunID] = append(childrenMap[run.ParentRunID], run.Id)
			isChild[run.Id] = true
		}
	}

	// Find top-level nodes (children of the root, which isn't in the results)
	topLevelNodes := []string{}
	for _, run := range runs {
		if _, exists := isChild[run.ParentRunID]; !exists && run.ParentRunID != "" {
			topLevelNodes = append(topLevelNodes, run.Id)
		}
	}

	// Perform BFS starting from the top-level nodes
	bfsNodes := []string{}
	visited := make(map[string]bool)
	toProcess := topLevelNodes

	for len(toProcess) > 0 {
		curr := toProcess[0]
		toProcess = toProcess[1:]

		if visited[curr] {
			continue
		}

		visited[curr] = true
		bfsNodes = append(bfsNodes, curr)

		// Add all children of the current node to processing queue
		for _, childId := range childrenMap[curr] {
			if !visited[childId] {
				toProcess = append(toProcess, childId)
			}
		}
	}

	return bfsNodes
}
