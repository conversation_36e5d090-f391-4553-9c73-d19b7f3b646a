package runs_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/ggicci/httpin"
	"github.com/go-chi/httplog/v2"
	"github.com/gofrs/uuid"
	"github.com/justinas/alice"
	"github.com/neilotoole/slogt"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/config"
	"langchain.com/smith/runs"
	"langchain.com/smith/storage"
	"langchain.com/smith/testutil/leak"
)

func testLogger(t *testing.T) func(next http.Handler) http.Handler {
	opt := httplog.Options{LogLevel: slog.Level(config.Env.SlogLevel)}
	log := &httplog.Logger{Logger: slogt.New(t), Options: opt}
	return httplog.RequestLogger(log)
}

func TestRunObjectsHandler_Internal(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	s3Client, err := storage.NewS3StorageClient(false, nil)
	require.NoError(t, err)
	azureClient, err := storage.NewAzureStorageClient(false, nil)
	require.NoError(t, err)

	tests := []struct {
		client storage.StorageClient
		engine string
	}{
		{
			client: s3Client,
			engine: "s3",
		},
		{
			client: azureClient,
			engine: "azure",
		},
	}

	for _, test := range tests {
		h := runs.NewRunObjectsHandler(test.client)

		t.Run(fmt.Sprintf("%s: fail without content-type", test.engine), func(t *testing.T) {
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", nil)
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusUnprocessableEntity, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: fail without body", test.engine), func(t *testing.T) {
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", nil)
			r.Header.Set("Content-Type", "application/json")
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusUnprocessableEntity, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: fail without object key", test.engine), func(t *testing.T) {
			b := strings.NewReader(`{"key": "value"}`)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", b)
			r.Header.Set("Content-Type", "application/json")
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusUnprocessableEntity, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: successful upload and download", test.engine), func(t *testing.T) {
			k := "hello/there"
			b := strings.NewReader(`{"key": "value"}`)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", b)
			defer r.Body.Close()
			r.Header.Set("Content-Type", "application/json")
			r.Header.Set("X-Object-Key", k)
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusNoContent, w.Code, w.Body.String())

			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?path="+k, nil)
			alice.New(testLogger(t), httpin.NewInput(runs.DownloadObjectReq{})).ThenFunc(h.DownloadObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, "application/json", w.Header().Get("Content-Type"))
			require.Equal(t, "inline", w.Header().Get("Content-Disposition"))
			require.Equal(t, "16", w.Header().Get("Content-Length"))
			require.Equal(t, `{"key": "value"}`, w.Body.String())

			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?path="+k+"&download_as=there", nil)
			alice.New(testLogger(t), httpin.NewInput(runs.DownloadObjectReq{})).ThenFunc(h.DownloadObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, "application/json", w.Header().Get("Content-Type"))
			require.Equal(t, "attachment; filename=\"there.json\"; filename*=\"there.json\"", w.Header().Get("Content-Disposition"))
			require.Equal(t, "16", w.Header().Get("Content-Length"))
			require.Equal(t, `{"key": "value"}`, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: fail to download with unknown path", test.engine), func(t *testing.T) {
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/?path=dont/exist", nil)
			alice.New(testLogger(t), httpin.NewInput(runs.DownloadObjectReq{})).ThenFunc(h.DownloadObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusNotFound, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: fail to head object with unknown path", test.engine), func(t *testing.T) {
			w := httptest.NewRecorder()
			r := httptest.NewRequest("HEAD", "/?path=dont/exist", nil)
			alice.New(testLogger(t), httpin.NewInput(runs.DownloadObjectReq{})).ThenFunc(h.DownloadObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusNotFound, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: fail to copy without source key", test.engine), func(t *testing.T) {
			payload := runs.CopyObjectPayload{DestKey: "dest"}
			body, err := json.Marshal(payload)
			require.NoError(t, err)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("POST", "/", bytes.NewReader(body))
			r.Header.Set("Content-Type", "application/json")
			alice.New(testLogger(t), httpin.NewInput(runs.CopyObjectRequest{})).ThenFunc(h.CopyObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: fail to copy without dest key", test.engine), func(t *testing.T) {
			payload := runs.CopyObjectPayload{SourceKey: "source"}
			body, err := json.Marshal(payload)
			require.NoError(t, err)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("POST", "/", bytes.NewReader(body))
			r.Header.Set("Content-Type", "application/json")
			alice.New(testLogger(t), httpin.NewInput(runs.CopyObjectRequest{})).ThenFunc(h.CopyObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusBadRequest, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: successful copy", test.engine), func(t *testing.T) {
			// upload object
			id, err := uuid.NewV4()
			require.NoError(t, err)
			k := fmt.Sprintf("ttl_s/%s", id)
			b := strings.NewReader(`{"key": "value"}`)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", b)
			defer r.Body.Close()
			r.Header.Set("Content-Type", "application/json")
			r.Header.Set("X-Object-Key", k)
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusNoContent, w.Code, w.Body.String())

			// copy object
			dest := fmt.Sprintf("ttl_l/%s", id)
			req := runs.CopyObjectPayload{SourceKey: k, DestKey: dest}
			body, err := json.Marshal(req)
			require.NoError(t, err)
			w = httptest.NewRecorder()
			r = httptest.NewRequest("POST", "/", bytes.NewReader(body))
			alice.New(testLogger(t), httpin.NewInput(runs.CopyObjectRequest{})).ThenFunc(h.CopyObjectInternal).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())

			// download object
			_, tok, err := h.Jwt.Encode(map[string]interface{}{"path": dest})
			require.NoError(t, err)
			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?jwt="+tok, nil)
			alice.New(testLogger(t)).ThenFunc(h.DownloadObjectPublic).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, `{"key": "value"}`, w.Body.String())
		})
	}

}

func TestRunObjectsHandler_Public(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	s3Client, err := storage.NewS3StorageClient(false, nil)
	require.NoError(t, err)
	azureClient, err := storage.NewAzureStorageClient(false, nil)
	require.NoError(t, err)

	tests := []struct {
		client storage.StorageClient
		engine string
	}{
		{
			client: s3Client,
			engine: "s3",
		},
		{
			client: azureClient,
			engine: "azure",
		},
	}

	for _, test := range tests {
		h := runs.NewRunObjectsHandler(test.client)
		t.Run(fmt.Sprintf("%s: successful upload, fails to download w invalid token", test.engine), func(t *testing.T) {
			// upload object
			k := "ttl_s/inputs/665315bd727913f8e0894a65907b1665345106468ff337e7e0167cd0ce63d143/008356df1937b4b4ffa1c9d03f181a4a18383e600aa6888ecb2c11473f7d6c22/e2234b884f00c9dbe3344709a9cc0a64cfc284291e00e2b8d6743c1faa265c66/2e606d50b413e3d27a996545f59508bd769dc3cf013480788761c825e70669eb"
			b := strings.NewReader(`{"key": "value"}`)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", b)
			defer r.Body.Close()
			r.Header.Set("Content-Type", "application/json")
			r.Header.Set("X-Object-Key", k)
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusNoContent, w.Code, w.Body.String())

			// download object
			_, tok, err := h.Jwt.Encode(map[string]interface{}{"path": k})
			require.NoError(t, err)
			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?jwt="+tok+"a", nil)
			alice.New(testLogger(t)).ThenFunc(h.DownloadObjectPublic).ServeHTTP(w, r)
			require.Equal(t, http.StatusUnauthorized, w.Code, w.Body.String())
		})

		t.Run(fmt.Sprintf("%s: successful upload, successful download with valid token", test.engine), func(t *testing.T) {
			// upload object
			k := "ttl_s/inputs/e2234b884f00c9dbe3344709a9cc0a64cfc284291e00e2b8d6743c1faa265c66/008356df1937b4b4ffa1c9d03f181a4a18383e600aa6888ecb2c11473f7d6c22/e2234b884f00c9dbe3344709a9cc0a64cfc284291e00e2b8d6743c1faa265c66/2e606d50b413e3d27a996545f59508bd769dc3cf013480788761c825e70669eb"
			b := strings.NewReader(`{"key": "value"}`)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", b)
			defer r.Body.Close()
			r.Header.Set("Content-Type", "application/json")
			r.Header.Set("X-Object-Key", k)
			testLogger(t)(http.HandlerFunc(h.UploadObject)).ServeHTTP(w, r)
			require.Equal(t, http.StatusNoContent, w.Code, w.Body.String())

			// download object
			_, tok, err := h.Jwt.Encode(map[string]interface{}{"path": k})
			require.NoError(t, err)
			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?jwt="+tok, nil)
			alice.New(testLogger(t)).ThenFunc(h.DownloadObjectPublic).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, "application/json", w.Header().Get("Content-Type"))
			require.Equal(t, "inline", w.Header().Get("Content-Disposition"))
			require.Equal(t, "16", w.Header().Get("Content-Length"))
			require.Equal(t, `{"key": "value"}`, w.Body.String())

			// download object with donwload_as without extension
			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?download_as=hello&jwt="+tok, nil)
			alice.New(testLogger(t)).ThenFunc(h.DownloadObjectPublic).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, "application/json", w.Header().Get("Content-Type"))
			require.Equal(t, "attachment; filename=\"2e606d50b413e3d27a996545f59508bd769dc3cf013480788761c825e70669eb.json\"; filename*=\"hello.json\"", w.Header().Get("Content-Disposition"))
			require.Equal(t, "16", w.Header().Get("Content-Length"))
			require.Equal(t, `{"key": "value"}`, w.Body.String())

			// download object with download_as with extension
			w = httptest.NewRecorder()
			r = httptest.NewRequest("GET", "/?download_as=hello.json&jwt="+tok, nil)
			alice.New(testLogger(t)).ThenFunc(h.DownloadObjectPublic).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, "application/json", w.Header().Get("Content-Type"))
			require.Equal(t, "attachment; filename=\"2e606d50b413e3d27a996545f59508bd769dc3cf013480788761c825e70669eb.json\"; filename*=\"hello.json\"", w.Header().Get("Content-Disposition"))
			require.Equal(t, "16", w.Header().Get("Content-Length"))
			require.Equal(t, `{"key": "value"}`, w.Body.String())
		})
	}
}

func TestRunObjectsHandler_Performance(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// spool to disk when size is greater than minimum and less than cumulative limit
	diskOpts := &storage.BlobStorageClientOptions{
		SpoolMinSizeBytes: 2,
		SpoolLimitBytes:   1024 * 1024 * 1024,
	}

	// spool to memory when size is less than minimum and the cumulative limit
	memOpts := &storage.BlobStorageClientOptions{
		SpoolMinSizeBytes: 1024 * 1024 * 1024,
		SpoolLimitBytes:   1024 * 1024 * 1024,
	}

	// upload directly when size is greater than the cumulative limit, even if it's greater than minimum
	directOpts := &storage.BlobStorageClientOptions{
		SpoolMinSizeBytes: 2,
		SpoolLimitBytes:   2,
	}

	tests := []struct {
		name       string
		clientType string
		opts       *storage.BlobStorageClientOptions
		expected   storage.UploadStrategy
	}{
		{
			name:       "from disk",
			clientType: "Azure",
			opts:       diskOpts,
			expected:   storage.UploadStrategyDisk,
		},
		{
			name:       "from memory",
			clientType: "Azure",
			opts:       memOpts,
			expected:   storage.UploadStrategyMemory,
		},
		{
			name:       "direct",
			clientType: "Azure",
			opts:       directOpts,
			expected:   storage.UploadStrategyDirect,
		},
		{
			name:       "from disk",
			clientType: "S3",
			opts:       diskOpts,
			expected:   storage.UploadStrategyDisk,
		},
		{
			name:       "from memory",
			clientType: "S3",
			opts:       memOpts,
			expected:   storage.UploadStrategyMemory,
		},
		{
			name:       "direct",
			clientType: "S3",
			opts:       directOpts,
			expected:   storage.UploadStrategyDirect,
		},
	}

	for _, test := range tests {
		var client storage.StorageClient
		var err error
		if test.clientType == "Azure" {
			client, err = storage.NewAzureStorageClient(false, test.opts)
		} else if test.clientType == "S3" {
			client, err = storage.NewS3StorageClient(false, test.opts)
		} else {
			t.Fatalf("unknown client type: %s", test.clientType)
		}
		require.NoError(t, err)
		h := runs.NewRunObjectsHandler(client)

		t.Run(fmt.Sprintf("%s: %s", test.clientType, test.name), func(t *testing.T) {
			id, err := uuid.NewV4()
			require.NoError(t, err)
			k := fmt.Sprintf("ttl_s/%s", id)
			b := strings.NewReader(`{"key": "value"}`)
			strategy, err := h.Client.UploadObject(context.Background(), &storage.UploadObjectInput{
				Bucket:          config.Env.S3BucketName,
				ContentEncoding: "",
				ContentLength:   int64(b.Len()),
				ContentType:     "application/json",
				Reader:          b,
				Key:             k,
			})
			require.NoError(t, err)
			require.Equal(t, test.expected, strategy)

			// download object
			_, tok, err := h.Jwt.Encode(map[string]interface{}{"path": k})
			require.NoError(t, err)
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/?jwt="+tok, nil)
			alice.New(testLogger(t)).ThenFunc(h.DownloadObjectPublic).ServeHTTP(w, r)
			require.Equal(t, http.StatusOK, w.Code, w.Body.String())
			require.Equal(t, "application/json", w.Header().Get("Content-Type"))
			require.Equal(t, "inline", w.Header().Get("Content-Disposition"))
			require.Equal(t, "16", w.Header().Get("Content-Length"))
			require.Equal(t, `{"key": "value"}`, w.Body.String())
		})
	}
}
