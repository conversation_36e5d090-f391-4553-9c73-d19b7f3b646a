package ingestion

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"langchain.com/smith/auth"
	"langchain.com/smith/compression"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/queue"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
)

const (
	QueueRunSetExpiration = 24*time.Hour + 5*time.Minute
	TaskInQueuedSet       = 1
)

// Error codes.
const (
	CodeInvalidInput       = "invalid_input"
	CodeInternal           = "internal"
	CodeDuplicate          = "duplicate"
	CodeTraceLimitExceeded = "trace_limit_exceeded"
	CodeUsageLimitExceeded = "usage_limit_exceeded"
)

type IngestionError struct {
	Code    string
	Message string
	Err     error
}

func (e *IngestionError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

func (e *IngestionError) Unwrap() error {
	return e.Err
}

func newError(code, message string, err error) *IngestionError {
	return &IngestionError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

type ExtraValue struct {
	ContentType string
	Encoding    string
	Data        []byte
}

type QueuePayload struct {
	RunID         uuid.UUID
	ParentID      *uuid.UUID
	TraceID       *uuid.UUID
	Value         []byte
	ContentType   string
	HashKey       string
	SetKey        *string
	ProcessInline bool
	SessionID     *uuid.UUID
	SessionName   *string
	StartTime     *string
	Extra         map[string]ExtraValue
	AutoUpgrade   bool
}

type HashKey string

const (
	HashKeyPost  HashKey = "post"
	HashKeyPatch HashKey = "patch"
)

func (p *QueuePayload) SessionHashStr() (string, error) {
	data := map[string]interface{}{
		"session_id":   p.SessionID,
		"session_name": p.SessionName,
	}
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func EnsureSessionsBeforeQueueRunPayload(
	ctx context.Context,
	db *database.AuditLoggedPool,
	usageLimitsClient *usage_limits.UsageLimitsClient,
	tracerSessionsClient *tracer_sessions.TracerSessionsClient,
	authInfo auth.AuthInfo,
	payloads []QueuePayload,
	receivedAt *string,
) error {
	if receivedAt == nil {
		now := time.Now().UTC().Format(time.RFC3339)
		receivedAt = &now
	}

	semaphore := make(chan struct{}, config.Env.IngestProjectCreationSemaphore)

	sessionToEarliestPayloadMap := make(map[string]QueuePayload)

	getStartTime := func(payload QueuePayload) string {
		if payload.StartTime != nil {
			return *payload.StartTime
		}
		return *receivedAt
	}

	var payloadsWithSessions []QueuePayload
	for _, payload := range payloads {
		if payload.HashKey == string(HashKeyPost) {
			payloadsWithSessions = append(payloadsWithSessions, payload)
		}
	}

	// Sort the payloads in reverse order by start time
	sort.Slice(payloadsWithSessions, func(i, j int) bool {
		return getStartTime(payloadsWithSessions[i]) > getStartTime(payloadsWithSessions[j])
	})

	// Map sessions to their earliest payload
	for _, payload := range payloadsWithSessions {
		sessionHash, err := payload.SessionHashStr()
		if err != nil {
			return err
		}
		if _, exists := sessionToEarliestPayloadMap[sessionHash]; !exists {
			sessionToEarliestPayloadMap[sessionHash] = payload
		}
	}

	var wg sync.WaitGroup
	var firstError error
	var mutex sync.Mutex

	sessions := make([]*tracer_sessions.TracerSessionWithoutVirtualFields, 0, len(sessionToEarliestPayloadMap))
	var sessionsMu sync.Mutex

	// Start or fetch tracer sessions with concurrency control
	for _, payload := range sessionToEarliestPayloadMap {
		wg.Add(1)
		semaphore <- struct{}{} // acquire semaphore
		go func(payload QueuePayload) {
			defer wg.Done()
			defer func() { <-semaphore }() // release semaphore

			startTimeStr := getStartTime(payload)
			var sessionIDStr *string
			if payload.SessionID != nil {
				idStr := payload.SessionID.String()
				sessionIDStr = &idStr
			}
			sessionName := payload.SessionName

			session, err := tracerSessionsClient.StartOrFetchTracerSession(
				ctx,
				authInfo,
				sessionIDStr,
				sessionName,
				&startTimeStr,
			)
			if err != nil {
				mutex.Lock()
				if firstError == nil {
					firstError = err
				}
				mutex.Unlock()
				return
			}

			sessionsMu.Lock()
			sessions = append(sessions, session)
			sessionsMu.Unlock()
		}(payload)
	}

	wg.Wait()

	if firstError != nil {
		return firstError
	}

	// Check for long-lived trace tiers
	if config.Env.FFTraceTiersEnabled {
		longlivedFound := false
		for _, session := range sessions {
			if *session.TraceTier == tracer_sessions.LongLived {
				longlivedFound = true
				break
			}
		}
		autoUpgradeFound := false
		for _, payload := range payloads {
			if payload.AutoUpgrade {
				autoUpgradeFound = true
				break
			}
		}
		if longlivedFound || autoUpgradeFound {
			tenantExceededLonglivedLimits, limitExceededMessage, err := usageLimitsClient.CheckLonglivedUsageLimits(ctx, authInfo)
			if err != nil {
				return fmt.Errorf("failed to check longlived usage limits: %w", err)
			}
			if tenantExceededLonglivedLimits {
				return newError(CodeUsageLimitExceeded, limitExceededMessage, nil)
			}
		}
	}

	return nil
}

func QueueRunPayload(
	ctx context.Context,
	routedRedisClient redis.UniversalClient,
	queueingRedisClient redis.UniversalClient,
	authInfo auth.AuthInfo,
	traceIDs map[uuid.UUID]struct{},
	payloads []QueuePayload,
	affectsShorttermLimits bool,
	dualWrite bool,
) error {
	oplog := httplog.LogEntry(ctx)
	// validate payloads
	for _, payload := range payloads {
		if payload.HashKey != "" {
			if payload.HashKey != string(HashKeyPost) && payload.HashKey != string(HashKeyPatch) {
				return newError(CodeInvalidInput, fmt.Sprintf("invalid value: %s", payload.HashKey), nil)
			}
		} else if payload.SetKey != nil {
			if *payload.SetKey != "feedback" {
				return newError(CodeInvalidInput, fmt.Sprintf("invalid value: %s", *payload.SetKey), nil)
			}
			if payload.Extra != nil {
				return newError(CodeInvalidInput, "extra is not allowed for set_key", nil)
			}
			if payload.ContentType != "application/json" {
				return newError(CodeInvalidInput, "content_type must be application/json for set_key", nil)
			}
		} else {
			return newError(CodeInvalidInput, "one of hash_key or set_key should be set", nil)
		}

		if payload.ParentID != nil && payload.ParentID.String() == payload.RunID.String() {
			return newError(CodeInvalidInput, "parent_id and run_id cannot be the same", nil)
		}
	}

	// record received_at timestamp
	receivedAtBytes, err := json.Marshal(time.Now().UTC())
	if err != nil {
		return newError(CodeInternal, "failed to marshal timestamp", err)
	}
	receivedAt := string(receivedAtBytes)

	authID := authInfo.TenantID

	// short-circuit if payload already received
	type payloadIndexCmd struct {
		index int
		cmd   *redis.StringCmd
	}

	var cmds []payloadIndexCmd

	pipe := routedRedisClient.TxPipeline()

	for i, payload := range payloads {
		if payload.HashKey != "" {
			pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, payload.RunID)
			cmd := pipe.HGet(ctx, pendingKey, payload.HashKey+"_received_at")
			cmds = append(cmds, payloadIndexCmd{index: i, cmd: cmd})
		}
	}

	if len(cmds) > 0 {
		_, err := pipe.Exec(ctx)
		if err != nil && err != redis.Nil {
			return newError(CodeInternal, "failed to execute Redis pipeline", err)
		}

		// remove payloads that have already been received
		indicesToRemove := make(map[int]struct{})

		for _, pc := range cmds {
			_, err := pc.cmd.Result()
			if err == nil {
				// field exists, mark for removal
				indicesToRemove[pc.index] = struct{}{}
			} else if err != redis.Nil {
				return newError(CodeInternal, "failed to get Redis result", err)
			}
		}

		if len(indicesToRemove) > 0 {
			// remove indices from payloads
			newPayloads := make([]QueuePayload, 0, len(payloads)-len(indicesToRemove))
			for i, payload := range payloads {
				if _, skip := indicesToRemove[i]; !skip {
					newPayloads = append(newPayloads, payload)
				}
			}
			payloads = newPayloads
		}

		if len(payloads) == 0 {
			return newError(CodeDuplicate, "payloads already received", nil)
		}
	}

	// find the root run id (trace_ids)
	if len(payloads) == 1 {
		var err error
		traceIDs, err = validateAndGetTraceIDs(ctx, routedRedisClient, authID, payloads[0], traceIDs)
		if err != nil {
			return err
		}
	} else {
		if len(traceIDs) == 0 {
			return newError(CodeInvalidInput, "trace_ids must be specified for batch requests", nil)
		}
	}

	shouldCheckLimits := !config.Env.IsSelfHosted

	useInlineProcessing := false
	if len(traceIDs) > 0 {
		allProcessInline := true
		for _, payload := range payloads {
			if !payload.ProcessInline {
				allProcessInline = false
				break
			}
		}
		if allProcessInline {
			useInlineProcessing = true
		} else {
			allFeedbackForRoot := true
			for _, payload := range payloads {
				if payload.SetKey == nil || *payload.SetKey != "feedback" {
					allFeedbackForRoot = false
					break
				}
				if _, exists := traceIDs[payload.RunID]; !exists {
					allFeedbackForRoot = false
					break
				}
			}
			if allFeedbackForRoot {
				useInlineProcessing = true
			}
		}
	}

	queuedRunSetForAuth := QueuedRunsKey(authID)

	chunkSize := config.Env.RedisTransactionRunsChunkSize
	if chunkSize <= 0 {
		chunkSize = 40
	}

	var allStatusResults []int64
	var traceIDList []uuid.UUID

	for t := range traceIDs {
		traceIDList = append(traceIDList, t)
	}
	sort.Slice(traceIDList, func(i, j int) bool {
		return strings.Compare(traceIDList[i].String(), traceIDList[j].String()) < 0
	})

	// Initialize maps before use
	traceUpgradeSet := make(map[string]interface{})

	for start := 0; start < len(payloads); start += chunkSize {
		end := start + chunkSize
		if end > len(payloads) {
			end = len(payloads)
		}
		chunk := payloads[start:end]

		pipe := routedRedisClient.TxPipeline()

		type compressedPayload struct {
			value             []byte
			compressionMethod compression.CompressionMethod
		}
		compressedPayloadsAndMethods := make([]compressedPayload, len(chunk))
		compressedExtrasList := make([]map[string]compression.CompressedData, len(chunk))

		for i, payload := range chunk {
			if payload.HashKey != "" {
				value, compressionMethod, err := compression.CompressBasedOnSize("", payload.Value)
				if err != nil {
					return newError(CodeInternal, "failed to compress payload", err)
				}
				compressedPayloadsAndMethods[i] = compressedPayload{
					value:             value,
					compressionMethod: compressionMethod,
				}
			} else {
				compressedPayloadsAndMethods[i] = compressedPayload{
					value:             payload.Value,
					compressionMethod: compression.NONE,
				}
			}

			if payload.Extra != nil {
				payloadDataMap := make(map[string]compression.PayloadData)
				for k, v := range payload.Extra {
					if v.ContentType != "" || v.Encoding != "" {
						payloadDataMap[k] = compression.PayloadData{
							ContentType:     v.ContentType,
							ContentEncoding: v.Encoding,
							Payload:         v.Data,
						}
					}
				}
				compressedExtras, err := compression.CompressBasedOnSizeMany(payloadDataMap)
				if err != nil {
					return newError(CodeInternal, "failed to compress extras", err)
				}
				compressedExtrasList[i] = compressedExtras
			}
		}

		var chunkStatusCmds []*redis.BoolCmd
		var smIsMemberCmds *redis.BoolSliceCmd
		if !useInlineProcessing && len(traceIDs) > 0 {
			if config.Env.RedisUseSmismember {
				traceIDStrs := make([]interface{}, len(traceIDList))
				for i, tID := range traceIDList {
					traceIDStrs[i] = tID.String()
				}
				smIsMemberCmds = pipe.SMIsMember(ctx, queuedRunSetForAuth, traceIDStrs...)

			} else {
				for _, tID := range traceIDList {
					statusCmd := pipe.SIsMember(ctx, queuedRunSetForAuth, tID.String())
					chunkStatusCmds = append(chunkStatusCmds, statusCmd)
				}
			}
		}

		traceLimitMap := make(map[string]int)       // traceID string -> available slots
		traceIDToUUID := make(map[string]uuid.UUID) // traceID string -> traceID uuid

		for _, payload := range chunk {
			if config.Env.FFTraceTiersEnabled && (payload.TraceID != nil || (len(chunk) == 1 && len(traceIDs) > 0)) {
				var tid uuid.UUID
				if payload.TraceID != nil {
					tid = *payload.TraceID
				} else {
					for t := range traceIDs {
						tid = t
						break
					}
				}
				tidStr := tid.String()
				if _, exists := traceLimitMap[tidStr]; !exists {
					// Initialize; actual available slots will be computed via SCARD
					traceLimitMap[tidStr] = 0
					traceIDToUUID[tidStr] = tid
				}
			}
		}

		if len(traceLimitMap) > 0 {
			pipeForLimit := routedRedisClient.Pipeline()
			scardCmds := make(map[string]*redis.IntCmd)
			for tidStr := range traceLimitMap {
				key := fmt.Sprintf("smith:runs:trace_runs:%s:%s", authID, tidStr)
				scardCmds[tidStr] = pipeForLimit.SCard(ctx, key)
			}
			_, err := pipeForLimit.Exec(ctx)
			if err != nil && err != redis.Nil {
				return newError(CodeInternal, "failed to execute SCARD pipeline", err)
			}
			for tidStr, cmd := range scardCmds {
				count, err := cmd.Result()
				if err != nil && err != redis.Nil {
					return newError(CodeInternal, "failed to get SCARD result", err)
				}
				available := int(config.Env.MaxTraceLimit) - int(count)
				traceLimitMap[tidStr] = available
			}
		}

		// store each chunk's payload
		payloadSizeIncr := 0
		for i, payload := range chunk {
			comp := compressedPayloadsAndMethods[i]
			extras := compressedExtrasList[i]
			pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, payload.RunID)

			// HSET for hashKey-based runs
			if payload.HashKey != "" {
				pipe.HSetNX(ctx, pendingKey, payload.HashKey, comp.value)
				pipe.HSetNX(ctx, pendingKey, payload.HashKey+"_received_at", receivedAt)
				pipe.HSetNX(ctx, pendingKey, payload.HashKey+"_content_type", payload.ContentType)
				methodBytes, err := json.Marshal(comp.compressionMethod)
				if err != nil {
					return newError(CodeInternal, "failed to marshal compression method", err)
				}
				pipe.HSetNX(ctx, pendingKey, payload.HashKey+"_compression_method", methodBytes)
				pipe.Expire(ctx, pendingKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)
			}
			// Store extras
			if payload.Extra != nil {
				extraKey := fmt.Sprintf("%s:extra", pendingKey)
				for key, extraVal := range payload.Extra {
					if extras != nil {
						if val, ok := extras[key]; ok {
							pipe.HSet(ctx, extraKey, key, val.Data, key+"_compression_method", val.CompressionMethod, key+"_content_type", extraVal.ContentType)
						} else {
							pipe.HSet(ctx, extraKey, key+"_s3_url", string(extraVal.Data))
						}
					} else {
						pipe.HSet(ctx, extraKey, key+"_s3_url", string(extraVal.Data))
					}
				}
				pipe.Expire(ctx, extraKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)
			}
			// If setKey-based (feedback, etc)
			if payload.SetKey != nil {
				fullSetKey := fmt.Sprintf("smith:runs:%s:%s:%s", *payload.SetKey, authID, payload.RunID)
				pipe.SAdd(ctx, fullSetKey, comp.value)
				pipe.Expire(ctx, fullSetKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)
			}
			// Parent relationship
			if payload.ParentID != nil {
				parentIDJson, err := json.Marshal(payload.ParentID.String())
				if err != nil {
					return newError(CodeInternal, "failed to marshal parent ID", err)
				}
				pipe.HSetNX(ctx, pendingKey, "parent", parentIDJson)
				childrenKey := fmt.Sprintf("smith:runs:children:%s:%s", authID, payload.ParentID.String())
				runIDJson, err := json.Marshal(payload.RunID.String())
				if err != nil {
					return newError(CodeInternal, "failed to marshal run ID", err)
				}
				pipe.SAdd(ctx, childrenKey, runIDJson)
				pipe.Expire(ctx, childrenKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)
			}
			if payload.TraceID != nil {
				payloadTidStr := payload.TraceID.String()
				traceIDJson, err := json.Marshal(payloadTidStr)
				if err != nil {
					return newError(CodeInternal, "failed to marshal trace ID", err)
				}
				pipe.HSetNX(ctx, pendingKey, "trace_id", traceIDJson)
			}
			// Set session ID if available
			if payload.SessionID != nil {
				sessionIDJson, err := json.Marshal(payload.SessionID.String())
				if err != nil {
					return newError(CodeInternal, "failed to marshal session ID", err)
				}
				pipe.HSetNX(ctx, pendingKey, "session_id", sessionIDJson)
			}
			if config.Env.FFTraceTiersEnabled && (payload.TraceID != nil || (len(chunk) == 1 && len(traceIDs) > 0)) {
				var traceID uuid.UUID
				if payload.TraceID != nil {
					traceID = *payload.TraceID
				} else {
					for tID := range traceIDs {
						traceID = tID
						break
					}
				}
				tidStr := traceID.String()
				if available, ok := traceLimitMap[tidStr]; ok {
					if available <= 0 {
						return newError(CodeTraceLimitExceeded, fmt.Sprintf("trace run limit exceeded for trace id %s", tidStr), nil)
					}
					traceLimitMap[tidStr] = available - 1
				}
				tracePendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, tidStr)
				traceRunsKey := fmt.Sprintf("smith:runs:trace_runs:%s:%s", authID, tidStr)
				runIDJson, err := json.Marshal(payload.RunID.String())
				if err != nil {
					return newError(CodeInternal, "failed to marshal run ID", err)
				}
				pipe.HSetNX(ctx, tracePendingKey, "trace_first_received_at", receivedAt)
				pipe.Expire(ctx, tracePendingKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)
				pipe.SAdd(ctx, traceRunsKey, runIDJson)
				pipe.Expire(ctx, traceRunsKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)
				if _, ok := traceUpgradeSet[tidStr]; !ok && payload.AutoUpgrade {
					oplog.Info("Auto upgrading trace tier", "trace_id", traceID, "run_id", payload.RunID)
					upgradeTierJson, err := json.Marshal(tracer_sessions.LongLived)
					if err != nil {
						return newError(CodeInternal, "failed to marshal upgrade tier", err)
					}
					pipe.HSet(ctx, tracePendingKey, "upgrade_trace_tier", upgradeTierJson)
					traceUpgradeSet[tidStr] = struct{}{}
				}
			}
			// Increase total size for usage-limits
			size := len(payload.Value)
			if payload.Extra != nil {
				for _, v := range payload.Extra {
					size += len(v.Data)
				}
			}
			payloadSizeIncr += size
		}

		// Usage-limits
		if shouldCheckLimits && affectsShorttermLimits {
			payloadSizeKey := usage_limits.UsageLimitPayloadSizePerHourCounterKey(authID)
			pipe.IncrBy(ctx, payloadSizeKey, int64(payloadSizeIncr))
			pipe.Expire(ctx, payloadSizeKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)

			totalRequestsPerHourKey := usage_limits.UsageLimitEventsIngestedPerHourCounterKey(authID)
			pipe.IncrBy(ctx, totalRequestsPerHourKey, int64(len(chunk)))
			pipe.Expire(ctx, totalRequestsPerHourKey, time.Duration(config.Env.RedisRunsExpirySeconds)*time.Second)

			totalRequestsPerMinuteKey := usage_limits.UsageLimitEventsIngestedPerMinuteCounterKey(authID)
			pipe.IncrBy(ctx, totalRequestsPerMinuteKey, int64(len(chunk)))
			pipe.Expire(ctx, totalRequestsPerMinuteKey, 5*time.Minute)
		}

		// If usage-limits + traceIDs => update HLL
		if shouldCheckLimits && len(traceIDs) > 0 {
			hllKey := usage_limits.UsageLimitUniqueTracesPerMonthHLLKey(authID)
			var traceIDJsons []interface{}
			for _, tID := range traceIDList {
				tIDjson, err := json.Marshal(tID.String())
				if err != nil {
					return newError(CodeInternal, "failed to marshal trace ID", err)
				}
				traceIDJsons = append(traceIDJsons, tIDjson)
			}
			pipe.PFAdd(ctx, hllKey, traceIDJsons...)
			expiry := startOfNextMonthTimestamp()
			pipe.ExpireAt(ctx, hllKey, time.Unix(int64(expiry), 0))

			limits := usage_limits.GetAllLimits(usage_limits.UsageLimitUnitTraces)
			for _, limit := range limits {
				limit.MarkSeenEventsInPipe(ctx, traceIDs, pipe, authID)
			}
		}

		// Queue in a separate pipeline for Cluster support
		res, pipeErr := pipe.Exec(ctx)
		if pipeErr != nil && pipeErr != redis.Nil {
			oplog.Error("error executing ingest pipeline", "err", pipeErr, "res", res)
			return newError(CodeInternal, "error executing Redis ingest pipeline", pipeErr)
		}

		if !dualWrite {
			queuePipe := queueingRedisClient.TxPipeline()
			if useInlineProcessing {
				if err := EnqueueInlineProcessing(ctx, queuePipe, chunk, authInfo); err != nil {
					return newError(CodeInternal, "failed to enqueue inline processing", err)
				}
			}
			_, queuePipeErr := queuePipe.Exec(ctx)

			if queuePipeErr != nil && queuePipeErr != redis.Nil {
				// handle NOSCRIPT error by loading script and retrying
				if strings.Contains(queuePipeErr.Error(), "NOSCRIPT") {
					// re-enqueue inline processing job in pipeline
					if err := EnqueueInlineProcessing(ctx, queuePipe, chunk, authInfo); err != nil {
						return newError(CodeInternal, "failed to enqueue inline processing", err)
					}
					queuePipeErr = handleNOSCRIPTError(ctx, oplog, queuePipe, queueingRedisClient)
				}
				// handle non-NOSCRIPT errors
				if queuePipeErr != nil && queuePipeErr != redis.Nil {
					oplog.Error("error executing chunk pipeline", "err", queuePipeErr)
					return newError(CodeInternal, "error executing Redis pipeline for chunk", queuePipeErr)
				}
			}
		}

		if !useInlineProcessing && len(traceIDs) > 0 {
			if config.Env.RedisUseSmismember {
				result, err := smIsMemberCmds.Result()
				if err != nil && err != redis.Nil {
					return newError(CodeInternal, "failed to get status result", err)
				}
				for _, isMember := range result {
					if isMember {
						allStatusResults = append(allStatusResults, 1)
					} else {
						allStatusResults = append(allStatusResults, 0)
					}
				}
			} else {
				for _, sCmd := range chunkStatusCmds {
					isMember, err := sCmd.Result()
					if err != nil && err != redis.Nil {
						return newError(CodeInternal, "failed to get status result", err)
					}
					if isMember {
						allStatusResults = append(allStatusResults, 1)
					} else {
						allStatusResults = append(allStatusResults, 0)
					}
				}
			}

		}
	}

	if len(traceIDs) == 0 {
		return nil
	}
	if !useInlineProcessing {

		if len(allStatusResults) < len(traceIDs) {
			allStatusResults = make([]int64, len(traceIDs))
		}

		allInQueuedSet := true
		for _, st := range allStatusResults[:len(traceIDs)] {
			if st != TaskInQueuedSet {
				allInQueuedSet = false
				break
			}
		}
		if allInQueuedSet {
			// Tasks currently in the queue, no need to schedule again
			oplog.Info("Runs already in queue, skipping", "trace_ids", traceIDList)
			return nil
		}

		// Figure out tasks to schedule
		var jobIDsToQueue []string
		var jobs []queue.JobEnqueue

		lbTraceContext, _ := ctx.Value(config.LbTraceCtxKey).(string)
		apiKeyShort, _ := ctx.Value(config.ApiKeyCtxKey).(string)
		for i, tID := range traceIDList {
			if allStatusResults[i] != TaskInQueuedSet {
				job := queue.JobEnqueue{
					Func: "persist_run_or_parent",
					Kwargs: map[string]interface{}{
						"auth_id":          authID,
						"run_id":           tID.String(),
						"lb_trace_context": lbTraceContext,
						"api_key_short":    apiKeyShort,
					},
				}
				jobs = append(jobs, job)
				jobIDsToQueue = append(jobIDsToQueue, tID.String())
			} else {
				oplog.Info("Run already in queue, skipping", "trace_id", tID.String())
			}
		}

		if len(jobs) > 0 {
			pipe = routedRedisClient.TxPipeline()
			jobIDsToQueueInterfaces := make([]interface{}, len(jobIDsToQueue))
			for i, v := range jobIDsToQueue {
				jobIDsToQueueInterfaces[i] = v
			}
			pipe.SAdd(ctx, queuedRunSetForAuth, jobIDsToQueueInterfaces...)
			pipe.Expire(ctx, queuedRunSetForAuth, QueueRunSetExpiration)
			_, err = pipe.Exec(ctx)
			if err != nil && err != redis.Nil {
				oplog.Error("error executing Redis pipeline on final queued runs", "err", err)
				return newError(CodeInternal, "error executing Redis pipeline on final queued runs", err)
			}
			if !dualWrite {
				queuePipe := queueingRedisClient.TxPipeline()
				for _, job := range jobs {
					if err := EnqueueJob(ctx, queuePipe, job); err != nil {
						return newError(CodeInternal, "failed to enqueue job", err)
					}
				}
				_, queuePipeErr := queuePipe.Exec(ctx)
				if queuePipeErr != nil && queuePipeErr != redis.Nil {
					if strings.Contains(queuePipeErr.Error(), "NOSCRIPT") {
						// re-enqueue jobs in pipeline
						for _, job := range jobs {
							enqueueErr := EnqueueJob(ctx, queuePipe, job)
							if enqueueErr != nil {
								oplog.Error("error enqueueing job", "err", enqueueErr)
								return newError(CodeInternal, "error enqueueing job", enqueueErr)
							}
						}
						queuePipeErr = handleNOSCRIPTError(ctx, oplog, queuePipe, queueingRedisClient)
					}
					if queuePipeErr != nil && queuePipeErr != redis.Nil {
						oplog.Error("error executing Redis pipeline on final scheduling", "err", queuePipeErr)
						return newError(CodeInternal, "error executing Redis pipeline on final scheduling", queuePipeErr)
					}
				}
			}
		}
	}

	return nil
}

func QueuedRunsKey(authID string) string {
	currentDay := time.Now().UTC().Format("2006-01-02")
	return fmt.Sprintf("smith:runs:queued_runs:%s:%s", authID, currentDay)
}

func EnqueueJob(ctx context.Context, pipe redis.Pipeliner, jobEnqueue queue.JobEnqueue) error {
	job := queue.NewJob(queue.JobOptions{
		Function: jobEnqueue.Func,
		Queue:    config.Env.IngestionQueue,
		Kwargs:   jobEnqueue.Kwargs,
	})

	// Enqueue the job using a pipeline
	err := queue.EnqueueWithPipeline(ctx, job, pipe)
	if err != nil {
		return fmt.Errorf("error enqueueing job: %w", err)
	}
	return nil
}

func handleNOSCRIPTError(ctx context.Context, oplog *slog.Logger, pipe redis.Pipeliner, queueingRedisClient redis.UniversalClient) error {
	err := InitializeScriptAndRetry(ctx, oplog, pipe, queueingRedisClient)
	if err != nil {
		return newError(CodeInternal, "error initializing Redis script", err)
	}
	return nil
}

func InitializeScriptAndRetry(ctx context.Context, oplog *slog.Logger, pipe redis.Pipeliner, queueingRedisClient redis.UniversalClient) error {
	_, initErr := queue.InitializeScripts(ctx, queueingRedisClient)
	if initErr != nil {
		return newError(CodeInternal, "error initializing Redis script", initErr)
	}
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		oplog.Error("error executing Redis pipeline after script reload", "err", err)
		return newError(CodeInternal, "error executing Redis pipeline after script reload", err)
	}
	return nil
}

func EnqueueInlineProcessing(ctx context.Context, pipe redis.Pipeliner, payloads []QueuePayload, authInfo auth.AuthInfo) error {
	var runIDs []interface{}
	if config.Env.FFTraceTiersEnabled {
		for _, payload := range payloads {
			runIDs = append(runIDs, []interface{}{payload.TraceID.String(), payload.RunID.String()})
		}
	} else {
		for _, payload := range payloads {
			runIDs = append(runIDs, payload.RunID.String())
		}
	}
	lbTraceContext, _ := ctx.Value(config.LbTraceCtxKey).(string)
	apiKeyShort, _ := ctx.Value(config.ApiKeyCtxKey).(string)

	kwargs := map[string]interface{}{
		"run_ids":          runIDs,
		"tenant_id":        authInfo.TenantID,
		"lb_trace_context": lbTraceContext,
		"api_key_short":    apiKeyShort,
	}

	job := queue.NewJob(queue.JobOptions{
		Function: "persist_batched_runs",
		Queue:    config.Env.IngestionQueue,
		Kwargs:   kwargs,
	})

	err := queue.EnqueueWithPipeline(ctx, job, pipe)
	if err != nil {
		return fmt.Errorf("error enqueueing inline processing job: %w", err)
	}
	return nil
}

func startOfNextMonthTimestamp() float64 {
	now := time.Now().UTC()

	firstOfNextMonth := time.Date(
		now.Year(),
		now.Month()+1,
		1, // day
		0, // hour
		1, // minute
		0, // second
		0, // nanosecond
		time.UTC,
	)

	return float64(firstOfNextMonth.Unix())
}

func validateAndGetTraceIDs(ctx context.Context, routedClient redis.UniversalClient, authID string, payload QueuePayload, traceIDs map[uuid.UUID]struct{}) (map[uuid.UUID]struct{}, error) {
	if len(traceIDs) > 0 {
		// do nothing
	} else if payload.ParentID == nil && payload.HashKey == string(HashKeyPost) {
		traceIDs = map[uuid.UUID]struct{}{
			payload.RunID: {},
		}
	} else {
		var currentID uuid.UUID
		seen := make(map[uuid.UUID]struct{})
		if payload.ParentID != nil {
			currentID = *payload.ParentID
			seen[payload.RunID] = struct{}{}
		} else {
			currentID = payload.RunID
		}
		for i := 0; i <= 1000; i++ {
			if i == 1000 {
				return nil, newError(CodeInvalidInput, "run tree too deep", nil)
			}
			if _, exists := seen[currentID]; exists {
				return nil, newError(CodeInvalidInput, "cycle detected in run tree", nil)
			}
			seen[currentID] = struct{}{}

			pipe := routedClient.TxPipeline()
			pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, currentID)
			existsCmd := pipe.Exists(ctx, pendingKey)
			getCmds := pipe.HMGet(ctx, pendingKey, "trace_id", "parent")
			_, err := pipe.Exec(ctx)
			if err != nil && err != redis.Nil {
				return nil, newError(CodeInternal, "failed to execute Redis pipeline", err)
			}
			exists, err := existsCmd.Result()
			if err != nil && err != redis.Nil {
				return nil, newError(CodeInternal, "failed to check Redis key existence", err)
			}
			if exists == 0 {
				// parent hasn't been queued yet
				break
			}
			getCmdsResult, err := getCmds.Result()
			if err != nil && err != redis.Nil {
				return nil, newError(CodeInternal, "failed to check pending runs parent and trace ID", err)
			}
			if len(getCmdsResult) != 2 {
				return nil, newError(CodeInternal, "invalid HMGET response", fmt.Errorf("invalid response length: %d", len(getCmdsResult)))
			}
			traceOfCurrentIDResult := getCmdsResult[0]

			if err == nil && traceOfCurrentIDResult != nil {
				traceOfCurrentID := traceOfCurrentIDResult.(string)
				traceUUID, err := uuid.Parse(traceOfCurrentID)
				if err != nil {
					return nil, newError(CodeInvalidInput, "invalid trace ID format", nil)
				}
				traceIDs = map[uuid.UUID]struct{}{
					traceUUID: {},
				}
				break
			} else if err != nil && !errors.Is(err, redis.Nil) {
				return nil, newError(CodeInternal, "failed to get trace ID from Redis", err)
			}

			parentOfCurrentIDResult := getCmdsResult[1]
			if parentOfCurrentIDResult == nil {
				// parent field does not exist
				traceIDs = map[uuid.UUID]struct{}{
					currentID: {},
				}
				break
			} else if parentOfCurrentIDResult.(string) == "" {
				// no parent, we found the root
				traceIDs = map[uuid.UUID]struct{}{
					currentID: {},
				}
				break
			} else if parentOfCurrentIDResult.(string) != "" {
				parentUUID, err := uuid.Parse(parentOfCurrentIDResult.(string))
				if err != nil {
					return nil, newError(CodeInvalidInput, "invalid parent ID format", nil)
				}
				currentID = parentUUID
			} else {
				return nil, newError(CodeInternal, "failed to get parent ID from Redis", err)
			}
		}
	}
	return traceIDs, nil
}
