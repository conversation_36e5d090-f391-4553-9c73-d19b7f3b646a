package ingestion_test

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/ingestion"
	"langchain.com/smith/queue"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/testutil"
	"langchain.com/smith/testutil/leak"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
	"langchain.com/smith/util"
)

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "DELETE FROM tracer_session; DELETE FROM organizations; DELETE FROM users;")
	assert.NoError(t, err)
}

func TestEnsureSessionsBeforeQueueRunPayload(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	// Set up the database and Redis connections
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	// Create a test organization and tenant
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	// Define test payloads
	existingSessionID := uuid.New()
	existingSession := tracer_sessions.TracerSessionCreate{
		TracerSessionBase: tracer_sessions.TracerSessionBase{
			ID:        existingSessionID,
			StartTime: time.Now().UTC(),
			Name:      "Existing Session",
		},
	}

	client := tracer_sessions.NewTracerSessionsClient(dbpool, cachingRedisPool)
	assert.NotNil(t, client)
	usageLimitsClient := usage_limits.NewUsageLimitsClient(dbpool, *routedRedisPools, cachingRedisPool)
	assert.NotNil(t, usageLimitsClient)

	// Create the existing session in the database
	_, err := client.CreateTracerSession(ctx, authInfo, existingSession, false)
	assert.NoError(t, err)

	payloads := []ingestion.QueuePayload{
		{
			HashKey:     "post",
			SessionID:   nil,
			SessionName: nil,
			StartTime:   nil,
		},
		{
			HashKey:     "post",
			SessionID:   nil,
			SessionName: util.StringPtr("Test Session"),
			StartTime:   util.StringPtr(time.Now().UTC().Format(time.RFC3339)),
		},
		{
			HashKey:     "post",
			SessionID:   &existingSessionID,
			SessionName: nil,
			StartTime:   util.StringPtr(time.Now().Add(-time.Hour).UTC().Format(time.RFC3339)),
		},
	}

	// Call the function under test
	err = ingestion.EnsureSessionsBeforeQueueRunPayload(ctx, dbpool, usageLimitsClient, client, authInfo, payloads, nil)
	assert.NoError(t, err)

	// Verify that sessions have been created or fetched
	for _, payload := range payloads {
		if payload.HashKey != "post" {
			continue
		}

		var session tracer_sessions.TracerSessionWithoutVirtualFields
		if payload.SessionID != nil {
			// Fetch by SessionID
			err = dbpool.QueryRow(ctx, `
                SELECT id, tenant_id, name, start_time, trace_tier
                FROM tracer_session
                WHERE tenant_id = $1 AND id = $2
            `, authInfo.TenantID, payload.SessionID).Scan(
				&session.ID,
				&session.TenantID,
				&session.Name,
				&session.StartTime,
				&session.TraceTier,
			)
			assert.NoError(t, err)
			assert.Equal(t, authInfo.TenantID, session.TenantID.String())
			assert.Equal(t, payload.SessionID.String(), session.ID.String())
		} else if payload.SessionName != nil {
			// Fetch by SessionName
			err = dbpool.QueryRow(ctx, `
                SELECT id, tenant_id, name, start_time, trace_tier
                FROM tracer_session
                WHERE tenant_id = $1 AND name = $2
            `, authInfo.TenantID, *payload.SessionName).Scan(
				&session.ID,
				&session.TenantID,
				&session.Name,
				&session.StartTime,
				&session.TraceTier,
			)
			assert.NoError(t, err)
			assert.Equal(t, authInfo.TenantID, session.TenantID.String())
			assert.Equal(t, *payload.SessionName, session.Name)
		} else {
			// Fetch the default session
			err = dbpool.QueryRow(ctx, `
                SELECT id, tenant_id, name, start_time, trace_tier
                FROM tracer_session
                WHERE tenant_id = $1 AND name = 'default'
            `, authInfo.TenantID).Scan(
				&session.ID,
				&session.TenantID,
				&session.Name,
				&session.StartTime,
				&session.TraceTier,
			)
			assert.NoError(t, err)
			assert.Equal(t, authInfo.TenantID, session.TenantID.String())
			assert.Equal(t, "default", session.Name)
		}
	}
}

func TestQueueRunPayload(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	// Set up the database and Redis connections
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	// Create a test organization and tenant
	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	// Define test payloads
	runID := uuid.New()
	payload := ingestion.QueuePayload{
		RunID:       runID,
		HashKey:     "post",
		Value:       []byte(`{"test_key":"test_value"}`),
		ContentType: "application/json",
	}

	traceID := uuid.New()
	traceIDs := map[uuid.UUID]struct{}{
		traceID: {},
	}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	// Call the function under test
	err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	assert.NoError(t, err)

	// Verify that the payload has been stored in Redis as expected
	authID := authInfo.TenantID
	pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, runID.String())

	// Check that the HashKey field exists in Redis
	value, err := routedPool.HGet(ctx, pendingKey, payload.HashKey).Result()
	assert.NoError(t, err)
	assert.Equal(t, string(payload.Value), value)

	// Check that received_at is set
	receivedAt, err := routedPool.HGet(ctx, pendingKey, payload.HashKey+"_received_at").Result()
	assert.NoError(t, err)
	assert.NotEmpty(t, receivedAt)

	// Check that content_type is set correctly
	contentType, err := routedPool.HGet(ctx, pendingKey, payload.HashKey+"_content_type").Result()
	assert.NoError(t, err)
	assert.Equal(t, payload.ContentType, contentType)
}

func TestQueueRunPayloadSchema(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	// Set up the database and Redis connections
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	// Store original value and set FFTraceTiersEnabled to true for this test
	origFFTraceTiersEnabled := config.Env.FFTraceTiersEnabled
	config.Env.FFTraceTiersEnabled = true
	defer func() {
		config.Env.FFTraceTiersEnabled = origFFTraceTiersEnabled
	}()

	ctx = context.WithValue(ctx, config.LbTraceCtxKey, "test-trace-context")

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	runID1 := uuid.New()
	runID2 := uuid.New()
	traceID := uuid.New()
	payload1 := ingestion.QueuePayload{
		RunID:         runID1,
		HashKey:       "post",
		Value:         []byte(`{"test_key":"test_value1"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: true,
	}
	payload2 := ingestion.QueuePayload{
		RunID:         runID2,
		HashKey:       "post",
		Value:         []byte(`{"test_key":"test_value2"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: true,
	}
	traceIDs := map[uuid.UUID]struct{}{
		traceID: {},
	}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload1, payload2}, true, false)
	assert.NoError(t, err)

	// Retrieve the enqueued job key from Redis
	queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
	jobKeys, err := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
	assert.NoError(t, err)
	assert.Len(t, jobKeys, 1, "Expected one job to be enqueued")

	jobKey := jobKeys[0]
	jobData, err := queueingRedisClient.Get(ctx, jobKey).Result()
	assert.NoError(t, err)

	var job queue.Job
	err = json.Unmarshal([]byte(jobData), &job)
	assert.NoError(t, err)

	// Verify the job schema matches the expected structure
	expectedFunction := "persist_batched_runs"
	expectedQueue := config.Env.IngestionQueue
	expectedTimeout := queue.JobTimeout
	expectedRetries := queue.MaxIngestAttempts
	expectedRetryDelay := queue.RetryDelay

	assert.Equal(t, expectedFunction, job.Function)
	assert.Equal(t, expectedQueue, job.Queue)
	assert.Equal(t, expectedTimeout, job.Timeout)
	assert.Equal(t, expectedRetries, job.Retries)
	assert.Equal(t, expectedRetryDelay, job.RetryDelay)
	assert.True(t, job.RetryBackoff)
	assert.Equal(t, "queued", job.Status)
	assert.NotEmpty(t, job.Key)
	assert.NotZero(t, job.Queued)

	// Verify the kwargs
	runIDs, ok := job.Kwargs["run_ids"].([]interface{})
	assert.True(t, ok, "run_ids should be a list")
	assert.Len(t, runIDs, 2)

	// Verify the format of run_ids based on FFTraceTiersEnabled
	if config.Env.FFTraceTiersEnabled {
		// For FFTraceTiersEnabled=true, run_ids should be nested lists
		for _, runIDEntry := range runIDs {
			entry, ok := runIDEntry.([]interface{})
			assert.True(t, ok, "Each run_id entry should be a list when FFTraceTiersEnabled=true")
			assert.Len(t, entry, 2)
			traceIDStr, ok1 := entry[0].(string)
			runIDStr, ok2 := entry[1].(string)
			assert.True(t, ok1 && ok2, "TraceID and RunID should be strings")
			assert.Equal(t, traceID.String(), traceIDStr)
			assert.Contains(t, []string{runID1.String(), runID2.String()}, runIDStr)
		}
	} else {
		// For FFTraceTiersEnabled=false, run_ids should be simple strings
		for _, runID := range runIDs {
			runIDStr, ok := runID.(string)
			assert.True(t, ok, "Each run_id should be a string when FFTraceTiersEnabled=false")
			assert.Contains(t, []string{runID1.String(), runID2.String()}, runIDStr)
		}
	}

	lbTraceContext, ok := job.Kwargs["lb_trace_context"].(string)
	assert.True(t, ok, "lb_trace_context should be a string")
	assert.Equal(t, "test-trace-context", lbTraceContext)

	tenantIDFromKwargs, ok := job.Kwargs["tenant_id"].(string)
	assert.True(t, ok, "tenant_id should be a string")
	assert.Equal(t, authInfo.TenantID, tenantIDFromKwargs)
}

func TestQueueRunPayloadQueuedSet(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	// Set up the database and Redis connections
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	traceID1 := uuid.New()
	traceID2 := uuid.New()
	runID1 := uuid.New()
	runID2 := uuid.New()

	payloads := []ingestion.QueuePayload{
		{
			RunID:         runID1,
			HashKey:       "post",
			Value:         []byte(`{"test":"value1"}`),
			ContentType:   "application/json",
			TraceID:       &traceID1,
			ProcessInline: false,
		},
		{
			RunID:         runID2,
			HashKey:       "post",
			Value:         []byte(`{"test":"value2"}`),
			ContentType:   "application/json",
			TraceID:       &traceID2,
			ProcessInline: false,
		},
	}

	traceIDs := map[uuid.UUID]struct{}{
		traceID1: {},
		traceID2: {},
	}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, payloads, true, false)
	assert.NoError(t, err)

	// Verify that the trace IDs were added to the queued set
	queuedRunSetForAuth := ingestion.QueuedRunsKey(authInfo.TenantID)
	members, err := routedPool.SMembers(ctx, queuedRunSetForAuth).Result()
	assert.NoError(t, err)
	assert.Len(t, members, 2, "Expected 2 members in the queued set")

	memberUUIDs := make([]uuid.UUID, 0, len(members))
	for _, member := range members {
		id, err := uuid.Parse(member)
		assert.NoError(t, err)
		memberUUIDs = append(memberUUIDs, id)
	}

	assert.Contains(t, memberUUIDs, traceID1)
	assert.Contains(t, memberUUIDs, traceID2)

	ttl, err := routedPool.TTL(ctx, queuedRunSetForAuth).Result()
	assert.NoError(t, err)
	assert.True(t, ttl > 0, "Expected TTL to be set")
	assert.True(t, ttl <= ingestion.QueueRunSetExpiration, "Expected TTL to be less than or equal to QueueRunSetExpiration")
}

func TestQueueRunPayloadHandlesNoscriptError(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	runID := uuid.New()
	traceID := uuid.New()
	payload := ingestion.QueuePayload{
		RunID:         runID,
		HashKey:       "post",
		Value:         []byte(`{"test":"value"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: true,
	}

	traceIDs := map[uuid.UUID]struct{}{
		traceID: {},
	}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	err := queueingRedisClient.ScriptFlush(ctx).Err()
	assert.NoError(t, err)

	err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	assert.NoError(t, err)

	pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, runID)
	value, err := routedPool.HGet(ctx, pendingKey, payload.HashKey).Result()
	assert.NoError(t, err)
	assert.Equal(t, string(payload.Value), value)

	contentType, err := routedPool.HGet(ctx, pendingKey, payload.HashKey+"_content_type").Result()
	assert.NoError(t, err)
	assert.Equal(t, payload.ContentType, contentType)

	receivedAt, err := routedPool.HGet(ctx, pendingKey, payload.HashKey+"_received_at").Result()
	assert.NoError(t, err)
	assert.NotEmpty(t, receivedAt)

	queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
	jobKeys, err := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
	assert.NoError(t, err)
	assert.NotEmpty(t, jobKeys, "Expected at least one job to be enqueued")

	jobKey := jobKeys[0]
	jobData, err := queueingRedisClient.Get(ctx, jobKey).Result()
	assert.NoError(t, err)

	var job queue.Job
	err = json.Unmarshal([]byte(jobData), &job)
	assert.NoError(t, err)
	assert.Equal(t, "persist_batched_runs", job.Function)
}

func TestQueueRunPayload_Validation(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	authInfo := auth.AuthInfo{TenantID: tenantID, TenantConfig: &auth.TenantConfig{}}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	t.Run("InvalidHashKey", func(t *testing.T) {
		payload := ingestion.QueuePayload{
			RunID:       uuid.New(),
			HashKey:     "invalid",
			Value:       []byte(`{}`),
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid value: invalid")
	})

	t.Run("InvalidSetKey", func(t *testing.T) {
		sk := "not-feedback"
		payload := ingestion.QueuePayload{
			RunID:       uuid.New(),
			SetKey:      &sk,
			Value:       []byte(`{}`),
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid value: not-feedback")
	})

	t.Run("ExtraWithSetKey", func(t *testing.T) {
		sk := "feedback"
		extraMap := map[string]ingestion.ExtraValue{
			"foo": {Data: []byte("bar")},
		}
		payload := ingestion.QueuePayload{
			RunID:       uuid.New(),
			SetKey:      &sk,
			Value:       []byte(`{}`),
			Extra:       extraMap,
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "extra is not allowed for set_key")
	})

	t.Run("InvalidContentTypeWithSetKey", func(t *testing.T) {
		sk := "feedback"
		payload := ingestion.QueuePayload{
			RunID:       uuid.New(),
			SetKey:      &sk,
			Value:       []byte(`{}`),
			ContentType: "text/plain",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "content_type must be application/json for set_key")
	})

	t.Run("MissingHashOrSetKey", func(t *testing.T) {
		payload := ingestion.QueuePayload{
			RunID:       uuid.New(),
			Value:       []byte(`{}`),
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "one of hash_key or set_key should be set")
	})

	t.Run("ParentIDEqualsRunID", func(t *testing.T) {
		id := uuid.New()
		payload := ingestion.QueuePayload{
			RunID:       id,
			ParentID:    &id,
			HashKey:     "post",
			Value:       []byte(`{}`),
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "parent_id and run_id cannot be the same")
	})

	t.Run("AlreadyReceivedPayload", func(t *testing.T) {
		// Insert once
		id := uuid.New()
		payload := ingestion.QueuePayload{
			RunID:       id,
			HashKey:     "post",
			Value:       []byte(`{"a":"b"}`),
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.NoError(t, err)

		// Insert again, same run, should error with DuplicateError
		err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "payloads already received")
	})

	t.Run("BatchWithoutTraceIDs", func(t *testing.T) {
		payloads := []ingestion.QueuePayload{
			{RunID: uuid.New(), HashKey: "post", Value: []byte(`{}`), ContentType: "application/json"},
			{RunID: uuid.New(), HashKey: "post", Value: []byte(`{}`), ContentType: "application/json"},
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, payloads, true, false)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "trace_ids must be specified for batch requests")
	})
}

func TestQueueRunPayloadHandlesNoscriptErrorNonInline(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)

	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	runID := uuid.New()
	traceID := uuid.New()
	payload := ingestion.QueuePayload{
		RunID:         runID,
		HashKey:       "post",
		Value:         []byte(`{"test":"value"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: false,
	}

	traceIDs := map[uuid.UUID]struct{}{
		traceID: {},
	}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	err := queueingRedisClient.ScriptFlush(ctx).Err()
	assert.NoError(t, err)

	err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	assert.NoError(t, err)

	pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, runID)
	value, err := routedPool.HGet(ctx, pendingKey, payload.HashKey).Result()

	assert.NoError(t, err)
	assert.Equal(t, string(payload.Value), value)

	contentType, err := routedPool.HGet(ctx, pendingKey, payload.HashKey+"_content_type").Result()
	assert.NoError(t, err)
	assert.Equal(t, payload.ContentType, contentType)

	receivedAt, err := routedPool.HGet(ctx, pendingKey, payload.HashKey+"_received_at").Result()
	assert.NoError(t, err)
	assert.NotEmpty(t, receivedAt)

	queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
	jobKeys, err := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
	assert.NoError(t, err)
	assert.NotEmpty(t, jobKeys, "Expected at least one job to be enqueued")

	jobKey := jobKeys[0]
	jobData, err := queueingRedisClient.Get(ctx, jobKey).Result()
	assert.NoError(t, err)

	var job queue.Job
	err = json.Unmarshal([]byte(jobData), &job)
	assert.NoError(t, err)
	assert.Equal(t, "persist_run_or_parent", job.Function)
}

func TestQueueRunPayload_ChunkedPayloads(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	authInfo := auth.AuthInfo{TenantID: tenantID, TenantConfig: &auth.TenantConfig{}}
	authID := authInfo.TenantID

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	t.Run("PartialDuplicatesRemoved", func(t *testing.T) {
		routedPool.Del(ctx, ingestion.QueuedRunsKey(authID))

		runNew := uuid.New()
		runDup := uuid.New()
		payloads := []ingestion.QueuePayload{
			{
				RunID:       runDup,
				HashKey:     "post",
				Value:       []byte(`{"duplicate":true}`),
				ContentType: "application/json",
			},
			{
				RunID:       runNew,
				HashKey:     "post",
				Value:       []byte(`{"unique":true}`),
				ContentType: "application/json",
			},
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, payloads[:1], false, false)
		assert.NoError(t, err)

		err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, payloads, false, false)

		assert.NoError(t, err, "partial duplicates are removed silently; expect no error")
	})

	t.Run("InlineFeedbackAllInTraceIDs", func(t *testing.T) {
		routedPool.Del(ctx, ingestion.QueuedRunsKey(authID))

		traceIDs := map[uuid.UUID]struct{}{}
		sk := "feedback"
		run1 := uuid.New()
		run2 := uuid.New()
		traceIDs[run1] = struct{}{}
		traceIDs[run2] = struct{}{}

		payloads := []ingestion.QueuePayload{
			{
				RunID:       run1,
				TraceID:     &run1,
				SetKey:      &sk,
				Value:       []byte(`{"foo":1}`),
				ContentType: "application/json",
			},
			{
				RunID:       run2,
				TraceID:     &run2,
				SetKey:      &sk,
				Value:       []byte(`{"bar":2}`),
				ContentType: "application/json",
			},
		}

		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, payloads, false, false)
		assert.NoError(t, err)

		members, err := routedPool.SMembers(ctx, ingestion.QueuedRunsKey(authID)).Result()
		assert.NoError(t, err)
		assert.Empty(t, members, "no new run IDs should be placed in the queued set if inline used")

		for _, r := range []uuid.UUID{run1, run2} {
			pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, r)
			hvals, _ := routedPool.HGetAll(ctx, pendingKey).Result()
			assert.NotEmpty(t, hvals["trace_id"], "trace_id is stored for setKey-based runs")
			assert.Empty(t, hvals["post"], "no 'post' field for feedback runs")
		}
	})

	t.Run("FeedbackNotInTraceIDs", func(t *testing.T) {
		traceID := uuid.New()
		traceIDs := map[uuid.UUID]struct{}{traceID: {}}
		sk := "feedback"
		// One payload has runID = not in traceIDs => allFeedbackForRoot => false => not inline
		payloads := []ingestion.QueuePayload{
			{
				RunID:       uuid.New(),
				SetKey:      &sk,
				Value:       []byte(`{"foo":"bar"}`),
				ContentType: "application/json",
			},
			{
				RunID:       traceID,
				SetKey:      &sk,
				Value:       []byte(`{"baz":123}`),
				ContentType: "application/json",
			},
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, payloads, true, false)
		assert.NoError(t, err)

		// Should have scheduled job for traceIDs since not inline
		queuedSet := ingestion.QueuedRunsKey(authID)
		members, err := routedPool.SMembers(ctx, queuedSet).Result()
		assert.NoError(t, err)
		assert.Contains(t, members, traceID.String())
	})

	t.Run("ChunkingAndUsageLimits", func(t *testing.T) {
		assert.NoError(t, routedPool.Del(ctx, ingestion.QueuedRunsKey(authID)).Err())

		// Force chunk size = 1
		origChunk := config.Env.RedisTransactionRunsChunkSize
		origSelfHosted := config.Env.IsSelfHosted
		config.Env.RedisTransactionRunsChunkSize = 1
		config.Env.IsSelfHosted = false
		defer func() {
			config.Env.RedisTransactionRunsChunkSize = origChunk
			config.Env.IsSelfHosted = origSelfHosted
		}()

		traceID1 := uuid.New()
		traceID2 := uuid.New()
		traceIDs := map[uuid.UUID]struct{}{traceID1: {}, traceID2: {}}

		payloads := []ingestion.QueuePayload{
			{RunID: traceID1, HashKey: "post", Value: []byte(`{"chunk1":true}`), ContentType: "application/json"},
			{RunID: traceID2, HashKey: "post", Value: []byte(`{"chunk2":true}`), ContentType: "application/json"},
		}

		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, payloads, true, false)
		assert.NoError(t, err)

		queuedSet := ingestion.QueuedRunsKey(authID)
		members, err := routedPool.SMembers(ctx, queuedSet).Result()
		assert.NoError(t, err)
		assert.Len(t, members, 2, "should contain the 2 new run IDs")
	})

	t.Run("AllInQueuedSetSkipsScheduling", func(t *testing.T) {
		traceID := uuid.New()
		queuedSet := ingestion.QueuedRunsKey(authID)
		_ = routedPool.SAdd(ctx, queuedSet, traceID.String()).Err()

		payload := ingestion.QueuePayload{
			RunID:       traceID,
			HashKey:     "post",
			Value:       []byte(`{"noop":true}`),
			ContentType: "application/json",
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, map[uuid.UUID]struct{}{traceID: {}}, []ingestion.QueuePayload{payload}, false, false)
		assert.NoError(t, err)

		queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
		jobKeys, _ := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
		for _, k := range jobKeys {
			jobData, _ := queueingRedisClient.Get(ctx, k).Result()
			assert.NotContains(t, jobData, traceID.String(), "No new job for the already-queued traceID")
		}
	})

	t.Run("PartialQueuedSchedulesRemainder", func(t *testing.T) {
		trace1 := uuid.New()
		trace2 := uuid.New()
		queuedSet := ingestion.QueuedRunsKey(authID)
		_ = routedPool.SAdd(ctx, queuedSet, trace1.String()).Err()

		payloads := []ingestion.QueuePayload{
			{RunID: trace1, HashKey: "post", Value: []byte(`{"foo":1}`), ContentType: "application/json"},
			{RunID: trace2, HashKey: "post", Value: []byte(`{"bar":2}`), ContentType: "application/json"},
		}
		traceIDs := map[uuid.UUID]struct{}{trace1: {}, trace2: {}}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, payloads, false, false)
		assert.NoError(t, err)

		queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
		jobKeys, err := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
		assert.NoError(t, err)

		foundTrace2 := false
		for _, k := range jobKeys {
			jobData, _ := queueingRedisClient.Get(ctx, k).Result()
			if strings.Contains(jobData, trace2.String()) {
				foundTrace2 = true
				break
			}
		}
		assert.True(t, foundTrace2, "trace2 must be scheduled")
	})

	t.Run("FFTraceTiersEnabled", func(t *testing.T) {
		orig := config.Env.FFTraceTiersEnabled
		config.Env.FFTraceTiersEnabled = true
		defer func() { config.Env.FFTraceTiersEnabled = orig }()

		traceID := uuid.New()
		payload := ingestion.QueuePayload{
			RunID:       uuid.New(),
			HashKey:     "post",
			Value:       []byte(`{}`),
			ContentType: "application/json",
			TraceID:     &traceID,
		}
		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, map[uuid.UUID]struct{}{traceID: {}}, []ingestion.QueuePayload{payload}, false, false)
		assert.NoError(t, err)

		tracePendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, traceID.String())
		val, err := routedPool.HGet(ctx, tracePendingKey, "trace_first_received_at").Result()
		assert.NoError(t, err)
		assert.NotEmpty(t, val)
	})
	t.Run("ExtrasAndParentID", func(t *testing.T) {
		routedPool.Del(ctx, ingestion.QueuedRunsKey(authID))

		parentID := uuid.New()
		childID := uuid.New()

		extras := map[string]ingestion.ExtraValue{
			"withEncoding": {
				ContentType: "image/png",
				Encoding:    "",
				Data:        []byte("iVBORw0KGgoAAAANSUhEUgAAAAEAAAAB"),
			},
			"noEncoding": {
				Data: []byte("raw-data-here"),
			},
		}

		payload := ingestion.QueuePayload{
			RunID:       childID,
			HashKey:     "post",
			Value:       []byte(`{"child":"data"}`),
			ContentType: "application/json",
			ParentID:    &parentID,
			Extra:       extras,
		}

		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, nil, []ingestion.QueuePayload{payload}, false, false)
		require.NoError(t, err, "QueueRunPayload should succeed with valid base64 extras")

		pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authID, childID)
		parentField, err := routedPool.HGet(ctx, pendingKey, "parent").Result()
		require.NoError(t, err)
		assert.NotEmpty(t, parentField, "parent field should be stored")

		childrenKey := fmt.Sprintf("smith:runs:children:%s:%s", authID, parentID)
		members, err := routedPool.SMembers(ctx, childrenKey).Result()
		require.NoError(t, err)
		assert.Contains(t, members, fmt.Sprintf(`"%s"`, childID), "child run should be in parent's children set")

		extraKey := fmt.Sprintf("%s:extra", pendingKey)
		storedExtras, err := routedPool.HGetAll(ctx, extraKey).Result()
		require.NoError(t, err)

		withEncData := storedExtras["withEncoding"]
		withEncMethod := storedExtras["withEncoding_compression_method"]
		withEncCT := storedExtras["withEncoding_content_type"]
		assert.NotEmpty(t, withEncData, "compressed data for 'withEncoding'")
		assert.NotEmpty(t, withEncMethod, "compression method for 'withEncoding'")
		assert.Equal(t, "image/png", withEncCT, "content type for 'withEncoding' should match")

		noEncData := storedExtras["noEncoding"]
		noEncS3url := storedExtras["noEncoding_s3_url"]
		assert.True(t, noEncData != "" || noEncS3url != "",
			"one of noEncoding or noEncoding_s3_url should be set (the code picks one branch)")
	})

	t.Run("MultipleRunsSameTraceID", func(t *testing.T) {
		routedPool.Del(ctx, ingestion.QueuedRunsKey(authID))

		traceIDs := map[uuid.UUID]struct{}{}
		sk := "feedback"
		run1 := uuid.New()
		run2 := uuid.New()
		traceIDs[run1] = struct{}{}

		payloads := []ingestion.QueuePayload{
			{
				RunID:       run1,
				TraceID:     &run1,
				SetKey:      &sk,
				Value:       []byte(`{"foo":1}`),
				ContentType: "application/json",
			},
			{
				RunID:       run2,
				TraceID:     &run1,
				SetKey:      &sk,
				Value:       []byte(`{"bar":2}`),
				ContentType: "application/json",
			},
		}

		err := ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, payloads, false, false)
		assert.NoError(t, err)

		// Verify that the trace ID was added to the queued set
		queuedRunSetForAuth := ingestion.QueuedRunsKey(authInfo.TenantID)
		members, err := routedPool.SMembers(ctx, queuedRunSetForAuth).Result()
		assert.NoError(t, err)
		assert.Len(t, members, 1, "Expected 1 member in the queued set")

		memberUUIDs := make([]uuid.UUID, 0, len(members))
		for _, member := range members {
			id, err := uuid.Parse(member)
			assert.NoError(t, err)
			memberUUIDs = append(memberUUIDs, id)
		}

		assert.Contains(t, memberUUIDs, run1)

		ttl, err := routedPool.TTL(ctx, queuedRunSetForAuth).Result()
		assert.NoError(t, err)
		assert.True(t, ttl > 0, "Expected TTL to be set")
		assert.True(t, ttl <= ingestion.QueueRunSetExpiration, "Expected TTL to be less than or equal to QueueRunSetExpiration")

		for _, r := range []uuid.UUID{run1, run2} {
			pendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, r)
			hvals, _ := routedPool.HGetAll(ctx, pendingKey).Result()
			assert.NotEmpty(t, hvals["trace_id"], "trace_id is stored for setKey-based runs")
			assert.Empty(t, hvals["post"], "no 'post' field for feedback runs")
		}
	})
}

func TestQueueRunPayload_TraceLimitExceeded(t *testing.T) {
	defer leak.VerifyNoLeak(t)
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	// Save original value and set FFTraceTiersEnabled to true for this test
	origFFTraceTiersEnabled := config.Env.FFTraceTiersEnabled
	config.Env.FFTraceTiersEnabled = true
	defer func() {
		config.Env.FFTraceTiersEnabled = origFFTraceTiersEnabled
	}()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	authInfo := auth.AuthInfo{
		TenantID:     tenantID,
		TenantConfig: &auth.TenantConfig{},
	}

	traceID := uuid.New()
	traceIDs := map[uuid.UUID]struct{}{
		traceID: {},
	}

	config.Env.MaxTraceLimit = 1

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	traceRunsKey := fmt.Sprintf("smith:runs:trace_runs:%s:%s", tenantID, traceID.String())
	maxLimit := int(config.Env.MaxTraceLimit)
	for i := 0; i < maxLimit; i++ {
		fakeRunID := uuid.New()
		runIDJson, err := json.Marshal(fakeRunID.String())
		require.NoError(t, err)
		err = routedPool.SAdd(ctx, traceRunsKey, runIDJson).Err()
		require.NoError(t, err)
	}

	count, err := routedPool.SCard(ctx, traceRunsKey).Result()
	require.NoError(t, err)
	require.Equal(t, int64(maxLimit), count)

	newRunID := uuid.New()
	payload := ingestion.QueuePayload{
		RunID:         newRunID,
		HashKey:       "post",
		Value:         []byte(`{"test_key": "test_value"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: true,
	}

	err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "trace run limit exceeded")
}

func TestEnqueueInlineProcessing(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// Set up the database and Redis connections
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	authInfo := auth.AuthInfo{TenantID: tenantID, TenantConfig: &auth.TenantConfig{}}

	// Create test payloads
	runID1 := uuid.New()
	traceID1 := uuid.New()
	runID2 := uuid.New()
	traceID2 := uuid.New()

	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	payloads := []ingestion.QueuePayload{
		{
			RunID:       runID1,
			TraceID:     &traceID1,
			HashKey:     "post",
			Value:       []byte(`{"test":"value1"}`),
			ContentType: "application/json",
		},
		{
			RunID:       runID2,
			TraceID:     &traceID2,
			HashKey:     "post",
			Value:       []byte(`{"test":"value2"}`),
			ContentType: "application/json",
		},
	}

	t.Run("WithFFTraceTiersEnabled=false", func(t *testing.T) {
		// Save original value and restore after test
		origValue := config.Env.FFTraceTiersEnabled
		config.Env.FFTraceTiersEnabled = false
		defer func() {
			config.Env.FFTraceTiersEnabled = origValue
		}()

		// Clear the Redis queue before test
		queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
		err := queueingRedisClient.Del(ctx, queueKey).Err()
		assert.NoError(t, err)

		// Enqueue the job
		pipe := queueingRedisClient.Pipeline()
		err = ingestion.EnqueueInlineProcessing(ctx, pipe, payloads, authInfo)
		assert.NoError(t, err)
		_, err = pipe.Exec(ctx)
		assert.NoError(t, err)

		// Retrieve the enqueued job
		jobKeys, err := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
		assert.NoError(t, err)
		assert.Len(t, jobKeys, 1, "Expected one job to be enqueued")

		jobKey := jobKeys[0]
		jobData, err := queueingRedisClient.Get(ctx, jobKey).Result()
		assert.NoError(t, err)

		var job queue.Job
		err = json.Unmarshal([]byte(jobData), &job)
		assert.NoError(t, err)

		// Verify job details
		assert.Equal(t, "persist_batched_runs", job.Function)

		// Verify run_ids format: they should be simple strings, not arrays
		runIDs, ok := job.Kwargs["run_ids"].([]interface{})
		assert.True(t, ok, "run_ids should be a list")
		assert.Len(t, runIDs, 2, "Expected two run IDs")

		// For each run ID, verify that it's a string, not an array
		for _, runID := range runIDs {
			strRunID, ok := runID.(string)
			assert.True(t, ok, "Each run_id should be a string when FFTraceTiersEnabled=false")
			assert.True(t, strRunID == runID1.String() || strRunID == runID2.String(),
				"Run ID should match one of the provided run IDs")
		}
	})

	t.Run("WithFFTraceTiersEnabled=true", func(t *testing.T) {
		// Save original value and restore after test
		origValue := config.Env.FFTraceTiersEnabled
		config.Env.FFTraceTiersEnabled = true
		defer func() {
			config.Env.FFTraceTiersEnabled = origValue
		}()

		// Clear the Redis queue before test
		queueKey := fmt.Sprintf("saq:%s:queued", config.Env.IngestionQueue)
		err := queueingRedisClient.Del(ctx, queueKey).Err()
		assert.NoError(t, err)

		// Enqueue the job
		pipe := queueingRedisClient.Pipeline()
		err = ingestion.EnqueueInlineProcessing(ctx, pipe, payloads, authInfo)
		assert.NoError(t, err)
		_, err = pipe.Exec(ctx)
		assert.NoError(t, err)

		// Retrieve the enqueued job
		jobKeys, err := queueingRedisClient.LRange(ctx, queueKey, 0, -1).Result()
		assert.NoError(t, err)
		assert.Len(t, jobKeys, 1, "Expected one job to be enqueued")

		jobKey := jobKeys[0]
		jobData, err := queueingRedisClient.Get(ctx, jobKey).Result()
		assert.NoError(t, err)

		var job queue.Job
		err = json.Unmarshal([]byte(jobData), &job)
		assert.NoError(t, err)

		// Verify job details
		assert.Equal(t, "persist_batched_runs", job.Function)

		// Verify run_ids format: they should be arrays with trace ID and run ID
		runIDs, ok := job.Kwargs["run_ids"].([]interface{})
		assert.True(t, ok, "run_ids should be a list")
		assert.Len(t, runIDs, 2, "Expected two run ID entries")

		// For each run ID, verify that it's an array [traceID, runID]
		for _, runIDEntry := range runIDs {
			entry, ok := runIDEntry.([]interface{})
			assert.True(t, ok, "Each run_id entry should be a list when FFTraceTiersEnabled=true")
			assert.Len(t, entry, 2, "Each entry should have 2 elements (trace ID and run ID)")

			traceIDStr, ok1 := entry[0].(string)
			runIDStr, ok2 := entry[1].(string)
			assert.True(t, ok1 && ok2, "TraceID and RunID should be strings")

			// Check that traceIDs and runIDs match one of our test payloads
			validPair1 := traceIDStr == traceID1.String() && runIDStr == runID1.String()
			validPair2 := traceIDStr == traceID2.String() && runIDStr == runID2.String()
			assert.True(t, validPair1 || validPair2,
				"Trace ID and Run ID should match one of the provided pairs")
		}
	})
}

func TestQueueRunPayloadWithAutoUpgradeFalse(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// Set up the database and Redis connections
	dbpool := database.PgConnect()
	defer DbCleanup(t, dbpool)

	routedRedisPools, cachingRedisPool := testutil.InitTestRedisClients(t)
	defer testutil.CleanupTestRoutedRedisPools(t, routedRedisPools, true)
	defer testutil.CleanupTestRedisClient(t, cachingRedisPool, true)

	ctx := context.Background()

	// Reset MaxTraceLimit to default
	origMaxTraceLimit := config.Env.MaxTraceLimit
	config.Env.MaxTraceLimit = 1000 // Use a large value to avoid hitting limit
	defer func() {
		config.Env.MaxTraceLimit = origMaxTraceLimit
	}()

	// Enable FFTraceTiersEnabled to test auto-upgrade behavior
	origFFTraceTiersEnabled := config.Env.FFTraceTiersEnabled
	config.Env.FFTraceTiersEnabled = true
	defer func() {
		config.Env.FFTraceTiersEnabled = origFFTraceTiersEnabled
	}()

	orgID := testutil.OrgSetup(t, dbpool, "Test Org", false, uuid.New().String())
	tenantID := testutil.TenantSetup(t, dbpool, orgID, "Test Tenant", "test-tenant", &auth.TenantConfig{}, true)
	authInfo := auth.AuthInfo{TenantID: tenantID, TenantConfig: &auth.TenantConfig{}}

	routedPool := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := routedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)
	queue.InitializeScripts(context.Background(), queueingRedisClient)

	// Clear Redis before test
	traceID := uuid.New()
	tracePendingKey := fmt.Sprintf("smith:runs:pending:%s:%s", authInfo.TenantID, traceID.String())
	traceRunsKey := fmt.Sprintf("smith:runs:trace_runs:%s:%s", authInfo.TenantID, traceID.String())
	err := routedPool.Del(ctx, tracePendingKey).Err()
	assert.NoError(t, err)
	err = routedPool.Del(ctx, traceRunsKey).Err()
	assert.NoError(t, err)

	runID := uuid.New()
	payload := ingestion.QueuePayload{
		RunID:         runID,
		HashKey:       "post",
		Value:         []byte(`{"test":"value"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: true,
		AutoUpgrade:   false, // Set to false as per the code change
	}

	traceIDs := map[uuid.UUID]struct{}{
		traceID: {},
	}

	// Queue the payload
	err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	assert.NoError(t, err)

	// Verify that upgrade_trace_tier was not set in Redis
	exists, err := routedPool.HExists(ctx, tracePendingKey, "upgrade_trace_tier").Result()
	assert.NoError(t, err)
	assert.False(t, exists, "upgrade_trace_tier should not exist when AutoUpgrade is false")

	// Now try with AutoUpgrade set to true
	runID2 := uuid.New()
	payload2 := ingestion.QueuePayload{
		RunID:         runID2,
		HashKey:       "post",
		Value:         []byte(`{"test":"value2"}`),
		ContentType:   "application/json",
		TraceID:       &traceID,
		ProcessInline: true,
		AutoUpgrade:   true, // Set to true to compare behavior
	}

	// Queue the second payload
	err = ingestion.QueueRunPayload(ctx, routedPool, queueingRedisClient, authInfo, traceIDs, []ingestion.QueuePayload{payload2}, true, false)
	assert.NoError(t, err)

	// Verify that upgrade_trace_tier was set in Redis
	exists, err = routedPool.HExists(ctx, tracePendingKey, "upgrade_trace_tier").Result()
	assert.NoError(t, err)
	assert.True(t, exists, "upgrade_trace_tier should exist when AutoUpgrade is true")

	// Verify the value of upgrade_trace_tier
	upgradeTier, err := routedPool.HGet(ctx, tracePendingKey, "upgrade_trace_tier").Result()
	assert.NoError(t, err)
	assert.Contains(t, upgradeTier, "longlived", "upgrade_trace_tier should be set to longlived")
}
