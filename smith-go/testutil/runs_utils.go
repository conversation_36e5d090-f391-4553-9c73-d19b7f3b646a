package testutil

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"langchain.com/smith/runs"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/stretchr/testify/assert"
)

type InsertRun struct {
	Run                  runs.Run
	IsRoot               bool
	PromptTokens         int
	CompletionTokens     int
	TotalTokens          int
	FirstTokenTime       *string
	ModifiedAt           *string
	PromptCost           float64
	CompletionCost       float64
	TotalCost            float64
	InputTokens          int
	OutputTokens         int
	InputSize            int
	OutputSize           int
	TraceTier            *string
	TraceUpgrade         bool
	TraceFirstReceivedAt *string
}

// Inserts a run into clickhouse
func InsertRunIntoClickhouse(t *testing.T, ch clickhouse.Conn, tenantId string, sessionId string, run InsertRun) error {
	runsQuery := `
		INSERT INTO runs (
			id,
			tenant_id,
			name,
			start_time,
			end_time,
			extra,
			error,
			is_root,
			run_type,
			inputs,
			outputs,
			session_id,
			parent_run_id,
			events,
			tags,
			status,
			trace_id,
			dotted_order,
			prompt_tokens,
			completion_tokens,
			total_tokens,
			first_token_time,
			modified_at,
			prompt_cost,
			completion_cost,
			total_cost,
			input_tokens,
			output_tokens,
			input_size,
			output_size,
			trace_tier,
			trace_upgrade,
			trace_first_received_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33
		)
	`
	extraJSON, err := json.Marshal(run.Run.Extra)
	if err != nil {
		return fmt.Errorf("failed to marshal extra field: %w", err)
	}
	extraString := string(extraJSON)
	err = ch.Exec(context.Background(), runsQuery, run.Run.ID, tenantId, run.Run.Name, run.Run.StartTime, run.Run.EndTime, extraString, run.Run.Error, run.IsRoot, run.Run.RunType, run.Run.Inputs, run.Run.Outputs, sessionId, run.Run.ParentRunID, run.Run.Events, run.Run.Tags, run.Run.Status, run.Run.TraceID, run.Run.DottedOrder, run.PromptTokens, run.CompletionTokens, run.TotalTokens, run.FirstTokenTime, run.ModifiedAt, run.PromptCost, run.CompletionCost, run.TotalCost, run.InputTokens, run.OutputTokens, run.InputSize, run.OutputSize, run.TraceTier, run.TraceUpgrade, run.TraceFirstReceivedAt)
	assert.NoError(t, err)
	return nil
}
