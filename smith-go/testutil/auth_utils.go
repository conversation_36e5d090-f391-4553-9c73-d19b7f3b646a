package testutil

import (
	"context"
	"log/slog"
	"net/http"
	"sort"
	"testing"

	"github.com/go-chi/httplog/v2"
	"github.com/jackc/pgx/v5"
	"github.com/neilotoole/slogt"
	"github.com/stretchr/testify/assert"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/usage_limits"
)

var ADMIN_PERMISSIONS = []string{
	"annotation-queues:create", "annotation-queues:delete", "annotation-queues:read", "annotation-queues:update",
	"charts:create", "charts:delete", "charts:read", "charts:update",
	"datasets:create", "datasets:delete", "datasets:read", "datasets:share", "datasets:update",
	"deployments:create", "deployments:delete", "deployments:read", "deployments:update",
	"feedback:create", "feedback:delete", "feedback:read", "feedback:update",
	"projects:create", "projects:delete", "projects:read", "projects:update",
	"prompts:create", "prompts:delete", "prompts:read", "prompts:share", "prompts:update",
	"repos:create", "repos:delete", "repos:read", "repos:share", "repos:update",
	"rules:create", "rules:delete", "rules:read", "rules:update",
	"run-rules:create", "run-rules:delete", "run-rules:read", "run-rules:update",
	"runs:create", "runs:delete", "runs:read", "runs:share",
	"workspaces:manage", "workspaces:read",
}
var ORG_PERMISSIONS = []string{"organization:manage", "organization:read"}
var ORG_USER_PERMISSIONS = []string{"organization:read"}
var ALL_PERMISSIONS []string

var READ_ONLY_PERMISSIONS = []string{
	"annotation-queues:read",
	"charts:read",
	"datasets:read",
	"deployments:read",
	"feedback:read",
	"projects:read",
	"prompts:read",
	"repos:read",
	"rules:read",
	"run-rules:read",
	"runs:read",
	"workspaces:read",
}

func UtilsInit() {
	ALL_PERMISSIONS = append(ADMIN_PERMISSIONS, ORG_PERMISSIONS...)
	sort.Strings(ALL_PERMISSIONS)
}

func DbCleanup(t *testing.T, dbpool *database.AuditLoggedPool) {
	defer dbpool.Close()
	_, err := dbpool.Exec(context.Background(), "delete from organizations; delete from users; delete from alert_rules; delete from alert_actions; delete from tracer_session; delete from tenants;")
	assert.NoError(t, err)
}

func OrgSetup(t *testing.T, dbpool *database.AuditLoggedPool, orgName string, isPersonal bool, createdByUserId string) string {
	var orgID string
	query := `
		insert into organizations (display_name, is_personal, created_by_user_id)
		values ($1, $2, $3)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, orgName, isPersonal, createdByUserId).Scan(&orgID)
	assert.NoError(t, err)
	return orgID
}

func SamlProviderSetup(t *testing.T, dbpool *database.AuditLoggedPool, orgId string, metadataUrl string, defaultWorkspaceIds []string, defaultWsRoleName string) string {
	var samlProviderId string
	query := `
		insert into saml_providers (provider_id, organization_id, metadata_url, default_workspace_ids, default_workspace_role_id)
		values ((select gen_random_uuid()), $1, $2, $3, (select id from roles where name = $4))
		returning provider_id
    `
	err := dbpool.QueryRow(context.Background(), query, orgId, metadataUrl, defaultWorkspaceIds, defaultWsRoleName).Scan(&samlProviderId)
	assert.NoError(t, err)
	return samlProviderId
}

func TenantSetup(t *testing.T, dbpool *database.AuditLoggedPool, orgId string, name string, handle string, config *auth.TenantConfig, isEnabled bool) string {
	var tenantID string
	query := `
		insert into tenants (organization_id, display_name, tenant_handle, config, is_enabled)
		values ($1, $2, $3, $4, $5)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, orgId, name, handle, config, isEnabled).Scan(&tenantID)
	assert.NoError(t, err)
	return tenantID
}

func UserSetup(t *testing.T, dbpool *database.AuditLoggedPool, userId string, email string, fullName string, provider string, samlProviderId interface{}, providerUserId *string) auth.ProviderUserInfo {
	row, _ := dbpool.Query(
		context.Background(),
		auth.SafeInsertUserAndProviderUserQuery,
		userId,
		email,
		fullName,
		provider,
		samlProviderId,
		providerUserId,
	)
	providerInfo, err := pgx.CollectExactlyOneRow(row, pgx.RowToAddrOfStructByPos[auth.ProviderUserInfo])
	assert.NoError(t, err)
	return *providerInfo
}

func IdentitySetup(t *testing.T, dbpool *database.AuditLoggedPool, userId string, orgId string, tenantId *string, roleName string, accessScope string, parentIdentityId *string, lsUserId string) string {
	var identityId string
	query := `
		insert into identities (user_id, organization_id, tenant_id, role_id, access_scope, parent_identity_id, ls_user_id)
		values ($1, $2, $3, (select id from roles where name = $4), $5, $6, $7)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, userId, orgId, tenantId, roleName, accessScope, parentIdentityId, lsUserId).Scan(&identityId)
	assert.NoError(t, err)
	return identityId
}

func ApiKeySetup(t *testing.T, dbpool *database.AuditLoggedPool, apiKey string, tenantId string, identityId string, shortKey string, orgId string, userId string, lsUserId string) string {
	var apiKeyId string
	query := `
		insert into api_keys (id, api_key, tenant_id, identity_id, short_key, organization_id, user_id, ls_user_id)
		values (gen_random_uuid(), $1, $2, $3, $4, $5, $6, $7)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, apiKey, tenantId, identityId, shortKey, orgId, userId, lsUserId).Scan(&apiKeyId)
	assert.NoError(t, err)
	return apiKeyId
}

func ServiceIdentitySetup(t *testing.T, dbpool *database.AuditLoggedPool, orgId string, tenantId *string, roleName string, accessScope string, serviceAccountId string, parentIdentityId *string) string {
	var identityId string
	query := `
		insert into identities (organization_id, tenant_id, role_id, access_scope, service_account_id, parent_identity_id)
		values ($1, $2, (select id from roles where name = $3), $4, $5, $6)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, orgId, tenantId, roleName, accessScope, serviceAccountId, parentIdentityId).Scan(&identityId)
	assert.NoError(t, err)
	return identityId
}

func ServiceApiKeySetup(t *testing.T, dbpool *database.AuditLoggedPool, apiKey string, tenantId string, identityId string, shortKey string, serviceAccountId string, orgId string) string {
	var apiKeyId string
	query := `
		insert into api_keys (id, api_key, tenant_id, identity_id, short_key, service_account_id, organization_id)
		values (gen_random_uuid(), $1, $2, $3, $4, $5, $6)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, apiKey, tenantId, identityId, shortKey, serviceAccountId, orgId).Scan(&apiKeyId)
	assert.NoError(t, err)
	return apiKeyId
}

func ServiceAccountSetup(t *testing.T, dbpool *database.AuditLoggedPool, name string, orgId string) string {
	var serviceAccountId string
	query := `
		insert into service_accounts (id, name, organization_id)
		values (gen_random_uuid(), $1, $2)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, name, orgId).Scan(&serviceAccountId)
	assert.NoError(t, err)
	return serviceAccountId
}

func TracerSessionSetup(t *testing.T, dbpool *database.AuditLoggedPool, tenantId string) string {
	var tracerSessionId string
	query := `
	  insert into tracer_session (id, name, tenant_id, trace_tier)
		values (gen_random_uuid(), 'default', $1, 'shortlived')
	  returning id
	`
	err := dbpool.QueryRow(context.Background(), query, tenantId).Scan(&tracerSessionId)
	assert.NoError(t, err)
	return tracerSessionId
}

func HostProjectSetup(t *testing.T, dbpool *database.AuditLoggedPool, tenantId string, apiKeyId string, tracerSessionId string) string {
	var hostProjectId string
	query := `
	  insert into host_projects (id, tenant_id, name, api_key_id, knative, tracer_session_id)
		values (gen_random_uuid(), $1, 'testproject', $2, '{}', $3)
	  returning id
	`
	err := dbpool.QueryRow(context.Background(), query, tenantId, apiKeyId, tracerSessionId).Scan(&hostProjectId)
	assert.NoError(t, err)
	return hostProjectId
}

func TenantUsageLimitsSetup(t *testing.T, dbpool *database.AuditLoggedPool, tenantId string, limitType usage_limits.UsageLimitType, limitValue int) string {
	var limitID string
	query := `
		insert into per_tenant_usage_limits (id, limit_type, limit_value, tenant_id)
		values (gen_random_uuid(), $1, $2, $3)
		returning id
	`
	err := dbpool.QueryRow(context.Background(), query, limitType, limitValue, tenantId).Scan(&limitID)
	assert.NoError(t, err)
	return limitID
}

func TestLogger(t *testing.T) func(next http.Handler) http.Handler {
	opt := httplog.Options{LogLevel: slog.Level(config.Env.SlogLevel)}
	log := &httplog.Logger{Logger: slogt.New(t), Options: opt}
	return httplog.RequestLogger(log)
}
