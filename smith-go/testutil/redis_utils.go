package testutil

import (
	"context"
	"testing"

	"github.com/redis/go-redis/v9"
	"langchain.com/smith/config"
	lsredis "langchain.com/smith/redis"
)

// InitTestRedisClients initializes all Redis clients and returns them in a RoutedRedisPools struct
// and a separate cachingRedisPool
func InitTestRedisClients(t *testing.T) (*lsredis.RoutedRedisPools, redis.UniversalClient) {
	redisPool := lsredis.SingleRedisConnect()
	clusterPool := lsredis.RedisClusterConnect()
	cachingRedisPool := lsredis.CachingRedisConnect()

	var shardManager *lsredis.ShardedRedisManager
	if config.Env.RedisShardingEnabled {
		shardManager = lsredis.NewShardedManager()
	}

	routedRedisPools := &lsredis.RoutedRedisPools{
		SingleRedisPool:  &redisPool,
		ClusterRedisPool: &clusterPool,
		ShardManager:     shardManager,
	}

	return routedRedisPools, cachingRedisPool
}

// CleanupTestRedisClient handles cleanup of a Redis client with optional flush
// Usage: defer testutil.CleanupTestRedisClient(t, redisPool, true)
func CleanupTestRedisClient(t *testing.T, client redis.UniversalClient, flush bool) {
	if flush {
		client.FlushAll(context.Background())
	}
	err := client.Close()
	if err != nil {
		t.Error(err)
	}
}

// CleanupTestRoutedRedisPools handles cleanup of all Redis clients in RoutedRedisPools with optional flush
func CleanupTestRoutedRedisPools(t *testing.T, pools *lsredis.RoutedRedisPools, flush bool) {
	if pools.SingleRedisPool != nil {
		CleanupTestRedisClient(t, *pools.SingleRedisPool, flush)
	}
	if pools.ClusterRedisPool != nil {
		CleanupTestRedisClient(t, *pools.ClusterRedisPool, flush)
	}
	if pools.ShardManager != nil {
		for _, client := range pools.ShardManager.GetClients() {
			client.FlushAll(context.Background())
		}
	}
}
