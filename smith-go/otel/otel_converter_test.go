package otel_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	commonpb "go.opentelemetry.io/proto/otlp/common/v1"
	tracesdkpb "go.opentelemetry.io/proto/otlp/trace/v1"
	"langchain.com/smith/config"
	"langchain.com/smith/otel"
	"langchain.com/smith/runs"
	"langchain.com/smith/testutil"
	"langchain.com/smith/util"
)

func TestConverter_ConvertArizeSpan(t *testing.T) {
	converter := otel.GenAiConverter{}

	testCases := []struct {
		name     string
		input    *tracesdkpb.Span
		expected *runs.Run
	}{
		{
			name: "arize span conversion",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "LLMChain",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"prompt": "What is the capital of France?"}`,
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"response": "The capital of France is Paris."}`,
							},
						},
					},
					{
						Key: "llm.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "openai",
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "LLM",
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("LLMChain"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("llm"),
				Inputs: map[string]interface{}{
					"prompt": "What is the capital of France?",
				},
				Outputs: map[string]interface{}{
					"response": "The capital of France is Paris.",
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
						"ls_provider":   "openai",
					},
				},
			},
		},
		{
			name: "openinference llm span with messages",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "LLMChain",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "input.mime_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "application/json",
							},
						},
					},
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"model": "claude-3", "max_tokens": 100, "temperature": 0, "messages": [{"role": "user", "content": [{"type": "text", "text": "Hi"}]}]}`,
							},
						},
					},
					{
						Key: "llm.input_messages.0.message.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Hi",
							},
						},
					},
					{
						Key: "llm.input_messages.0.message.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "user",
							},
						},
					},
					{
						Key: "llm.invocation_parameters",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"model": "claude-3", "max_tokens": 100, "temperature": 0}`,
							},
						},
					},
					{
						Key: "llm.model_name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "claude-3",
							},
						},
					},
					{
						Key: "llm.output_messages.0.message.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Hello! How can I help you today?",
							},
						},
					},
					{
						Key: "llm.output_messages.0.message.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "assistant",
							},
						},
					},
					{
						Key: "llm.provider",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "anthropic",
							},
						},
					},
					{
						Key: "llm.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "anthropic",
							},
						},
					},
					{
						Key: "llm.token_count.completion",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 8,
							},
						},
					},
					{
						Key: "llm.token_count.prompt",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 1,
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "LLM",
							},
						},
					},
					{
						Key: "output.mime_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "application/json",
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"id":"msg_123","content":[{"text":"Hello! How can I help you today?","type":"text"}],"model":"claude-3","role":"assistant","type":"message","usage":{"input_tokens":1,"output_tokens":8}}`,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("LLMChain"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("llm"),
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "user",
							"content": "Hi",
						},
					},
				},
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "assistant",
							"content": "Hello! How can I help you today?",
						},
					},
					"usage_metadata": map[string]interface{}{
						"input_tokens":  int64(1),
						"output_tokens": int64(8),
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
						"ls_provider":   "anthropic",
						"ls_model_name": "claude-3",
					},
					"invocation_params": map[string]interface{}{
						"model":       "claude-3",
						"max_tokens":  100.0,
						"temperature": 0.0,
					},
				},
			},
		},
		{
			name: "prompt template with one variable",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "LLMChain",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "sky",
							},
						},
					},
					{
						Key: "llm.prompt_template.template",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "{x} {y} {z}?",
							},
						},
					},
					{
						Key: "llm.prompt_template.variables",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"x": "why is", "y": "sky", "z": "blue"}`,
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "UNKNOWN",
							},
						},
					},
					{
						Key: "output.mime_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "application/json",
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"output": "messages=[HumanMessage(content='why is sky blue?', additional_kwargs={}, response_metadata={})]"}`,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("LLMChain"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("prompt"),
				Inputs: map[string]interface{}{
					"y": "sky",
				},
				Outputs: map[string]interface{}{
					"output": "messages=[HumanMessage(content='why is sky blue?', additional_kwargs={}, response_metadata={})]",
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "prompt template with multiple variables",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "LLMChain",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"y": "sky", "z": "blue"}`,
							},
						},
					},
					{
						Key: "llm.prompt_template.template",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "{x} {y} {z}?",
							},
						},
					},
					{
						Key: "llm.prompt_template.variables",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"x": "why is", "y": "sky", "z": "blue"}`,
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "UNKNOWN",
							},
						},
					},
					{
						Key: "output.mime_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "application/json",
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"output": "messages=[HumanMessage(content='why is sky blue?', additional_kwargs={}, response_metadata={})]"}`,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("LLMChain"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("prompt"),
				Inputs: map[string]interface{}{
					"y": "sky",
					"z": "blue",
				},
				Outputs: map[string]interface{}{
					"output": "messages=[HumanMessage(content='why is sky blue?', additional_kwargs={}, response_metadata={})]",
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "embedding",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "Embedding",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "embedding.embeddings[0].embedding.text",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "sky",
							},
						},
					},
					{
						Key: "embedding.embeddings[0].embedding.vector",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_ArrayValue{
								ArrayValue: &commonpb.ArrayValue{
									Values: []*commonpb.AnyValue{
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: 0.0012893225066363811}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: 0.02517860382795334}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: 0.07008399814367294}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: -0.035142261534929276}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: 0.007030959241092205}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: -0.0317583791911602}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: -0.0010660801781341434}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: -0.015791459009051323}},
										{Value: &commonpb.AnyValue_DoubleValue{DoubleValue: 0.0019050013506785035}},
									},
								},
							},
						},
					},
					{
						Key: "embedding.model_name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "text-embedding-3-small",
							},
						},
					},
					{
						Key: "input.mime_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "application/json",
							},
						},
					},
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"input": "sky", "model": "text-embedding-3-small", "encoding_format": "base64"}`,
							},
						},
					},
					{
						Key: "llm.invocation_parameters",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"model": "text-embedding-3-small", "encoding_format": "base64"}`,
							},
						},
					},
					{
						Key: "llm.provider",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "openai",
							},
						},
					},
					{
						Key: "llm.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "openai",
							},
						},
					},
					{
						Key: "llm.token_count.prompt",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 1,
							},
						},
					},
					{
						Key: "llm.token_count.total",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 1,
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"data":[{"embedding":[0.0012893225066363811,0.02517860382795334,0.07008399814367294,-0.035142261534929276,0.007030959241092205,-0.0317583791911602,-0.0010660801781341434,-0.015791459009051323,0.0019050013506785035],"index":0,"object":"embedding"}],"mode":"text-embedding-3-small","object":"list","usage":{"prompt_tokens":1,"total_tokens":1}}`,
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "EMBEDDING",
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("Embedding"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("embedding"),
				Inputs: map[string]interface{}{
					"input":           "sky",
					"model":           "text-embedding-3-small",
					"encoding_format": "base64",
				},
				Outputs: map[string]interface{}{
					"data": []interface{}{
						map[string]interface{}{
							"embedding": []float64{
								0.0012893225066363811, 0.02517860382795334, 0.07008399814367294,
								-0.035142261534929276, 0.007030959241092205, -0.0317583791911602,
								-0.0010660801781341434, -0.015791459009051323, 0.0019050013506785035,
							},
							"index":  float64(0),
							"object": "embedding",
						},
					},
					"mode":   "text-embedding-3-small",
					"object": "list",
					"usage": map[string]interface{}{
						"prompt_tokens": float64(1),
						"total_tokens":  float64(1),
					},
					"usage_metadata": map[string]interface{}{
						"input_tokens": int64(1),
						"total_tokens": int64(1),
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
						"ls_provider":   "openai",
					},
					"invocation_params": map[string]interface{}{
						"model":           "text-embedding-3-small",
						"encoding_format": "base64",
					},
				},
			},
		},
		{
			name: "retrieval span",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "Retriever",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "What is this website about?",
							},
						},
					},
					{
						Key: "metadata",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"ls_retriever_name": "vectorstore", "ls_vector_store_provider": "Chroma", "ls_embedding_provider": "OpenAIEmbeddings"}`,
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "RETRIEVER",
							},
						},
					},
					{
						Key: "output.mime_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "application/json",
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"documents": ["page_content='Example Domain\n\n\n\n\n\n\n\nExample Domain\nThis domain is for use in illustrative examples in documents. You may use this\n    domain in literature without prior coordination or asking for permission.\nMore information...' metadata={'language': 'No language found.', 'source': 'https://www.example.com', 'title': 'Example Domain'}"]}`,
							},
						},
					},
					{
						Key: "retrieval.documents.0.document.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Example Domain\n\n\n\n\n\n\n\nExample Domain\nThis domain is for use in illustrative examples in documents. You may use this\n    domain in literature without prior coordination or asking for permission.\nMore information...",
							},
						},
					},
					{
						Key: "retrieval.documents.0.document.metadata",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"language": "No language found.", "source": "https://www.example.com", "title": "Example Domain"}`,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("Retriever"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("retriever"),
				Inputs: map[string]interface{}{
					"query": "What is this website about?",
				},
				Outputs: map[string]interface{}{
					"documents": []interface{}{
						map[string]interface{}{
							"page_content": "Example Domain\n\n\n\n\n\n\n\nExample Domain\nThis domain is for use in illustrative examples in documents. You may use this\n    domain in literature without prior coordination or asking for permission.\nMore information...",
							"metadata": map[string]interface{}{
								"language": "No language found.",
								"source":   "https://www.example.com",
								"title":    "Example Domain",
							},
							"type": "Document",
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID":            "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":             "e9c2aefd50516b2a",
						"ls_retriever_name":        "vectorstore",
						"ls_vector_store_provider": "Chroma",
						"ls_embedding_provider":    "OpenAIEmbeddings",
					},
				},
			},
		},
		{
			name: "tool span with tool.name",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "GenericToolSpan",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "input.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"query": "What's the weather in San Francisco?"}`,
							},
						},
					},
					{
						Key: "output.value",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"temperature": 68, "conditions": "Foggy"}`,
							},
						},
					},
					{
						Key: "tool.name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "WeatherTool",
							},
						},
					},
					{
						Key: "openinference.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "TOOL",
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("WeatherTool"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("tool"),
				Inputs: map[string]interface{}{
					"query": "What's the weather in San Francisco?",
				},
				Outputs: map[string]interface{}{
					"temperature": float64(68),
					"conditions":  "Foggy",
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := converter.ConvertSpan(tc.input, false)

			assert.NoError(t, err, "ConvertSpan should not return an error")
			assert.NotNil(t, result, "Result should not be nil")
			if result == nil {
				return
			}
			assert.Equal(t, tc.expected.Name, result.Name, "Name NEQ")
			testutil.StringPtrEqual(t, tc.expected.ID, result.ID, "ID NEQ")
			testutil.StringPtrEqual(t, tc.expected.TraceID, result.TraceID, "TraceID NEQ")
			assert.Equal(t, tc.expected.StartTime, tc.expected.StartTime, "StartTime NEQ")
			assert.Equal(t, tc.expected.EndTime, tc.expected.EndTime, "EndTime NEQ")
			assert.Equal(t, tc.expected.Extra, result.Extra, "Extra NEQ")
			assert.Equal(t, tc.expected.Inputs, result.Inputs, "Inputs NEQ")
			assert.Equal(t, tc.expected.Outputs, result.Outputs, "Outputs NEQ")
			assert.Equal(t, tc.expected.RunType, result.RunType, "RunType NEQ")
			testutil.StringPtrEqual(t, tc.expected.Error, result.Error, "Error NEQ")
			assert.Equal(t, tc.expected.Serialized, result.Serialized, "Serialized NEQ")
			testutil.StringPtrEqual(t, tc.expected.ParentRunID, result.ParentRunID, "ParentRunID NEQ")
			assert.Equal(t, tc.expected.Events, result.Events, "Events NEQ")
			assert.Equal(t, tc.expected.Tags, result.Tags, "Tags NEQ")
			assert.Equal(t, tc.expected.DottedOrder, result.DottedOrder, "DottedOrder NEQ")
			testutil.StringPtrEqual(t, tc.expected.SessionID, result.SessionID, "SessionID NEQ")
			testutil.StringPtrEqual(t, tc.expected.SessionName, result.SessionName, "SessionName NEQ")
			assert.Equal(t, tc.expected.InputAttachments, result.InputAttachments, "InputAttachments NEQ")
			assert.Equal(t, tc.expected.OutputAttachments, result.OutputAttachments, "OutputAttachments NEQ")

		})
	}
	t.Run("logfire tools extraction", func(t *testing.T) {
		toolsJSON := `["search","calculator"]`
		argsJSON := `{"query":"2+2"}`

		span := &tracesdkpb.Span{
			Name:              "tool-span",
			TraceId:           []byte{0xde, 0xca, 0xfb, 0xad, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xaa, 0xbb},
			SpanId:            []byte{0xde, 0xad, 0xfa, 0xce, 0xb0, 0x0b, 0x1e, 0x55},
			StartTimeUnixNano: 1731525170000000000,
			EndTimeUnixNano:   1731525170009000000,
			Attributes: []*commonpb.KeyValue{
				{
					Key: "tools",
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: toolsJSON},
					},
				},
				{
					Key: "gen_ai.tool.name",
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: "calculator"},
					},
				},
				{
					Key: "tool_arguments",
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: argsJSON},
					},
				},
			},
		}

		expected := &runs.Run{
			Name:      util.StringPtr("tool-span"),
			ID:        util.StringPtr("00000000-0000-0000-dead-faceb00b1e55"),
			StartTime: util.StringPtr(time.Unix(0, 1731525170000000000).Format(time.RFC3339Nano)),
			EndTime:   util.StringPtr(time.Unix(0, 1731525170009000000).Format(time.RFC3339Nano)),
			RunType:   util.StringPtr("tool"),
			Extra: map[string]interface{}{
				"metadata": map[string]interface{}{
					"OTEL_TRACE_ID": "decafbad00112233445566778899aabb",
					"OTEL_SPAN_ID":  "deadfaceb00b1e55",
				},
				"invocation_params": map[string]interface{}{
					"tools":          []interface{}{"search", "calculator"},
					"tool_name":      "calculator",
					"tool_arguments": map[string]interface{}{"query": "2+2"},
				},
			},
		}

		result, err := converter.ConvertSpan(span, false)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		if result == nil {
			return
		}

		assert.Equal(t, expected.RunType, result.RunType, "RunType mismatch")
		assert.Equal(t, expected.Extra, result.Extra, "invocation_params mismatch")
	})
}

func TestConverter_ConvertSpan(t *testing.T) {
	converter := otel.GenAiConverter{}

	testCases := []struct {
		name     string
		input    *tracesdkpb.Span
		expected *runs.Run
	}{
		{
			name: "genai events extraction",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "GenAI Operation",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: otel.GenAIOperationName,
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "chat",
							},
						},
					},
				},
				Events: []*tracesdkpb.Span_Event{
					{
						Name: otel.GenAIContentPrompt,
						Attributes: []*commonpb.KeyValue{
							{
								Key: otel.GenAIPrompt,
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"messages":[{"role":"user","content":"Tell me about AI"}]}`,
									},
								},
							},
						},
					},
					{
						Name: otel.GenAIContentCompletion,
						Attributes: []*commonpb.KeyValue{
							{
								Key: otel.GenAICompletion,
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"messages":[{"role":"assistant","content":"AI stands for Artificial Intelligence..."}]}`,
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("GenAI Operation"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("llm"),
				Inputs: map[string]interface{}{
					"messages": []interface{}{
						map[string]interface{}{
							"role":    "user",
							"content": "Tell me about AI",
						},
					},
				},
				Outputs: map[string]interface{}{
					"messages": []interface{}{
						map[string]interface{}{
							"role":    "assistant",
							"content": "AI stands for Artificial Intelligence...",
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "genai events plain string extraction",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "GenAI Operation",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: otel.GenAIOperationName,
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "completion",
							},
						},
					},
				},
				Events: []*tracesdkpb.Span_Event{
					{
						Name: otel.GenAIContentPrompt,
						Attributes: []*commonpb.KeyValue{
							{
								Key: otel.GenAIPrompt,
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Tell me about AI",
									},
								},
							},
						},
					},
					{
						Name: otel.GenAIContentCompletion,
						Attributes: []*commonpb.KeyValue{
							{
								Key: otel.GenAICompletion,
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "AI stands for Artificial Intelligence...",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("GenAI Operation"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("llm"),
				Inputs: map[string]interface{}{
					"input": "Tell me about AI",
				},
				Outputs: map[string]interface{}{
					"output": "AI stands for Artificial Intelligence...",
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "genai embedding operation",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "GenAI Operation",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: otel.GenAIOperationName,
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "embedding",
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("GenAI Operation"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("embedding"),
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "prompt template span conversion",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "ChatPromptTemplate.task",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "traceloop.association.properties.data",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "aaaaaaaaaaaaaaaaaaaa",
							},
						},
					},
					{
						Key: "traceloop.association.properties.thread_id",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "yello",
							},
						},
					},
					{
						Key: "traceloop.workflow.name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "RunnableSequence",
							},
						},
					},
					{
						Key: "traceloop.entity.path",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "",
							},
						},
					},
					{
						Key: "traceloop.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "task",
							},
						},
					},
					{
						Key: "traceloop.entity.name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "ChatPromptTemplate",
							},
						},
					},
					{
						Key: "traceloop.entity.input",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"inputs": {"question": "stream it where did harrison work?", "context": [{"lc": 1, "type": "constructor", "id": ["langchain", "schema", "document", "Document"], "kwargs": {"page_content": "Harrison worked at Kensho", "type": "Document"}}]}, "tags": ["seq:step:2"], "metadata": {}, "kwargs": {"run_type": "prompt", "name": "ChatPromptTemplate"}}`,
							},
						},
					},
					{
						Key: "traceloop.entity.output",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: `{"outputs": {"lc": 1, "type": "constructor", "id": ["langchain", "prompts", "chat", "ChatPromptValue"], "kwargs": {"messages": [{"lc": 1, "type": "constructor", "id": ["langchain", "schema", "messages", "HumanMessage"], "kwargs": {"content": "Answer the question based only on the following context:\n[Document(metadata={}, page_content='Harrison worked at Kensho')]\n\nQuestion: stream it where did harrison work?\n iteration 0", "type": "human"}}]}}, "kwargs": {"tags": ["seq:step:2"]}}`,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				Name:        util.StringPtr("ChatPromptTemplate"),
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				TraceID:     nil,
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("chain"), // TODO: this is based off high level attrib of task and not inputs / outputs
				Inputs: map[string]interface{}{
					"inputs": map[string]interface{}{
						"context": []interface{}{
							map[string]interface{}{
								"id": []interface{}{"langchain", "schema", "document", "Document"},
								"kwargs": map[string]interface{}{
									"page_content": "Harrison worked at Kensho",
									"type":         "Document",
								},
								"lc":   float64(1),
								"type": "constructor",
							},
						},
						"question": "stream it where did harrison work?",
					},
					"kwargs": map[string]interface{}{
						"name":     "ChatPromptTemplate",
						"run_type": "prompt",
					},
					"metadata": map[string]interface{}{},
					"tags":     []interface{}{"seq:step:2"},
				},
				Outputs: map[string]interface{}{
					"kwargs": map[string]interface{}{
						"tags": []interface{}{"seq:step:2"},
					},
					"outputs": map[string]interface{}{
						"id": []interface{}{"langchain", "prompts", "chat", "ChatPromptValue"},
						"kwargs": map[string]interface{}{
							"messages": []interface{}{
								map[string]interface{}{
									"id": []interface{}{"langchain", "schema", "messages", "HumanMessage"},
									"kwargs": map[string]interface{}{
										"content": "Answer the question based only on the following context:\n[Document(metadata={}, page_content='Harrison worked at Kensho')]\n\nQuestion: stream it where did harrison work?\n iteration 0",
										"type":    "human",
									},
									"lc":   float64(1),
									"type": "constructor",
								},
							},
						},
						"lc":   float64(1),
						"type": "constructor",
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"data":          "aaaaaaaaaaaaaaaaaaaa",
						"thread_id":     "yello",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
					},
				},
			},
		},
		{
			name: "chat completion span conversion",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0x39, 0x90, 0x07, 0xa0, 0x5b, 0x32, 0x0b, 0x4c},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "ChatOpenAI.chat",
				StartTimeUnixNano: 1731525166454185000,
				EndTimeUnixNano:   1731525167160370000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "traceloop.association.properties.data",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "aaaaaaaaaaaaaaaaaaaa",
							},
						},
					},
					{
						Key: "traceloop.association.properties.thread_id",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "yello",
							},
						},
					},
					{
						Key: "traceloop.association.properties.ls_provider",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "openai",
							},
						},
					},
					{
						Key: "traceloop.association.properties.ls_model_type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "chat",
							},
						},
					},
					{
						Key: "traceloop.association.properties.ls_model_name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "gpt-3.5-turbo",
							},
						},
					},
					{
						Key: "traceloop.association.properties.ls_temperature",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_DoubleValue{
								DoubleValue: 0.7,
							},
						},
					},
					{
						Key: "traceloop.workflow.name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "RunnableSequence",
							},
						},
					},
					{
						Key: "traceloop.entity.path",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "",
							},
						},
					},
					{
						Key: "gen_ai.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Langchain",
							},
						},
					},
					{
						Key: "llm.request.type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "chat",
							},
						},
					},
					{
						Key: "gen_ai.request.model",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "gpt-3.5-turbo",
							},
						},
					},
					{
						Key: "gen_ai.response.model",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "gpt-3.5-turbo",
							},
						},
					},
					{
						Key: "gen_ai.request.temperature",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_DoubleValue{
								DoubleValue: 0.7,
							},
						},
					},
					{
						Key: "gen_ai.prompt.0.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "user",
							},
						},
					},
					{
						Key: "gen_ai.prompt.0.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Answer the question based only on the following context:\n[Document(metadata={}, page_content='Harrison worked at Kensho')]\n\nQuestion: stream it where did harrison work?\n iteration 0",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Harrison worked at Kensho.",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "assistant",
							},
						},
					},
				},
			},
			expected: &runs.Run{
				Name:        util.StringPtr("ChatOpenAI.chat"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166454185000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525167160370000).Format(time.RFC3339Nano)),
				ID:          util.StringPtr("00000000-0000-0000-3990-07a05b320b4c"),
				TraceID:     nil,
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				RunType:     util.StringPtr("llm"),
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"data":           "aaaaaaaaaaaaaaaaaaaa",
						"thread_id":      "yello",
						"ls_provider":    "openai",
						"ls_model_type":  "chat",
						"ls_model_name":  "gpt-3.5-turbo",
						"ls_temperature": 0.7,
						"OTEL_SPAN_ID":   "399007a05b320b4c",
						"OTEL_TRACE_ID":  "af6efed246024d0061517eca2a3fe9ab",
					},
					"invocation_params": map[string]interface{}{
						"model":       "gpt-3.5-turbo",
						"temperature": 0.7,
					},
				},
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						map[string]interface{}{
							"role":    "user",
							"content": "Answer the question based only on the following context:\n[Document(metadata={}, page_content='Harrison worked at Kensho')]\n\nQuestion: stream it where did harrison work?\n iteration 0",
						},
					},
				},
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						map[string]interface{}{
							"role":    "assistant",
							"content": "Harrison worked at Kensho.",
						},
					},
				},
			},
		},
		{
			name: "anthropic chat completion",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0x8a, 0x02, 0x76, 0xe6, 0x0f, 0x9c, 0x68, 0x95, 0x0f, 0x9b, 0xc0, 0x6b, 0x3a, 0xf4, 0x25, 0x11},
				SpanId:            []byte{0x37, 0x3e, 0xbe, 0xce, 0x4b, 0x87, 0x81, 0x33},
				ParentSpanId:      []byte{0xa0, 0xc3, 0xb2, 0xc3, 0xae, 0xe1, 0x5a, 0x10},
				Name:              "anthropic.chat",
				StartTimeUnixNano: 1731552045057582000,
				EndTimeUnixNano:   1731552051745125000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "gen_ai.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Anthropic",
							},
						},
					},
					{
						Key: "llm.request.type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "completion",
							},
						},
					},
					{
						Key: "traceloop.workflow.name",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "pirate_joke_generator",
							},
						},
					},
					{
						Key: "gen_ai.request.model",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "claude-3-opus-20240229",
							},
						},
					},
					{
						Key: "gen_ai.prompt.0.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Tell me a joke about OpenTelemetry",
							},
						},
					},
					{
						Key: "gen_ai.prompt.0.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "user",
							},
						},
					},
					{
						Key: "gen_ai.response.model",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "claude-3-opus-20240229",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.finish_reason",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "end_turn",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "assistant",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Sure, here's a joke about OpenTelemetry:\n\nWhy did the developer start using OpenTelemetry?\nBecause they wanted to trace their way to the root cause of the problem!\n\nExplanation: OpenTelemetry is an open-source observability framework that provides a standard way to generate, collect, and export telemetry data (metrics, logs, and traces) for distributed systems. It helps developers trace and monitor the performance of their applications across different services and infrastructure. The joke plays on the word \"trace,\" which is a key concept in OpenTelemetry, and how it can help developers find the root cause of issues in their distributed systems.",
							},
						},
					},
					{
						Key: "gen_ai.usage.prompt_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 17,
							},
						},
					},
					{
						Key: "gen_ai.usage.completion_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 145,
							},
						},
					},
					{
						Key: "llm.usage.total_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 162,
							},
						},
					},
					{
						Key: "gen_ai.usage.cache_read_input_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 0,
							},
						},
					},
					{
						Key: "gen_ai.usage.cache_creation_input_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 0,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-373e-bece4b878133"),
				TraceID:     nil,
				ParentRunID: util.StringPtr("00000000-0000-0000-a0c3-b2c3aee15a10"),
				Name:        util.StringPtr("anthropic.chat"),
				StartTime:   util.StringPtr(time.Unix(0, 1731552045057582000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731552051745125000).Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("llm"),
				Extra: map[string]interface{}{
					"invocation_params": map[string]interface{}{
						"model": "claude-3-opus-20240229",
					},
					"metadata": map[string]interface{}{
						"ls_provider":   "Anthropic",
						"ls_model_name": "claude-3-opus-20240229",
						"OTEL_SPAN_ID":  "373ebece4b878133",
						"OTEL_TRACE_ID": "8a0276e60f9c68950f9bc06b3af42511",
					},
				},
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"content": "Tell me a joke about OpenTelemetry",
							"role":    "user",
						},
					},
				},
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"content": "Sure, here's a joke about OpenTelemetry:\n\nWhy did the developer start using OpenTelemetry?\nBecause they wanted to trace their way to the root cause of the problem!\n\nExplanation: OpenTelemetry is an open-source observability framework that provides a standard way to generate, collect, and export telemetry data (metrics, logs, and traces) for distributed systems. It helps developers trace and monitor the performance of their applications across different services and infrastructure. The joke plays on the word \"trace,\" which is a key concept in OpenTelemetry, and how it can help developers find the root cause of issues in their distributed systems.",
							"role":    "assistant",
						},
					},
					"usage_metadata": map[string]interface{}{
						"input_tokens":  int64(17),
						"output_tokens": int64(145),
						"total_tokens":  int64(162),
					},
				},
			},
		},
		{
			name: "openai chat completion with haiku",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0x8c, 0x0a, 0xc9, 0x49, 0x71, 0x1f, 0x08, 0xce, 0x16, 0x5c, 0x2e, 0x53, 0xc4, 0x30, 0xab, 0x77},
				SpanId:            []byte{0x21, 0x2b, 0x2a, 0xc6, 0x5c, 0x45, 0xa8, 0xf8},
				Name:              "call_open_ai",
				StartTimeUnixNano: 1733424377667931000,
				EndTimeUnixNano:   1733424378367568000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "langsmith.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "LLM",
							},
						},
					},
					{
						Key: "langsmith.metadata.user_id",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "user_123",
							},
						},
					},
					{
						Key: "gen_ai.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "OpenAI",
							},
						},
					},
					{
						Key: "gen_ai.request.model",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "gpt-4o-mini",
							},
						},
					},
					{
						Key: "llm.request.type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "chat",
							},
						},
					},
					{
						Key: "gen_ai.prompt.0.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "You are a helpful assistant.",
							},
						},
					},
					{
						Key: "gen_ai.prompt.0.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "system",
							},
						},
					},
					{
						Key: "gen_ai.prompt.1.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Write a haiku about recursion in programming.",
							},
						},
					},
					{
						Key: "gen_ai.prompt.1.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "user",
							},
						},
					},
					{
						Key: "gen_ai.response.model",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "gpt-4o-mini-2024-07-18",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.content",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "Functions call themselves,  \nLoops within loops unfold fast,  \nInfinite echoes.",
							},
						},
					},
					{
						Key: "gen_ai.completion.0.role",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "asistant",
							},
						},
					},
					{
						Key: "gen_ai.usage.prompt_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 26,
							},
						},
					},
					{
						Key: "gen_ai.usage.completion_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 15,
							},
						},
					},
					{
						Key: "gen_ai.usage.total_tokens",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_IntValue{
								IntValue: 41,
							},
						},
					},
				},
			},
			expected: &runs.Run{
				Name:        util.StringPtr("call_open_ai"),
				StartTime:   util.StringPtr(time.Unix(0, 1733424377667931000).Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1733424378367568000).Format(time.RFC3339Nano)),
				ID:          util.StringPtr("00000000-0000-0000-212b-2ac65c45a8f8"),
				TraceID:     nil,
				ParentRunID: nil,
				RunType:     util.StringPtr("llm"),
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"ls_provider":   "OpenAI",
						"ls_model_name": "gpt-4o-mini",
						"user_id":       "user_123",
						"OTEL_SPAN_ID":  "212b2ac65c45a8f8",
						"OTEL_TRACE_ID": "8c0ac949711f08ce165c2e53c430ab77",
					},
					"invocation_params": map[string]interface{}{
						"model": "gpt-4o-mini-2024-07-18",
					},
				},
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "system",
							"content": "You are a helpful assistant.",
						},
						{
							"role":    "user",
							"content": "Write a haiku about recursion in programming.",
						},
					},
				},
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "asistant",
							"content": "Functions call themselves,  \nLoops within loops unfold fast,  \nInfinite echoes.",
						},
					},
					"usage_metadata": map[string]interface{}{
						"input_tokens":  int64(26),
						"output_tokens": int64(15),
						"total_tokens":  int64(41),
					},
				},
			},
		},
		{
			name: "exception test",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10},
				SpanId:            []byte{0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18},
				Name:              "exception_test_span",
				StartTimeUnixNano: 1733424377667931000,
				EndTimeUnixNano:   1733424378367568000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "langsmith.span.kind",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "LLM",
							},
						},
					},
					{
						Key: "llm.request.type",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "chat",
							},
						},
					},
				},
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "exception",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "exception.message",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Test error message",
									},
								},
							},
							{
								Key: "exception.stacktrace",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Test stacktrace",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				Name:      util.StringPtr("exception_test_span"),
				StartTime: util.StringPtr(time.Unix(0, 1733424377667931000).Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1733424378367568000).Format(time.RFC3339Nano)),
				ID:        util.StringPtr("00000000-0000-0000-1112-131415161718"),
				TraceID:   nil,
				RunType:   util.StringPtr("llm"),
				Status:    util.StringPtr("error"),
				Error:     util.StringPtr("Test error message\n\nStacktrace:\nTest stacktrace"),
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_SPAN_ID":  "1112131415161718",
						"OTEL_TRACE_ID": "0102030405060708090a0b0c0d0e0f10",
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := converter.ConvertSpan(tc.input, false)

			assert.NoError(t, err, "ConvertSpan should not return an error")
			assert.NotNil(t, result, "Result should not be nil")
			if result == nil {
				return
			}
			assert.Equal(t, tc.expected.Name, result.Name, "Name NEQ")
			testutil.StringPtrEqual(t, tc.expected.ID, result.ID, "ID NEQ")
			testutil.StringPtrEqual(t, tc.expected.TraceID, result.TraceID, "TraceID NEQ")
			assert.Equal(t, tc.expected.StartTime, tc.expected.StartTime, "StartTime NEQ")
			assert.Equal(t, tc.expected.EndTime, tc.expected.EndTime, "EndTime NEQ")
			assert.Equal(t, tc.expected.Extra, result.Extra, "Extra NEQ")
			assert.Equal(t, tc.expected.Inputs, result.Inputs, "Inputs NEQ")
			assert.Equal(t, tc.expected.Outputs, result.Outputs, "Outputs NEQ")
			assert.Equal(t, tc.expected.RunType, result.RunType, "RunType NEQ")
			testutil.StringPtrEqual(t, tc.expected.Error, result.Error, "Error NEQ")
			assert.Equal(t, tc.expected.Serialized, result.Serialized, "Serialized NEQ")
			testutil.StringPtrEqual(t, tc.expected.ParentRunID, result.ParentRunID, "ParentRunID NEQ")
			assert.Equal(t, tc.expected.Events, result.Events, "Events NEQ")
			assert.Equal(t, tc.expected.Tags, result.Tags, "Tags NEQ")
			assert.Equal(t, tc.expected.DottedOrder, result.DottedOrder, "DottedOrder NEQ")
			testutil.StringPtrEqual(t, tc.expected.SessionID, result.SessionID, "SessionID NEQ")
			testutil.StringPtrEqual(t, tc.expected.SessionName, result.SessionName, "SessionName NEQ")
			assert.Equal(t, tc.expected.InputAttachments, result.InputAttachments, "InputAttachments NEQ")
			assert.Equal(t, tc.expected.OutputAttachments, result.OutputAttachments, "OutputAttachments NEQ")

		})
	}
	t.Run("logfire prompt + all_messages_events", func(t *testing.T) {
		allMessagesJSON := `[{"content":"Hi","role":"user"},{"content":"OK","role":"assistant"}]`
		promptJSON := `{"text":"Ping"}`

		span := &tracesdkpb.Span{
			Name:              "logfire-test",
			TraceId:           []byte{0xca, 0xfe, 0xba, 0xbe, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xaa, 0xbb},
			SpanId:            []byte{0xde, 0xad, 0xbe, 0xef, 0x00, 0x11, 0x22, 0x33},
			StartTimeUnixNano: 1731525168000000000,
			EndTimeUnixNano:   1731525168009000000,
			Attributes: []*commonpb.KeyValue{
				{
					Key: otel.GenAIOperationName,
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: "chat"},
					},
				},
				{
					Key: otel.LogfirePrompt,
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: promptJSON},
					},
				},
				{
					Key: otel.LogfireAllMessagesEvents,
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: allMessagesJSON},
					},
				},
			},
		}

		expected := &runs.Run{
			Name:      util.StringPtr("logfire-test"),
			ID:        util.StringPtr("00000000-0000-0000-dead-beef00112233"),
			StartTime: util.StringPtr(time.Unix(0, 1731525168000000000).Format(time.RFC3339Nano)),
			EndTime:   util.StringPtr(time.Unix(0, 1731525168009000000).Format(time.RFC3339Nano)),
			RunType:   util.StringPtr("llm"),
			Inputs:    map[string]interface{}{"text": "Ping"},
			Outputs:   map[string]interface{}{"text": allMessagesJSON},
			Extra: map[string]interface{}{
				"metadata": map[string]interface{}{
					"OTEL_TRACE_ID": "cafebabe00112233445566778899aabb",
					"OTEL_SPAN_ID":  "deadbeef00112233",
				},
			},
		}

		result, err := converter.ConvertSpan(span, false)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		if result == nil {
			return
		}

		assert.Equal(t, expected.Name, result.Name)
		testutil.StringPtrEqual(t, expected.ID, result.ID, "ID NEQ")
		assert.Equal(t, expected.RunType, result.RunType)
		assert.Equal(t, expected.Inputs, result.Inputs)
		assert.Equal(t, expected.Outputs, result.Outputs)
		assert.Equal(t, expected.Extra, result.Extra)
	})
	t.Run("logfire events", func(t *testing.T) {
		eventsJSON := `[{"content":"Hi","role":"user","event.name":"gen_ai.user.message"},
						{"index":0,"message":{"role":"assistant"},"event.name":"gen_ai.choice"}]`

		span := &tracesdkpb.Span{
			Name:              "logfire-test",
			TraceId:           []byte{0xca, 0xfe, 0xba, 0xbe, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xaa, 0xbb},
			SpanId:            []byte{0xde, 0xad, 0xbe, 0xef, 0x00, 0x11, 0x22, 0x33},
			StartTimeUnixNano: 1731525168000000000,
			EndTimeUnixNano:   1731525168009000000,
			Attributes: []*commonpb.KeyValue{
				{
					Key: otel.GenAIOperationName,
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: "chat"},
					},
				},
				{
					Key: otel.LogfireEvents,
					Value: &commonpb.AnyValue{
						Value: &commonpb.AnyValue_StringValue{StringValue: eventsJSON},
					},
				},
			},
		}

		// what the converter should split out of the unified array
		inputEvents := []interface{}{
			map[string]interface{}{
				"content":    "Hi",
				"role":       "user",
				"event.name": "gen_ai.user.message",
			},
		}
		choiceEvent := map[string]interface{}{
			"index":      float64(0),
			"message":    map[string]interface{}{"role": "assistant"},
			"event.name": "gen_ai.choice",
		}

		expected := &runs.Run{
			Name:      util.StringPtr("logfire-test"),
			ID:        util.StringPtr("00000000-0000-0000-dead-beef00112233"),
			StartTime: util.StringPtr(time.Unix(0, 1731525168000000000).Format(time.RFC3339Nano)),
			EndTime:   util.StringPtr(time.Unix(0, 1731525168009000000).Format(time.RFC3339Nano)),
			RunType:   util.StringPtr("llm"),
			Inputs:    map[string]interface{}{"input": inputEvents},
			Outputs:   choiceEvent,
			Extra: map[string]interface{}{
				"metadata": map[string]interface{}{
					"OTEL_TRACE_ID": "cafebabe00112233445566778899aabb",
					"OTEL_SPAN_ID":  "deadbeef00112233",
				},
			},
		}

		result, err := converter.ConvertSpan(span, false)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		if result == nil {
			return
		}

		assert.Equal(t, expected.Name, result.Name)
		testutil.StringPtrEqual(t, expected.ID, result.ID, "ID mismatch")
		assert.Equal(t, expected.RunType, result.RunType, "RunType mismatch")
		assert.Equal(t, expected.Inputs, result.Inputs, "Inputs mismatch")
		assert.Equal(t, expected.Outputs, result.Outputs, "Outputs mismatch")
		assert.Equal(t, expected.Extra, result.Extra, "Extra metadata mismatch")
	})
}

func TestConverter_ConvertSpan_GenericOtelAttributes(t *testing.T) {
	// Remember original value so we can restore it after the test
	originalValue := config.Env.GenericOtelEnabled
	defer func() { config.Env.GenericOtelEnabled = originalValue }()

	converter := otel.GenAiConverter{}

	const genericAttributeKey = "unknown.prefix.my_attribute"
	const genericAttributeValue = "my_value"

	span := &tracesdkpb.Span{
		Name:              "generic-otel-test-span",
		TraceId:           []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f, 0x10},
		SpanId:            []byte{0xaa, 0xbb, 0xcc, 0xdd, 0x11, 0x22, 0x33, 0x44},
		StartTimeUnixNano: 1731525166451191000,
		EndTimeUnixNano:   1731525166453124000,
		Attributes: []*commonpb.KeyValue{
			{
				Key: genericAttributeKey,
				Value: &commonpb.AnyValue{
					Value: &commonpb.AnyValue_StringValue{
						StringValue: genericAttributeValue,
					},
				},
			},
			{
				Key: "gen_ai.completion.0.role",
				Value: &commonpb.AnyValue{
					Value: &commonpb.AnyValue_StringValue{
						StringValue: "asistant",
					},
				},
			},
		},
	}

	t.Run("GenericOtelEnabled = true includes generic attribute in metadata", func(t *testing.T) {
		result, err := converter.ConvertSpan(span, true)
		assert.NoError(t, err, "ConvertSpan should succeed")

		metadata, ok := result.Extra[otel.LangSmithMetadata].(map[string]interface{})
		assert.True(t, ok, "metadata must exist")

		value, found := metadata[genericAttributeKey]
		assert.True(t, found, "generic attribute should be present in metadata")
		assert.Equal(t, genericAttributeValue, value)

		_, found = metadata["gen_ai.completion.0.role"]
		assert.False(t, found, "gen_ai.* should not appear in generic metadata")
	})
}

func TestConverter_ConvertSpan_GenAIEventFormat(t *testing.T) {
	converter := otel.GenAiConverter{}

	testCases := []struct {
		name     string
		input    *tracesdkpb.Span
		expected *runs.Run
	}{
		{
			name: "new event format - system and user messages",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				ParentSpanId:      []byte{0x10, 0x62, 0xde, 0x00, 0x18, 0xfd, 0xea, 0x13},
				Name:              "ChatCompletion",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "gen_ai.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "openai",
							},
						},
					},
				},
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "gen_ai.system.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "You are a helpful assistant.",
									},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "system",
									},
								},
							},
						},
					},
					{
						Name: "gen_ai.user.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Hello, how are you?",
									},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "user",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:          util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				ParentRunID: util.StringPtr("00000000-0000-0000-1062-de0018fdea13"),
				Name:        util.StringPtr("ChatCompletion"),
				StartTime:   util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:     util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:     util.StringPtr("chain"),
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "system",
							"content": "You are a helpful assistant.",
						},
						{
							"role":    "user",
							"content": "Hello, how are you?",
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
						"ls_provider":   "openai",
					},
				},
			},
		},
		{
			name: "new event format - assistant and tool messages",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				Name:              "ChatCompletion",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "gen_ai.assistant.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "I'll help you with that calculation.",
									},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "assistant",
									},
								},
							},
						},
					},
					{
						Name: "gen_ai.tool.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Result: 42",
									},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "tool",
									},
								},
							},
							{
								Key: "id",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "call_12345",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				Name:      util.StringPtr("ChatCompletion"),
				StartTime: util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("chain"),
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "assistant",
							"content": "I'll help you with that calculation.",
						},
						{
							"role":         "tool",
							"content":      "Result: 42",
							"tool_call_id": "call_12345",
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "new event format - choice event with tool calls",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				Name:              "ChatCompletion",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "gen_ai.choice",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "finish_reason",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "tool_calls",
									},
								},
							},
							{
								Key: "message.content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Let me calculate that for you.",
									},
								},
							},
							{
								Key: "message.role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "assistant",
									},
								},
							},
							{
								Key: "tool_calls.0.id",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "call_abc123",
									},
								},
							},
							{
								Key: "tool_calls.0.function.name",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "calculator",
									},
								},
							},
							{
								Key: "tool_calls.0.function.arguments",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"expression": "2 + 2"}`,
									},
								},
							},
							{
								Key: "tool_calls.0.type",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "function",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				Name:      util.StringPtr("ChatCompletion"),
				StartTime: util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("chain"),
				Outputs: map[string]interface{}{
					"choices": []map[string]interface{}{
						{
							"finish_reason": "tool_calls",
							"message": map[string]interface{}{
								"content": "Let me calculate that for you.",
								"role":    "assistant",
							},
							"tool_calls": []map[string]interface{}{
								{
									"id":   "call_abc123",
									"type": "function",
									"function": map[string]interface{}{
										"name":      "calculator",
										"arguments": `{"expression": "2 + 2"}`,
									},
								},
							},
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "microsoft semantic kernel format",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				Name:              "AzureOpenAI",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: "gen_ai.system",
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{
								StringValue: "az.ai.inference",
							},
						},
					},
				},
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "gen_ai.system.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "gen_ai.system",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "az.ai.inference",
									},
								},
							},
							{
								Key: "gen_ai.event.content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"role": "system", "content": "You are a helpful assistant."}`,
									},
								},
							},
						},
					},
					{
						Name: "gen_ai.user.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "gen_ai.system",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "az.ai.inference",
									},
								},
							},
							{
								Key: "gen_ai.event.content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"role": "user", "content": "Hello"}`,
									},
								},
							},
						},
					},
					{
						Name: "gen_ai.choice",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "gen_ai.system",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "az.ai.inference",
									},
								},
							},
							{
								Key: "gen_ai.event.content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"message": {"content": "Hello! How can I assist you today? 😊"}, "finish_reason": "stop", "index": 0}`,
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				Name:      util.StringPtr("AzureOpenAI"),
				StartTime: util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("chain"),
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "system",
							"content": "You are a helpful assistant.",
						},
						{
							"role":    "user",
							"content": "Hello",
						},
					},
				},
				Outputs: map[string]interface{}{
					"choices": []map[string]interface{}{
						{
							"finish_reason": "stop",
							"index":         float64(0),
							"message": map[string]interface{}{
								"content": "Hello! How can I assist you today? 😊",
							},
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
						"ls_provider":   "az.ai.inference",
					},
				},
			},
		},
		{
			name: "mixed event formats - backward compatibility",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				Name:              "MixedFormats",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Events: []*tracesdkpb.Span_Event{
					// Old format event
					{
						Name: "gen_ai.content.prompt",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "gen_ai.prompt",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"messages":[{"role":"user","content":"Old format"}]}`,
									},
								},
							},
						},
					},
					// New format event
					{
						Name: "gen_ai.assistant.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "New format response",
									},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "assistant",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				Name:      util.StringPtr("MixedFormats"),
				StartTime: util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("chain"),
				Inputs: map[string]interface{}{
					"messages": []interface{}{
						map[string]interface{}{
							"role":    "user",
							"content": "Old format",
						},
					},
				},
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "assistant",
							"content": "New format response",
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "multiple tool calls in choice event",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				Name:              "MultiToolCall",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "gen_ai.choice",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "finish_reason",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "tool_calls",
									},
								},
							},
							{
								Key: "tool_calls.0.id",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "call_1",
									},
								},
							},
							{
								Key: "tool_calls.0.function.name",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "get_weather",
									},
								},
							},
							{
								Key: "tool_calls.0.function.arguments",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"location": "Paris"}`,
									},
								},
							},
							{
								Key: "tool_calls.0.type",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "function",
									},
								},
							},
							{
								Key: "tool_calls.1.id",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "call_2",
									},
								},
							},
							{
								Key: "tool_calls.1.function.name",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "get_news",
									},
								},
							},
							{
								Key: "tool_calls.1.function.arguments",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{"topic": "technology"}`,
									},
								},
							},
							{
								Key: "tool_calls.1.type",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "function",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				Name:      util.StringPtr("MultiToolCall"),
				StartTime: util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("chain"),
				Outputs: map[string]interface{}{
					"choices": []map[string]interface{}{
						{
							"finish_reason": "tool_calls",
							"tool_calls": []map[string]interface{}{
								{
									"id":   "call_1",
									"type": "function",
									"function": map[string]interface{}{
										"name":      "get_weather",
										"arguments": `{"location": "Paris"}`,
									},
								},
								{
									"id":   "call_2",
									"type": "function",
									"function": map[string]interface{}{
										"name":      "get_news",
										"arguments": `{"topic": "technology"}`,
									},
								},
							},
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "event with malformed JSON in gen_ai.event.content",
			input: &tracesdkpb.Span{
				TraceId:           []byte{0xaf, 0x6e, 0xfe, 0xd2, 0x46, 0x02, 0x4d, 0x00, 0x61, 0x51, 0x7e, 0xca, 0x2a, 0x3f, 0xe9, 0xab},
				SpanId:            []byte{0xe9, 0xc2, 0xae, 0xfd, 0x50, 0x51, 0x6b, 0x2a},
				Name:              "MalformedJSON",
				StartTimeUnixNano: 1731525166451191000,
				EndTimeUnixNano:   1731525166453124000,
				Events: []*tracesdkpb.Span_Event{
					{
						Name: "gen_ai.user.message",
						Attributes: []*commonpb.KeyValue{
							{
								Key: "gen_ai.event.content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: `{invalid json`,
									},
								},
							},
							// Fallback to standard format
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "Fallback content",
									},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{
										StringValue: "user",
									},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-e9c2-aefd50516b2a"),
				Name:      util.StringPtr("MalformedJSON"),
				StartTime: util.StringPtr(time.Unix(0, 1731525166451191000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1731525166453124000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("chain"),
				Inputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "user",
							"content": "Fallback content",
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "af6efed246024d0061517eca2a3fe9ab",
						"OTEL_SPAN_ID":  "e9c2aefd50516b2a",
					},
				},
			},
		},
		{
			name: "assistant messages merge with choice",
			input: &tracesdkpb.Span{
				Name:              "merge-assistant-choice",
				TraceId:           []byte{0xde, 0xad, 0xbe, 0xef, 0xde, 0xad, 0xbe, 0xef, 0xde, 0xad, 0xbe, 0xef, 0xde, 0xad, 0xbe, 0xef},
				SpanId:            []byte{0xde, 0xad, 0xfa, 0xce, 0xde, 0xad, 0xfa, 0xce},
				StartTimeUnixNano: 1748355987000000000,
				EndTimeUnixNano:   1748356029000000000,
				Attributes: []*commonpb.KeyValue{
					{
						Key: otel.GenAIOperationName,
						Value: &commonpb.AnyValue{
							Value: &commonpb.AnyValue_StringValue{StringValue: "chat"},
						},
					},
				},
				Events: []*tracesdkpb.Span_Event{
					{
						Name: otel.GenAIAssistantMessageEvent,
						Attributes: []*commonpb.KeyValue{
							{
								Key: "content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{StringValue: "Sure — working on it…"},
								},
							},
							{
								Key: "role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{StringValue: "assistant"},
								},
							},
						},
					},
					{
						Name: otel.GenAIChoiceEvent,
						Attributes: []*commonpb.KeyValue{
							{
								Key: "finish_reason",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{StringValue: "stop"},
								},
							},
							{
								Key: "message.content",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{StringValue: "All done!"},
								},
							},
							{
								Key: "message.role",
								Value: &commonpb.AnyValue{
									Value: &commonpb.AnyValue_StringValue{StringValue: "assistant"},
								},
							},
						},
					},
				},
			},
			expected: &runs.Run{
				ID:        util.StringPtr("00000000-0000-0000-dead-facedeadface"),
				Name:      util.StringPtr("merge-assistant-choice"),
				StartTime: util.StringPtr(time.Unix(0, 1748355987000000000).UTC().Format(time.RFC3339Nano)),
				EndTime:   util.StringPtr(time.Unix(0, 1748356029000000000).UTC().Format(time.RFC3339Nano)),
				RunType:   util.StringPtr("llm"),
				Outputs: map[string]interface{}{
					"messages": []map[string]interface{}{
						{
							"role":    "assistant",
							"content": "Sure — working on it…",
						},
					},
					"choices": []map[string]interface{}{
						{
							"finish_reason": "stop",
							"message": map[string]interface{}{
								"content": "All done!",
								"role":    "assistant",
							},
						},
					},
				},
				Extra: map[string]interface{}{
					"metadata": map[string]interface{}{
						"OTEL_TRACE_ID": "deadbeefdeadbeefdeadbeefdeadbeef",
						"OTEL_SPAN_ID":  "deadfacedeadface",
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := converter.ConvertSpan(tc.input, false)

			assert.NoError(t, err, "ConvertSpan should not return an error")
			assert.NotNil(t, result, "Result should not be nil")
			if result == nil {
				return
			}
			assert.Equal(t, tc.expected.Name, result.Name, "Name mismatch")
			testutil.StringPtrEqual(t, tc.expected.ID, result.ID, "ID mismatch")
			testutil.StringPtrEqual(t, tc.expected.StartTime, result.StartTime, "StartTime mismatch")
			testutil.StringPtrEqual(t, tc.expected.EndTime, result.EndTime, "EndTime mismatch")
			assert.Equal(t, tc.expected.Extra, result.Extra, "Extra mismatch")
			assert.Equal(t, tc.expected.Inputs, result.Inputs, "Inputs mismatch")
			assert.Equal(t, tc.expected.Outputs, result.Outputs, "Outputs mismatch")
			assert.Equal(t, tc.expected.RunType, result.RunType, "RunType mismatch")
		})
	}
}
