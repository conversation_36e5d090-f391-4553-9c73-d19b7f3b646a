package otel

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"mime"
	"net/http"
	"strings"
	"sync"

	"github.com/go-chi/httplog/v2"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	metricspb "go.opentelemetry.io/proto/otlp/collector/metrics/v1"
	tracepb "go.opentelemetry.io/proto/otlp/collector/trace/v1"
	metricssdkpb "go.opentelemetry.io/proto/otlp/metrics/v1"
	tracesdkpb "go.opentelemetry.io/proto/otlp/trace/v1"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"langchain.com/smith/auth"
	"langchain.com/smith/config"
	"langchain.com/smith/database"
	"langchain.com/smith/ingestion"
	lsredis "langchain.com/smith/redis"
	"langchain.com/smith/runs"
	"langchain.com/smith/storage"
	"langchain.com/smith/tracer_sessions"
	"langchain.com/smith/usage_limits"
	"langchain.com/smith/util"
)

const (
	ContentTypeJSON   = "application/json"
	ContentTypeProto  = "application/protobuf"
	ContentTypeXProto = "application/x-protobuf"
	RunsSemaphore     = 20
)

var errUnsupportedContentType = errors.New("content type is not supported")

type OTELHandler struct {
	Pg                   *database.AuditLoggedPool
	CachingRedisPool     redis.UniversalClient
	RoutedRedisPools     lsredis.RoutedRedisPools
	UsageLimitsClient    *usage_limits.UsageLimitsClient
	TracerSessionsClient *tracer_sessions.TracerSessionsClient
	StorageClient        storage.StorageClient
}

func NewOTELHandler(
	pg *database.AuditLoggedPool,
	cachingRedisPool redis.UniversalClient,
	routedRedisPools lsredis.RoutedRedisPools,
	storageClient storage.StorageClient,
) *OTELHandler {
	usageLimitsClient, err := usage_limits.GetUsageLimitsClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get usage limits client: %v", err))
	}
	tracerSessionsClient, err := tracer_sessions.GetTracerSessionsClient()
	if err != nil {
		panic(fmt.Sprintf("failed to get tracer sessions client: %v", err))
	}
	return &OTELHandler{
		Pg:                   pg,
		CachingRedisPool:     cachingRedisPool,
		RoutedRedisPools:     routedRedisPools,
		UsageLimitsClient:    usageLimitsClient,
		TracerSessionsClient: tracerSessionsClient,
		StorageClient:        storageClient,
	}
}

type OTELError struct {
	Code    int    // HTTP status code
	Message string // Error message for the client
}

func (e *OTELError) Error() string {
	return e.Message
}

func (h *OTELHandler) PostTraces(w http.ResponseWriter, r *http.Request) {
	authInfo := auth.GetAuthInfo(r)
	oplog := httplog.LogEntry(r.Context())

	resourceSpans, err := getResourceSpans(r)
	if err != nil {
		status := http.StatusInternalServerError
		if err == errUnsupportedContentType {
			status = http.StatusUnsupportedMediaType
		} else if strings.Contains(err.Error(), "unexpected EOF") {
			status = http.StatusBadRequest
		}

		writeErrorResponse(w, r, oplog, status, err.Error())
		return
	}

	sessionName := r.Header.Get("Langsmith-Project")
	converter := GenAiConverter{}
	var convertedRuns []*runs.Run = make([]*runs.Run, 0)
	genericOtelEnabled := config.Env.GenericOtelEnabled || config.Env.GenericOtelEnabledTenants.Contains(authInfo.TenantID)
	for _, rs := range resourceSpans {
		for _, ss := range rs.ScopeSpans {
			for _, span := range ss.Spans {
				run, err := converter.ConvertSpan(span, genericOtelEnabled)
				if err != nil {
					oplog.Info("Failed to convert span", "error", err)
					continue
				}
				if sessionName != "" {
					run.SessionName = &sessionName
				}
				convertedRuns = append(convertedRuns, run)
			}
		}
	}

	allHaveLS := true
	for _, run := range convertedRuns {
		if !hasLangsmithKeys(run) {
			allHaveLS = false
			break
		}
	}
	if allHaveLS {
		err = h.submitRunsBatch(r.Context(), authInfo, convertedRuns, oplog)
	} else {
		err = h.submitRunsParallel(r.Context(), authInfo, convertedRuns)
	}
	if err != nil {
		var otelErr *OTELError
		if errors.As(err, &otelErr) {
			writeErrorResponse(w, r, oplog, otelErr.Code, otelErr.Message)
			return
		}
		writeErrorResponse(w, r, oplog, http.StatusInternalServerError, fmt.Sprintf("Failed to submit runs: %v", err))
		return
	}

	resp := &tracepb.ExportTraceServiceResponse{}
	if err := writeProtoResponse(w, r, resp); err != nil {
		status := http.StatusInternalServerError
		if err == errUnsupportedContentType {
			status = http.StatusUnsupportedMediaType
		}
		writeErrorResponse(w, r, oplog, status, err.Error())
		return
	}
}

func (h *OTELHandler) submitRunsParallel(ctx context.Context, authInfo *auth.AuthInfo, newRuns []*runs.Run) error {
	var wg sync.WaitGroup
	sem := make(chan struct{}, RunsSemaphore)
	errChan := make(chan error, len(newRuns))

	for _, run := range newRuns {
		wg.Add(1)
		go func(run *runs.Run) {
			defer wg.Done()
			sem <- struct{}{}
			defer func() { <-sem }()

			if err := h.submitRun(ctx, authInfo, run); err != nil {
				errChan <- err // Send error to channel
			}
		}(run)
	}
	wg.Wait()
	close(errChan)

	// Check for errors
	for err := range errChan {
		if err != nil {
			return err // Return the first error encountered
		}
	}
	return nil
}
func (h *OTELHandler) submitRunsBatch(ctx context.Context, authInfo *auth.AuthInfo, newRuns []*runs.Run, oplog *slog.Logger) error {
	didCheckLonglivedLimit := false
	var payloads []ingestion.QueuePayload
	traceIDs := make(map[uuid.UUID]struct{})
	sessionsByKey := make(map[sessionKey]*tracer_sessions.TracerSessionWithoutVirtualFields)
	sessionsByRun := make(map[string]*tracer_sessions.TracerSessionWithoutVirtualFields)
	extras := make(map[string]map[string]ingestion.ExtraValue)
	var uploads []*storage.UploadAsyncResult

	// Process each run (performing S3 uploads for large fields)
	for _, run := range newRuns {
		// Parse required UUID fields with error handling.
		runID, err := runs.ParseUUID(run.ID, "run_id")
		if err != nil {
			return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
		}
		if runID == nil {
			return &OTELError{Code: http.StatusUnprocessableEntity, Message: "run_id is required"}
		}

		traceID, err := runs.ParseUUID(run.TraceID, "trace_id")
		if err != nil {
			return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
		}

		parentRunID, err := runs.ParseUUID(run.ParentRunID, "parent_run_id")
		if err != nil {
			return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
		}

		sessionID, err := runs.ParseUUID(run.SessionID, "session_id")
		if err != nil {
			return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
		}

		dottedOrder := run.DottedOrder
		if dottedOrder == nil {
			dottedOrder = util.StringPtr("")
		}

		err = runs.ValidateDottedOrder(traceID, *dottedOrder, parentRunID, *runID)
		if err != nil {
			return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
		}

		// Fetch or start tracer session for this run
		sessKey := sessionKey{sessionID: run.SessionID, sessionName: run.SessionName}
		var sess *tracer_sessions.TracerSessionWithoutVirtualFields
		var found bool

		if sess, found = sessionsByKey[sessKey]; !found {
			sess, err = h.TracerSessionsClient.StartOrFetchTracerSession(ctx, *authInfo, run.SessionID, run.SessionName, run.StartTime)
			if err != nil {
				switch {
				case errors.Is(err, tracer_sessions.ErrSessionAlreadyExists):
					return &OTELError{Code: http.StatusConflict, Message: "Session already exists"}
				case errors.Is(err, tracer_sessions.ErrReferenceDatasetNotFound),
					errors.Is(err, tracer_sessions.ErrDefaultDatasetNotFound),
					errors.Is(err, tracer_sessions.ErrTracerSessionNotFound):
					return &OTELError{Code: http.StatusNotFound, Message: err.Error()}
				case errors.Is(err, tracer_sessions.ErrSessionEndTimeBeforeRun):
					return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
				case errors.Is(err, tracer_sessions.ErrInvalidTimestampFormat):
					return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
				default:
					return &OTELError{
						Code:    http.StatusInternalServerError,
						Message: fmt.Sprintf("failed to start tracer session for run %s: %v", *run.ID, err),
					}
				}
			}
			sessionsByKey[sessKey] = sess
		}
		if sessionsByRun[*run.ID] == nil {
			sessionsByRun[*run.ID] = sess
		}

		session, ok := sessionsByRun[*run.ID]
		if !ok {
			oplog.Error("Failed to get session for run", "run_id", *run.ID)
			continue
		}

		if config.Env.FFTraceTiersEnabled && session.TraceTier != nil && *session.TraceTier == tracer_sessions.LongLived && !didCheckLonglivedLimit {
			tenantExceededLonglivedLimits, limitExceededMessage, err := h.UsageLimitsClient.CheckLonglivedUsageLimits(ctx, *authInfo)
			if err != nil {
				return &OTELError{
					Code:    http.StatusInternalServerError,
					Message: fmt.Sprintf("failed to check longlived usage limits for run %s: %v", *run.ID, err),
				}
			}
			if tenantExceededLonglivedLimits {
				return &OTELError{
					Code:    http.StatusTooManyRequests,
					Message: fmt.Sprintf("tenant exceeded usage limits: %v", limitExceededMessage),
				}
			}
			didCheckLonglivedLimit = true
		}

		// Process and store large fields in S3.
		store := fetchOrInitExtrasSlot(extras, *run.ID)
		processedRun, runUploads, err := runs.HandleLargeFields(ctx, *run, store, authInfo, session, h.StorageClient)
		if err != nil {
			return &OTELError{
				Code:    http.StatusInternalServerError,
				Message: fmt.Sprintf("failed to process large fields for run %s: %v", *run.ID, err),
			}
		}
		uploads = append(uploads, runUploads...)
		*run = processedRun

		runBytes, err := json.Marshal(run)
		if err != nil {
			return &OTELError{Code: http.StatusBadRequest, Message: fmt.Sprintf("Failed to marshal run %s to bytes: %v", *run.ID, err)}
		}
		payload := ingestion.QueuePayload{
			RunID:         *runID,
			ParentID:      parentRunID,
			TraceID:       traceID,
			Value:         runBytes,
			ContentType:   "application/json",
			HashKey:       "post",
			ProcessInline: true,
			SessionID:     sessionID,
			SessionName:   run.SessionName,
			StartTime:     run.StartTime,
			Extra:         extras[*run.ID],
			AutoUpgrade:   run.ReferenceExampleID != nil && *run.ReferenceExampleID != "",
		}
		payloads = append(payloads, payload)
		if traceID != nil && *traceID != uuid.Nil {
			traceIDs[*traceID] = struct{}{}
		}
	}

	// Wait for all uploads to complete
	for _, upload := range uploads {
		if upload == nil {
			continue
		}
		if err := upload.Wait(); err != nil {
			return &OTELError{
				Code:    http.StatusInternalServerError,
				Message: fmt.Sprintf("S3 upload failed: %v", err),
			}
		}
	}

	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	if err := ingestion.QueueRunPayload(ctx, routedRedisClient, queueingRedisClient, *authInfo, traceIDs, payloads, false, false); err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeInvalidInput:
				return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
			case ingestion.CodeInternal:
				return &OTELError{Code: http.StatusInternalServerError, Message: err.Error()}
			case ingestion.CodeDuplicate:
				return &OTELError{Code: http.StatusConflict, Message: err.Error()}
			case ingestion.CodeTraceLimitExceeded:
				return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
			default:
				return &OTELError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("Error queueing runs: %v", err)}
			}
		default:
			return &OTELError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("Error queueing runs: %v", err)}
		}
	}
	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, payloads, false, true); err != nil {
			// Log error but continue execution
			oplog := httplog.LogEntry(ctx)
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}
	return nil
}

func (h *OTELHandler) submitRun(ctx context.Context, authInfo *auth.AuthInfo, run *runs.Run) error {
	tenantExceededUsageLimits, limitExceededMessage, err := h.UsageLimitsClient.HasTenantExceededUsageLimits(ctx, *authInfo)
	if err != nil {
		return &OTELError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("Failed to check usage limits: %v", err)}
	}
	if tenantExceededUsageLimits {
		return &OTELError{Code: http.StatusTooManyRequests, Message: limitExceededMessage}
	}

	runID, err := runs.ParseUUID(run.ID, "run_id")
	if err != nil {
		return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
	}
	if runID == nil {
		return &OTELError{Code: http.StatusBadRequest, Message: "unable to parse run_id"}
	}

	traceID, err := runs.ParseUUID(run.TraceID, "trace_id")
	if err != nil {
		return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
	}

	parentRunID, err := runs.ParseUUID(run.ParentRunID, "parent_run_id")
	if err != nil {
		return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
	}

	sessionID, err := runs.ParseUUID(run.SessionID, "session_id")
	if err != nil {
		return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
	}

	sessionName := run.SessionName

	startTime := run.StartTime

	dottedOrder := run.DottedOrder
	if dottedOrder == nil {
		dottedOrder = util.StringPtr("")
	}

	runBytes, err := json.Marshal(run)
	if err != nil {
		return &OTELError{Code: http.StatusInternalServerError, Message: "Failed to marshal run to bytes"}
	}

	err = runs.ValidateDottedOrder(traceID, *dottedOrder, parentRunID, *runID)
	if err != nil {
		return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
	}

	payload := ingestion.QueuePayload{
		RunID:         *runID,
		ParentID:      parentRunID,
		TraceID:       traceID,
		Value:         runBytes,
		ContentType:   "application/json",
		HashKey:       "post",
		ProcessInline: false,
		SessionID:     sessionID,
		SessionName:   sessionName,
		StartTime:     startTime,
		Extra:         nil,
		AutoUpgrade:   run.ReferenceExampleID != nil && *run.ReferenceExampleID != "",
	}

	err = ingestion.EnsureSessionsBeforeQueueRunPayload(
		ctx,
		h.Pg,
		h.UsageLimitsClient,
		h.TracerSessionsClient,
		*authInfo,
		[]ingestion.QueuePayload{payload},
		nil,
	)
	if err != nil {
		if inErr, ok := err.(*ingestion.IngestionError); ok {
			switch inErr.Code {
			case ingestion.CodeUsageLimitExceeded:
				return &OTELError{Code: http.StatusTooManyRequests, Message: inErr.Message}
			}
		}
		switch {
		case errors.Is(err, tracer_sessions.ErrSessionAlreadyExists):
			return &OTELError{Code: http.StatusConflict, Message: "Session already exists"}
		case errors.Is(err, tracer_sessions.ErrReferenceDatasetNotFound),
			errors.Is(err, tracer_sessions.ErrDefaultDatasetNotFound):
			return &OTELError{Code: http.StatusBadRequest, Message: "Dataset not found"}
		case errors.Is(err, tracer_sessions.ErrSessionEndTimeBeforeRun):
			return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
		case errors.Is(err, tracer_sessions.ErrInvalidTimestampFormat):
			return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
		case errors.Is(err, tracer_sessions.ErrTracerSessionNotFound):
			return &OTELError{Code: http.StatusNotFound, Message: err.Error()}
		default:
			return &OTELError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("Error ensuring sessions: %v", err)}
		}
	}

	traceIDs := map[uuid.UUID]struct{}{}
	if traceID != nil && *traceID != uuid.Nil {
		traceIDs[*traceID] = struct{}{}
	}

	routedRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationIngestion)
	queueingRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationEnqueue)

	err = ingestion.QueueRunPayload(ctx, routedRedisClient, queueingRedisClient, *authInfo, traceIDs, []ingestion.QueuePayload{payload}, true, false)
	if err != nil {
		switch err := err.(type) {
		case *ingestion.IngestionError:
			switch err.Code {
			case ingestion.CodeInvalidInput:
				return &OTELError{Code: http.StatusBadRequest, Message: err.Error()}
			case ingestion.CodeInternal:
				return &OTELError{Code: http.StatusInternalServerError, Message: err.Error()}
			case ingestion.CodeDuplicate:
				return &OTELError{Code: http.StatusConflict, Message: err.Error()}
			case ingestion.CodeTraceLimitExceeded:
				return &OTELError{Code: http.StatusUnprocessableEntity, Message: err.Error()}
			default:
				return &OTELError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("Error queueing runs: %v", err)}
			}
		default:
			return &OTELError{Code: http.StatusInternalServerError, Message: fmt.Sprintf("Error queueing runs: %v", err)}
		}
	}
	if lsredis.IsDualWriteEnabledForTenant(authInfo.TenantID) {
		dualWriteRedisClient := h.RoutedRedisPools.GetRoutedRedisClient(ctx, authInfo.TenantID, lsredis.RedisOperationDualWrite)
		if err := ingestion.QueueRunPayload(ctx, dualWriteRedisClient, queueingRedisClient, *authInfo, traceIDs, []ingestion.QueuePayload{payload}, false, true); err != nil {
			// Log error but continue execution
			oplog := httplog.LogEntry(ctx)
			oplog.Error("Error in dual write to Redis", "error", err)
		}
	}
	return nil
}

func (h *OTELHandler) PostMetrics(w http.ResponseWriter, r *http.Request) {
	oplog := httplog.LogEntry(r.Context())
	// TODO: Use this for auth
	auth.GetAuthInfo(r)

	resourceMetrics, err := getResourceMetrics(r)
	if err != nil {
		status := http.StatusInternalServerError
		if err == errUnsupportedContentType {
			status = http.StatusUnsupportedMediaType
		}
		http.Error(w, err.Error(), status)
		return
	}

	for _, rm := range resourceMetrics {
		for _, sm := range rm.ScopeMetrics {
			for _, metric := range sm.Metrics {
				oplog.Info("Processing metric", "metric", metric)
			}
		}
	}

	metricsResponse := &metricspb.ExportMetricsServiceResponse{}
	if err := writeProtoResponse(w, r, metricsResponse); err != nil {
		status := http.StatusInternalServerError
		if err == errUnsupportedContentType {
			status = http.StatusUnsupportedMediaType
		}
		http.Error(w, err.Error(), status)
		return
	}
}

func decompressBody(r *http.Request) (io.ReadCloser, error) {
	switch strings.ToLower(r.Header.Get("content-encoding")) {
	case "", "identity":
		return r.Body, nil
	case "gzip":
		rc, err := gzip.NewReader(r.Body)
		if err != nil {
			return nil, fmt.Errorf("gzip: %w", err)
		}
		return rc, nil
	default:
		return nil, fmt.Errorf("unsupported content‑encoding %q", r.Header.Get("content-encoding"))
	}
}

func getResourceSpans(r *http.Request) ([]*tracesdkpb.ResourceSpans, error) {
	rc, err := decompressBody(r)
	if err != nil {
		return nil, errUnsupportedContentType
	}
	defer rc.Close()

	body, err := io.ReadAll(rc)
	if err != nil {
		return nil, err
	}

	traceReq := new(tracepb.ExportTraceServiceRequest)

	switch contentType := r.Header.Get("content-type"); contentType {
	case ContentTypeJSON:
		if err := protojson.Unmarshal(body, traceReq); err != nil {
			return nil, err
		}
	case ContentTypeXProto, ContentTypeProto:
		if err := proto.Unmarshal(body, traceReq); err != nil {
			return nil, err
		}
	default:
		return nil, errUnsupportedContentType
	}

	return traceReq.ResourceSpans, nil
}

func getResourceMetrics(r *http.Request) ([]*metricssdkpb.ResourceMetrics, error) {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}

	metricsReq := new(metricspb.ExportMetricsServiceRequest)

	switch ct, _, _ := mime.ParseMediaType(strings.ToLower(r.Header.Get("content-type"))); ct {
	case ContentTypeJSON:
		if err := protojson.Unmarshal(body, metricsReq); err != nil {
			return nil, err
		}
	case ContentTypeXProto, ContentTypeProto:
		if err := proto.Unmarshal(body, metricsReq); err != nil {
			return nil, err
		}
	default:
		return nil, errUnsupportedContentType
	}

	return metricsReq.ResourceMetrics, nil
}

func writeProtoResponse(w http.ResponseWriter, r *http.Request, resp proto.Message) error {
	var (
		b   []byte
		err error
	)

	switch contentType := r.Header.Get("content-type"); contentType {
	case ContentTypeJSON:
		b, err = protojson.Marshal(resp)
	case ContentTypeXProto, ContentTypeProto:
		b, err = proto.Marshal(resp)
	default:
		return errUnsupportedContentType
	}

	if err != nil {
		return err
	}

	if _, err := w.Write(b); err != nil {
		return err
	}

	return nil
}

func writeErrorResponse(w http.ResponseWriter, r *http.Request, oplog *slog.Logger, status int, message string) {
	oplog.Error("Returning OTEL error response", "status", status, "err", message)
	contentType := r.Header.Get("content-type")
	w.Header().Set("Content-Type", contentType)
	w.WriteHeader(status)

	switch contentType {
	case ContentTypeJSON:
		errorResp := struct {
			Error string `json:"error"`
		}{
			Error: message,
		}
		if err := json.NewEncoder(w).Encode(errorResp); err != nil {
			oplog.Error("Failed to write JSON error response", "error", err)
		}
	case ContentTypeXProto, ContentTypeProto:
		// For protobuf, we still return a valid ExportTraceServiceResponse
		// but with an error status code already set above
		resp := &tracepb.ExportTraceServiceResponse{
			PartialSuccess: &tracepb.ExportTracePartialSuccess{
				RejectedSpans: 0,
				ErrorMessage:  message,
			},
		}
		if b, err := proto.Marshal(resp); err == nil {
			if _, err := w.Write(b); err != nil {
				oplog.Error("Failed to write protobuf error response", "error", err)
			}
		}
	default:
		// Fallback to plain text
		if _, err := w.Write([]byte(message)); err != nil {
			oplog.Error("Failed to write plain text error response", "error", err)
		}
	}
}

func hasLangsmithKeys(run *runs.Run) bool {
	return run.DottedOrder != nil && *run.DottedOrder != "" &&
		run.TraceID != nil && *run.TraceID != "" &&
		run.ID != nil && *run.ID != ""
}

type sessionKey struct {
	sessionID   *string
	sessionName *string
}

func fetchOrInitExtrasSlot(extras map[string]map[string]ingestion.ExtraValue, runID string) map[string]ingestion.ExtraValue {
	slot, ok := extras[runID]
	if !ok {
		slot = make(map[string]ingestion.ExtraValue)
		extras[runID] = slot
	}
	return slot
}
