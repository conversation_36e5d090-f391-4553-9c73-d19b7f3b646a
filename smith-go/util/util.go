package util

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net"
	"slices"
	"strings"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/jackc/pgx/v5/pgconn"
)

// https://www.postgresql.org/docs/current/errcodes-appendix.html
var retriablePostgresCodes = []string{
	"55P03",
	"57000",
	"57014",
	"57P01",
	"57P02",
	"57P03",
}

var retriablePostgresPrefixes = []string{
	"08",
	"53",
}

func StringPtr(s string) *string {
	return &s
}

func StringPtrFromNullString(s sql.NullString) *string {
	if !s.Valid {
		return nil
	}
	return &s.String
}

func IntPtr(i int) *int {
	return &i
}

func FloatPtr(f float64) *float64 {
	return &f
}

func DerefInt64(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

func BoolPtr(b bool) *bool {
	return &b
}

func ParseFlexibleTimestamp(str string) (time.Time, error) {
	// Array of possible layouts to try
	layouts := []string{
		// RFC standards
		time.RFC3339,
		time.RFC3339Nano,

		// ISO-like formats
		"2006-01-02T15:04:05.999999",
		"2006-01-02T15:04:05",
		"2006-01-02 15:04:05.999999",
		"2006-01-02 15:04:05",
		"2006-01-02",

		// Compact formats
		"20060102T150405.000000",
		"20060102150405.000000",
		"20060102150405",
		"20060102T150405",
	}

	var firstErr error
	for _, layout := range layouts {
		if len(str) > 15 && strings.Contains(str, "T") {
			if !strings.Contains(str[15:], ".") {
				formatted := fmt.Sprintf("%sT%s.%s",
					str[:8],   // date
					str[9:15], // time
					str[15:],  // subseconds
				)
				t, err := time.Parse(layout, formatted)
				if err == nil {
					return t, nil
				}
				if firstErr == nil {
					firstErr = err
				}
			}
		}

		t, err := time.Parse(layout, str)
		if err == nil {
			return t, nil
		}
		if firstErr == nil {
			firstErr = err
		}

		if !strings.Contains(str, "Z") && !strings.Contains(str, "+") && !strings.Contains(str, "-") {
			t, err := time.Parse(layout, str+"Z")
			if err == nil {
				return t, nil
			}
		}
	}
	return time.Time{}, firstErr
}
func IsRetriableError(err error) bool {
	if err == nil {
		return false
	}
	if errors.Is(err, context.DeadlineExceeded) {
		return true
	}
	var netErr net.Error
	if errors.As(err, &netErr) && netErr.Timeout() {
		return true
	}

	var chErr *clickhouse.OpError
	if errors.As(err, &chErr) {
		return errors.Is(chErr.Err, clickhouse.ErrAcquireConnTimeout)
	}

	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return slices.Contains(retriablePostgresCodes, pgErr.Code) || slices.Contains(retriablePostgresPrefixes, pgErr.Code[:2])
	}

	// Fallback for any other errors
	msg := strings.ToLower(err.Error())
	if strings.Contains(msg, "connection refused") ||
		strings.Contains(msg, "connection reset by peer") ||
		strings.Contains(msg, "broken pipe") ||
		strings.Contains(msg, "i/o timeout") ||
		strings.Contains(msg, "connection has been released back to the pool") ||
		strings.Contains(msg, "connection timed out") ||
		// https://github.com/redis/go-redis/blob/v9.6/error.go#L143-L145
		// https://cloud.google.com/memorystore/docs/redis/about-scaling-instances#instance_scaling_behavior
		strings.Contains(msg, "readonly you can't write against a read only replica") ||
		// https://cloud.google.com/memorystore/docs/redis/memory-management-best-practices#system_memory_usage_ratio_2
		strings.Contains(msg, "oom command not allowed under oom prevention") ||
		strings.Contains(msg, "connection pool timeout") {
		return true
	}

	return false
}
