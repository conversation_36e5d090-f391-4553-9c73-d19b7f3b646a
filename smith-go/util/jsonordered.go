package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"

	orderedmap "github.com/wk8/go-ordered-map/v2"
)

// UnmarshalOrdered parses JSON and returns an ordered map that preserves
// the key order of every object (root and nested).
func UnmarshalOrdered(data []byte) (*orderedmap.OrderedMap[string, any], error) {
	dec := json.NewDecoder(bytes.NewReader(data))

	val, err := readValue(dec)
	if err != nil {
		return nil, err
	}

	if _, err = dec.Token(); err != io.EOF {
		if err == nil {
			return nil, fmt.Errorf("extra data after top-level JSON value")
		}
		return nil, err
	}

	om, ok := val.(*orderedmap.OrderedMap[string, any])
	if !ok {
		return nil, fmt.Errorf("top‑level JSON value is not an object (got %T)", val)
	}
	return om, nil
}

// ---------------------------------------------------------------------------
// internal helpers
// ---------------------------------------------------------------------------

func readValue(dec *json.Decoder) (any, error) {
	tok, err := dec.Token()
	if err != nil {
		return nil, err
	}

	switch d := tok.(type) {
	case json.Delim:
		switch d {
		case '{':
			return readObject(dec)
		case '[':
			return readArray(dec)
		default:
			return nil, fmt.Errorf("unexpected delimiter %q", d)
		}
	default:
		// primitive: string, json.Number, bool, or nil
		return tok, nil
	}
}

func readObject(dec *json.Decoder) (*orderedmap.OrderedMap[string, any], error) {
	om := orderedmap.New[string, any]()

	for dec.More() {
		keyTok, err := dec.Token()
		if err != nil {
			return nil, err
		}
		key, ok := keyTok.(string)
		if !ok {
			return nil, fmt.Errorf("object key is not a string (%T)", keyTok)
		}

		val, err := readValue(dec)
		if err != nil {
			return nil, err
		}
		om.Set(key, val)
	}

	// consume the closing '}'
	if _, err := dec.Token(); err != nil {
		return nil, err
	}
	return om, nil
}

func readArray(dec *json.Decoder) ([]any, error) {
	var arr []any
	for dec.More() {
		val, err := readValue(dec)
		if err != nil {
			return nil, err
		}
		arr = append(arr, val)
	}
	// consume the closing ']'
	if _, err := dec.Token(); err != nil {
		return nil, err
	}
	return arr, nil
}

func MapToOrdered(src map[string]any) *orderedmap.OrderedMap[string, any] {
	if src == nil {
		return nil
	}
	om := orderedmap.New[string, any]()
	for k, v := range src {
		om.Set(k, v)
	}
	return om
}
