package util_test

import (
	"reflect"
	"testing"

	"langchain.com/smith/util"

	orderedmap "github.com/wk8/go-ordered-map/v2"
)

func keys(om *orderedmap.OrderedMap[string, any]) []string {
	var out []string
	for el := om.Oldest(); el != nil; el = el.Next() {
		out = append(out, el.Key)
	}
	return out
}

// Positive paths
func TestUnmarshalOrdered_PreservesKeyOrder(t *testing.T) {
	raw := []byte(`{"alpha":1,"beta":2,"gamma":3}`)

	om, err := util.UnmarshalOrdered(raw)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	want := []string{"alpha", "beta", "gamma"}
	if got := keys(om); !reflect.DeepEqual(got, want) {
		t.Fatalf("key order mismatch: got %v, want %v", got, want)
	}
}

func TestUnmarshalOrdered_NestedOrdering(t *testing.T) {
	raw := []byte(`{
		"outer": {"one":1,"two":2},
		"arr"  : [ {"a":1,"b":2} ]
	}`)

	root, err := util.UnmarshalOrdered(raw)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}

	v, _ := root.Get("outer")
	outer, ok := v.(*orderedmap.OrderedMap[string, any])
	if !ok {
		t.Fatalf("outer is not an ordered map (got %T)", v)
	}
	wantOuter := []string{"one", "two"}
	if got := keys(outer); !reflect.DeepEqual(got, wantOuter) {
		t.Fatalf("nested key order mismatch: got %v, want %v", got, wantOuter)
	}

	v, _ = root.Get("arr")
	arr, ok := v.([]any)
	if !ok || len(arr) != 1 {
		t.Fatalf("arr is not []any with 1 element (got %T len=%d)", v, len(arr))
	}
	elem, ok := arr[0].(*orderedmap.OrderedMap[string, any])
	if !ok {
		t.Fatalf("arr[0] is not an ordered map (got %T)", arr[0])
	}
	wantElem := []string{"a", "b"}
	if got := keys(elem); !reflect.DeepEqual(got, wantElem) {
		t.Fatalf("array-element key order mismatch: got %v, want %v", got, wantElem)
	}
}

func TestMapToOrdered(t *testing.T) {
	src := map[string]any{"foo": 1, "bar": "baz"}
	om := util.MapToOrdered(src)

	if om == nil {
		t.Fatal("MapToOrdered returned nil")
	}
	if om.Len() != len(src) {
		t.Fatalf("expected len=%d, got %d", len(src), om.Len())
	}
	for k, v := range src {
		got, _ := om.Get(k)
		if !reflect.DeepEqual(got, v) {
			t.Fatalf("value mismatch for key %q: got %v, want %v", k, got, v)
		}
	}

	if util.MapToOrdered(nil) != nil {
		t.Fatal("MapToOrdered(nil) should return nil")
	}
}

// Negative paths
func TestUnmarshalOrdered_Errors(t *testing.T) {
	cases := []struct {
		name string
		json string
	}{
		// top-level *must* be an object
		{"nonObjectTopLevel", `[1,2,3]`},
		// malformed (missing closing brace)
		{"unterminatedObject", `{"bad": true`},
		// extra tokens after the first complete value
		{"trailingGarbage", `{"ok":true}  JUNK`},
	}

	for _, tc := range cases {
		tc := tc // capture
		t.Run(tc.name, func(t *testing.T) {
			if _, err := util.UnmarshalOrdered([]byte(tc.json)); err == nil {
				t.Fatalf("expected error for input %q, got nil", tc.name)
			}
		})
	}
}
