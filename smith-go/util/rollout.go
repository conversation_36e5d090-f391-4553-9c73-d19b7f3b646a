package util

import (
	"math/big"

	"github.com/google/uuid"
)

// rolloutForStableUUID determines if a UUID falls within a percentage-based cutoff
func rolloutForStableUUID(stableID uuid.UUID, percent float64) bool {
	idInt := new(big.Int)
	idInt.SetBytes(stableID[:])

	modulo := big.NewInt(100)
	rem := new(big.Int).Mod(idInt, modulo)

	return float64(rem.Int64()) < percent
}

func RolloutByTenant(tenantID uuid.UUID, percent float64) bool {
	/*
		Allows a percentage based rollout where a given tenant will
		always be in the same rollout group.

		This is useful for features where there may be slight differences
		in behavior that we want to test with a small subset of tenants.

		Parameters:
			tenantID: The UUID of the tenant
			percent: The percentage of tenants that should be in the rollout group.
					 Should be between 0 and 100.
	*/
	return rolloutForStableUUID(tenantID, percent)
}

func RolloutByOrg(organizationID uuid.UUID, percent float64) bool {
	/*
		Allows a percentage based rollout where a given organization will
		always be in the same rollout group.

		This is useful for features where there may be slight differences
		in behavior that we want to test with a small subset of organizations.

		Parameters:
			organizationID: The UUID of the organization
			percent: The percentage of organizations that should be in the rollout group.
					 Should be between 0 and 100.
	*/
	return rolloutForStableUUID(organizationID, percent)
}
