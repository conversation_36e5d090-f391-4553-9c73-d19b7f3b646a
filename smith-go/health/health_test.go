package health

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/go-chi/chi/v5"
	"langchain.com/smith/testutil/leak"
)

func TestHealthHandler(t *testing.T) {
	defer leak.VerifyNoLeak(t)

	// Create a new chi router
	r := chi.NewRouter()

	// Mount the endpoint
	r.Get("/health", HealthHandler)
	StartCPUMonitoring()
	defer StopCPUMonitoring()

	// Create a test server using httptest
	ts := httptest.NewServer(r)
	defer ts.Close()

	// test the health endpoint
	resp, err := http.Get(ts.URL + "/health")
	if err != nil {
		t.Fatalf("Failed to make request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, resp.StatusCode)
	}
}
