package health

import (
	"net/http"
	"sync"
	"time"

	"github.com/go-chi/httplog/v2"
	"github.com/prometheus/procfs"
	"langchain.com/smith/config"
)

type CPUMonitor struct {
	mu           sync.Mutex
	samples      []float64
	sampleTimes  []time.Time
	sampleWindow time.Duration
	threshold    float64
}

func NewCPUMonitor(sampleWindow time.Duration, threshold float64) *CPUMonitor {
	return &CPUMonitor{
		samples:      make([]float64, 0),
		sampleTimes:  make([]time.Time, 0),
		sampleWindow: sampleWindow,
		threshold:    threshold,
	}
}

func (c *CPUMonitor) AddSample(cpuUsage float64, observed time.Time) {
	c.mu.Lock()
	defer c.mu.Unlock()

	now := time.Now()
	c.samples = append(c.samples, cpuUsage)
	c.sampleTimes = append(c.sampleTimes, now)

	// Remove samples older than the window
	cutoff := now.Add(-c.sampleWindow)
	var i int
	for i = 0; i < len(c.sampleTimes); i++ {
		if c.sampleTimes[i].After(cutoff) {
			break
		}
	}
	if i > 0 {
		c.samples = c.samples[i:]
		c.sampleTimes = c.sampleTimes[i:]
	}
}

func (c *CPUMonitor) TimeElapsed() time.Duration {
	if len(c.sampleTimes) <= 1 {
		return 0
	}
	c.mu.Lock()
	defer c.mu.Unlock()

	return c.sampleTimes[len(c.sampleTimes)-1].Sub(c.sampleTimes[0])
}

func (c *CPUMonitor) GetAverageCPU() float64 {
	c.mu.Lock()
	defer c.mu.Unlock()

	if len(c.samples) <= 1 {
		return 0
	}

	var sum float64
	for _, sample := range c.samples {
		sum += sample
	}
	startTime := c.sampleTimes[0]
	endTime := c.sampleTimes[len(c.sampleTimes)-1]
	startCPU := c.samples[0]
	endCPU := c.samples[len(c.samples)-1]
	return (endCPU - startCPU) / endTime.Sub(startTime).Seconds()
}

func (c *CPUMonitor) IsHealthy() bool {
	// Healthy if there aren't enough samples
	// or the average CPU usage is less than the threshold
	avg := c.GetAverageCPU()
	return len(c.samples) < (config.Env.CPUHealthIntervalSeconds-2) || avg <= config.Env.CPUHealthThreshold
}

var defaultMonitor = NewCPUMonitor(time.Duration(config.Env.CPUHealthIntervalSeconds)*time.Second, config.Env.CPUHealthThreshold)

// StopCPUMonitoring stops the CPU monitoring goroutine
// by setting a flag that will be checked in the monitoring loop.
var stopCPUMonitoring = make(chan struct{})
var monitoringActive bool
var monitoringMutex sync.Mutex

// StopCPUMonitoring stops the CPU monitoring if it's currently running.
// It's safe to call this function multiple times.
func StopCPUMonitoring() {
	monitoringMutex.Lock()
	defer monitoringMutex.Unlock()

	if monitoringActive {
		stopCPUMonitoring <- struct{}{}
		monitoringActive = false
	}
}

func StartCPUMonitoring() {
	monitoringMutex.Lock()
	if monitoringActive {
		monitoringMutex.Unlock()
		return
	}
	monitoringActive = true
	monitoringMutex.Unlock()

	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-stopCPUMonitoring:
				return
			case <-ticker.C:
				cpu, observed := getCPUTotal()
				defaultMonitor.AddSample(cpu, observed)
			}
		}
	}()
}

func HealthHandler(w http.ResponseWriter, r *http.Request) {
	cpu := defaultMonitor.GetAverageCPU()
	if !defaultMonitor.IsHealthy() {
		log := httplog.LogEntry(r.Context())
		log.Error("Service is under high CPU load", "cpu", cpu, "threshold", config.Env.CPUHealthThreshold)
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(`Service unavailable`))
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`OK`))
}

func getCPUTotal() (total float64, observed time.Time) {
	// returns total CPU time and observation time
	total = 0
	observed = time.Now()
	fs, err := procfs.Self()

	if err != nil {
		return 0, observed
	}
	stat, err := fs.Stat()
	if err != nil {
		return 0, observed
	}
	total = stat.CPUTime() + ((float64(stat.CUTime) + float64(stat.CSTime)) / 100)
	return
}
